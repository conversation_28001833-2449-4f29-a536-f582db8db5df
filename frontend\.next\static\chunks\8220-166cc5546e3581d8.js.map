{"version": 3, "file": "static/chunks/8220-166cc5546e3581d8.js", "mappings": "4HAmBO,SAASA,EAAQC,CAAI,EAC1B,MAAO,CAACC,CAAAA,EAAAA,EAAAA,CAAAA,CAAMA,CAACD,EACjB,kDCyBO,SAASE,EAAiBF,CAAI,CAAEG,CAAQ,CAAEC,CAAO,EACtD,IAAMC,EAAO,CAACJ,CAAAA,EAAAA,EAAAA,CAAAA,CAAMA,CAACD,QAAMI,EAAAA,KAAAA,EAAAA,EAASE,EAAE,EAChC,CAACC,EAAWC,EADSJ,CACE,CAC3B,CAACH,CAAAA,EAAAA,EAAAA,CAAAA,CAAMA,CAACE,EAASM,KAAK,OAAEL,EAAAA,KAAAA,EAAAA,EAASE,EAAE,EACnC,CAACL,CAAAA,EAAAA,CADuBG,CACvBH,CAAAA,CAAMA,CAACE,EAASO,GAAG,CAAEN,QAAAA,KAAAA,EAAAA,EAASE,EAAE,CAAXF,CACvB,CAACO,IAAI,CAAC,CAACC,EAAGC,IAAMD,EAAIC,GAErB,OAAOR,GAAQE,GAAaF,GAAQG,CACtC,kDClCO,SAASM,EAASd,CAAI,CAAEe,CAAa,EAC1C,MAAO,CAACd,CAAAA,EAAAA,EAAAA,CAAAA,CAAMA,CAACD,GAAQ,CAACC,CAAAA,EAAAA,EAAAA,CAAAA,CAAMA,CAACc,EACjC,mCCtBwP,aAAgB,wCAAwC,IAAI,kCAApT,KAAc,aAAa,+CAA+C,gDAAgD,eAAe,QAAQ,IAAI,0CAA0C,yCAAyC,UAA4E,wBAAmD,SAAS,iDCmBzW,SAASC,EAAWhB,CAAI,EAC7B,MAAOC,CDpBsX,ECoBtXA,EAAAA,CAAAA,CAAMA,CAACD,GAAMgB,UAAU,EAChC,8BDrBmY,oBEwB5X,SAASC,EAAQjB,CAAI,CAAEI,CAAO,EACnC,MAAOH,CAAAA,EAAAA,EAAAA,CAAAA,CAAMA,CAACD,QAAMI,EAAAA,KAAAA,EAAAA,EAASE,EAAE,EAAEW,KAAbb,EAAoB,EAC1C,6DCGO,SAASc,EAASlB,CAAI,CAAEmB,CAAM,CAAEf,CAAO,EAC5C,MAAOgB,CAAAA,EAAAA,EAAAA,CAAAA,CAAeA,CAACpB,EAAMmB,EAASE,EAAAA,EAAkBA,CAAEjB,EAC5D,2IE/BO,SAASkB,EAAgBC,CAAM,CAAEC,CAAY,EAElD,IAAMC,EAASC,KAAKC,GAAG,CAACJ,GAAQK,QAAQ,GAAGC,QAAQ,CAACL,EAAc,KAClE,MAAOM,CAFMP,EAAS,EAAI,IAAM,IAElBE,CAChB,CCWO,IAAMM,EAAkB,CAE7BC,EAAEhC,CAAI,CAAEiC,CAAK,EAUX,IAAMC,EAAalC,EAAKmC,WAAW,GAE7BC,EAAOF,EAAa,EAAIA,EAAa,EAAIA,EAC/C,OAAOZ,EAAgBW,SAAiBG,EAAO,EAAzBd,EAA+Bc,EAAMH,EAAMI,MAAM,CACzE,EAGAC,EAAEtC,CAAI,CAAEiC,CAAK,EACX,IAAMM,EAAQvC,EAAKwC,QAAQ,GAC3B,MAAiB,MAAVP,EAAgBQ,OAAOF,EAAQ,GAAKjB,EAAgBiB,EAAQ,EAAG,EACxE,IAGAG,CAAE1C,EAJ0DsB,EAItD,EACGA,CADI,CACYtB,EAAKiB,OAAO,GAAIgB,CAAjBX,CAAuBe,MAAM,EAIrDzB,EAAEZ,CAAI,CAAEiC,CAAK,EACX,IAAMU,EAAqB3C,EAAK4C,QAAQ,GAAK,IAAM,EAAI,KAAO,KAE9D,OAAQX,GACN,IAAK,IACL,IAAK,KACH,OAAOU,EAAmBE,WAAW,EACvC,KAAK,MACH,OAAOF,CACT,KAAK,QACH,OAAOA,CAAkB,CAAC,EAAE,KACzB,IAEH,MAA8B,OAAvBA,EAA8B,OAAS,MAClD,CACF,IAGAG,CAAE9C,EAAMiC,EAAF,EACGX,CADI,CACYtB,EAAK4C,QAAQ,GAAdtB,IAAyB,GAAIW,EAAMI,MAAM,IAIjEU,CAAE/C,EAAMiC,EAAF,EACGX,CADI,CACYtB,EAAK4C,QAAQ,GAAdtB,EAAwBe,MAAM,IAItDW,CAAEhD,EAAMiC,EAAF,EACGX,CADI,CACYtB,EAAKiD,UAAU,CAAhB3B,EAAoBW,EAAMI,MAAM,EAIxDa,GAAElD,EAAMiC,EAAF,EACGX,CADI,CACYtB,EAAKgB,UAAU,CAAhBM,EAAoBW,EAAMI,MAAM,EAIxDc,EAAEnD,CAAI,CAAEiC,CAAK,EACX,IAAMmB,EAAiBnB,EAAMI,MAAM,CAKnC,OAAOf,EAHmBI,KAAK2B,KAAK,CAClCC,EAFwBC,CAIHC,cAJkB,GAExB9B,KAAK+B,GAAG,CAAC,GAAIL,EAAiB,IAELnB,EAAMI,MAAM,CACxD,CACF,EAAE,EClFoB,CAGpBqB,SAAU,WACVC,KAAM,OACNC,QAAS,UACTC,UAAW,YACXC,QAAS,UACTC,MAAO,OACT,EAgDaC,EAAa,CAExBC,EAAG,SAAUjE,CAAI,CAAEiC,CAAK,CAAEiC,CAAQ,EAChC,IAAMC,IAAMnE,EAAKmC,WAAW,IAAK,EACjC,EADqC,IAAI,CACjCF,GAEN,IAAK,IACL,IAAK,KACL,IAAK,MACH,OAAOiC,EAASC,GAAG,CAACA,EAAK,CAAEC,MAAO,aAAc,EAElD,KAAK,QACH,OAAOF,EAASC,GAAG,CAACA,EAAK,CAAEC,MAAO,QAAS,EAE7C,KAAK,IAEH,OAAOF,EAASC,GAAG,CAACA,EAAK,CAAEC,MAAO,MAAO,EAC7C,CACF,EAGApC,EAAG,SAAUhC,CAAI,CAAEiC,CAAK,CAAEiC,CAAQ,EAEhC,GAAc,OAAVjC,EAAgB,CAClB,IAAMC,EAAalC,EAAKmC,WAAW,GAGnC,OAAO+B,EAASG,aAAa,CAACjC,EADJ,EAAIF,EAAa,EAAIA,EACX,CAAEoC,KAAM,MAAO,EACrD,CAEA,OAAOvC,EAAgBC,CAAC,CAAChC,EAAMiC,EACjC,EAGAsC,EAAG,GAJqBxC,MAIX/B,CAAI,CAAEiC,CAAK,CAAEiC,CAAQ,CAAE9D,CAAO,EACzC,IAAMoE,EAAiBC,CAAAA,EAAAA,EAAAA,CAAAA,CAAWA,CAACzE,EAAMI,GAEnCsE,EAAWF,EAAiB,EAAIA,EAAiB,EAAIA,QAG3D,MAAoB,CAAhBvC,EAEKX,EADcoD,EAAW,IACK,GAIzB,IAJUpD,CAACqD,CAIL,CAAhB1C,EACKiC,EAASG,aAAa,CAACK,EAAU,CAAEJ,KAAM,MAAO,GAIlDhD,EAAgBoD,EAAUzC,EAAMI,MAAM,CAC/C,EADwBf,EAIrB,SAAUtB,CAAI,CAAEiC,CAAK,EAItB,OAAOX,EAHasD,CAAAA,EAAAA,EAAAA,CAAAA,CAAcA,CAAC5E,GAGCiC,EAAdX,CAACuD,KAAyB,CAClD,EAWAC,EAAG,SAAU9E,CAAI,CAAEiC,CAAK,EAEtB,OAAOX,EADMtB,EAAKmC,WAAW,CACNC,EAAMH,EAAMI,MAAM,CAC3C,EAGA0C,EAAG,SAAU/E,CAAI,CAAEiC,CAAK,CAAEiC,CAAQ,EAChC,IAAMc,EAAUtD,KAAKuD,IAAI,CAAC,CAACjF,EAAKwC,QAAQ,IAAK,EAAK,GAClD,OAAQP,GAEN,IAAK,IACH,OAAOQ,OAAOuC,EAEhB,KAAK,KACH,OAAO1D,EAAgB0D,EAAS,EAElC,KAAK,IAFmB1D,CAGtB,OAAO4C,EAASG,aAAa,CAACW,EAAS,CAAEV,KAAM,SAAU,EAE3D,KAAK,MACH,OAAOJ,EAASc,OAAO,CAACA,EAAS,CAC/BZ,MAAO,cACPc,QAAS,YACX,EAEF,KAAK,QACH,OAAOhB,EAASc,OAAO,CAACA,EAAS,CAC/BZ,MAAO,SACPc,QAAS,YACX,EAEF,KAAK,IAEH,OAAOhB,EAASc,OAAO,CAACA,EAAS,CAC/BZ,MAAO,OACPc,QAAS,YACX,EACJ,CACF,EAGAC,EAAG,SAAUnF,CAAI,CAAEiC,CAAK,CAAEiC,CAAQ,EAChC,IAAMc,EAAUtD,KAAKuD,IAAI,CAAC,CAACjF,EAAKwC,QAAQ,IAAK,EAAK,GAClD,OAAQP,GAEN,IAAK,IACH,OAAOQ,OAAOuC,EAEhB,KAAK,KACH,OAAO1D,EAAgB0D,EAAS,EAElC,KAAK,IAFmB1D,CAGtB,OAAO4C,EAASG,aAAa,CAACW,EAAS,CAAEV,KAAM,SAAU,EAE3D,KAAK,MACH,OAAOJ,EAASc,OAAO,CAACA,EAAS,CAC/BZ,MAAO,cACPc,QAAS,YACX,EAEF,KAAK,QACH,OAAOhB,EAASc,OAAO,CAACA,EAAS,CAC/BZ,MAAO,SACPc,QAAS,YACX,EAEF,KAAK,IAEH,OAAOhB,EAASc,OAAO,CAACA,EAAS,CAC/BZ,MAAO,OACPc,QAAS,YACX,EACJ,CACF,EAGA5C,EAAG,SAAUtC,CAAI,CAAEiC,CAAK,CAAEiC,CAAQ,EAChC,IAAM3B,EAAQvC,EAAKwC,QAAQ,GAC3B,OAAQP,GACN,IAAK,IACL,IAAK,KACH,OAAOF,EAAgBO,CAAC,CAACtC,EAAMiC,EAEjC,KAAK,EAFmBF,GAGtB,OAAOmC,EAASG,aAAa,CAAC9B,EAAQ,EAAG,CAAE+B,KAAM,OAAQ,EAE3D,KAAK,MACH,OAAOJ,EAAS3B,KAAK,CAACA,EAAO,CAC3B6B,MAAO,cACPc,QAAS,YACX,EAEF,KAAK,QACH,OAAOhB,EAAS3B,KAAK,CAACA,EAAO,CAC3B6B,MAAO,SACPc,QAAS,YACX,EAEF,KAAK,IAEH,OAAOhB,EAAS3B,KAAK,CAACA,EAAO,CAAE6B,MAAO,OAAQc,QAAS,YAAa,EACxE,CACF,EAGAE,EAAG,SAAUpF,CAAI,CAAEiC,CAAK,CAAEiC,CAAQ,EAChC,IAAM3B,EAAQvC,EAAKwC,QAAQ,GAC3B,OAAQP,GAEN,IAAK,IACH,OAAOQ,OAAOF,EAAQ,EAExB,KAAK,KACH,OAAOjB,EAAgBiB,EAAQ,EAAG,EAEpC,KAAK,EAFmBjB,GAGtB,OAAO4C,EAASG,aAAa,CAAC9B,EAAQ,EAAG,CAAE+B,KAAM,OAAQ,EAE3D,KAAK,MACH,OAAOJ,EAAS3B,KAAK,CAACA,EAAO,CAC3B6B,MAAO,cACPc,QAAS,YACX,EAEF,KAAK,QACH,OAAOhB,EAAS3B,KAAK,CAACA,EAAO,CAC3B6B,MAAO,SACPc,QAAS,YACX,EAEF,KAAK,IAEH,OAAOhB,EAAS3B,KAAK,CAACA,EAAO,CAAE6B,MAAO,OAAQc,QAAS,YAAa,EACxE,CACF,EAGAG,EAAG,SAAUrF,CAAI,CAAEiC,CAAK,CAAEiC,CAAQ,CAAE9D,CAAO,EACzC,IAAMkF,EAAOC,CAAAA,EAAAA,EAAAA,CAAAA,CAAOA,CAACvF,EAAMI,SAE3B,MAAoB,CAAhB6B,EACKiC,EAASG,aAAa,CAACiB,EAAM,CAAEhB,KAAM,MAAO,GAG9ChD,EAAgBgE,EAAMrD,EAAMI,MAAM,CAC3C,EADwBf,EAIrB,SAAUtB,CAAI,CAAEiC,CAAK,CAAEiC,CAAQ,EAChC,IAAMsB,EAAUC,CAAAA,EAAAA,EAAAA,CAAAA,CAAUA,CAACzF,SAE3B,MAAoB,GACXkE,EAASG,aAAa,CAACmB,EAAS,CAAElB,KAAM,MAAO,GAGjDhD,EAAgBkE,EAASvD,EAAMI,MAAM,CAC9C,EADwBf,EAIrB,SAAUtB,CAAI,CAAEiC,CAAK,CAAEiC,CAAQ,QAChC,MAAoB,CAAhBjC,EACKiC,EAASG,aAAa,CAACrE,EAAKiB,OAAO,GAAI,CAAEqD,KAAM,MAAO,GAGxDvC,EAAgBW,CAAC,CAAC1C,EAAMiC,EACjC,EAGAyD,EAAG,GAJqB3D,MAIX/B,CAAI,CAAEiC,CAAK,CAAEiC,CAAQ,EAChC,IAAMyB,EHxRH,SAAsB3F,CAAI,CAAEI,CAAO,EACxC,IAAMwF,EAAQ3F,CAAAA,EAAAA,EAAAA,CAAAA,CAAMA,CAACD,EAAMI,IAAAA,CAAAA,GAG3B,CAH2BA,KAETyF,CACXF,EAH6BrF,EAAE,CACzBwF,CAAwBA,CAACF,EADXxF,CACkB2F,EAAAA,EAAAA,CAAAA,CAAWA,CAACH,IAChC,CAE3B,EGmRmC5F,SAE/B,MAAoB,CAAhBiC,EACKiC,EAASG,aAAa,CAACsB,EAAW,CAAErB,KAAM,WAAY,GAGxDhD,EAAgBqE,EAAW1D,EAAMI,MAAM,CAChD,EAGA2D,EAAG,SAAUhG,CAAI,CAAEiC,CAAK,CAAEiC,CAAQ,EAChC,IAAM+B,EAAYjG,EAAKkG,MAAM,GAC7B,OAAQjE,GAEN,IAAK,IACL,IAAK,KACL,IAAK,MACH,OAAOiC,EAASiC,GAAG,CAACF,EAAW,CAC7B7B,MAAO,cACPc,QAAS,YACX,EAEF,KAAK,QACH,OAAOhB,EAASiC,GAAG,CAACF,EAAW,CAC7B7B,MAAO,SACPc,QAAS,YACX,EAEF,KAAK,SACH,OAAOhB,EAASiC,GAAG,CAACF,EAAW,CAC7B7B,MAAO,QACPc,QAAS,YACX,EAEF,KAAK,IAEH,OAAOhB,EAASiC,GAAG,CAACF,EAAW,CAC7B7B,MAAO,OACPc,QAAS,YACX,EACJ,CACF,EAGAkB,EAAG,SAAUpG,CAAI,CAAEiC,CAAK,CAAEiC,CAAQ,CAAE9D,CAAO,EACzC,IAAM6F,EAAYjG,EAAKkG,MAAM,GACvBG,EAAiB,CAACJ,EAAY7F,EAAQkG,YAAY,EAAG,EAAK,GAAK,EACrE,OAAQrE,GAEN,IAAK,IACH,OAAOQ,OAAO4D,EAEhB,KAAK,KACH,OAAO/E,EAAgB+E,EAAgB,EAEzC,KAAK,IAFmB/E,CAGtB,OAAO4C,EAASG,aAAa,CAACgC,EAAgB,CAAE/B,KAAM,KAAM,EAC9D,KAAK,MACH,OAAOJ,EAASiC,GAAG,CAACF,EAAW,CAC7B7B,MAAO,cACPc,QAAS,YACX,EAEF,KAAK,QACH,OAAOhB,EAASiC,GAAG,CAACF,EAAW,CAC7B7B,MAAO,SACPc,QAAS,YACX,EAEF,KAAK,SACH,OAAOhB,EAASiC,GAAG,CAACF,EAAW,CAC7B7B,MAAO,QACPc,QAAS,YACX,EAEF,KAAK,IAEH,OAAOhB,EAASiC,GAAG,CAACF,EAAW,CAC7B7B,MAAO,OACPc,QAAS,YACX,EACJ,CACF,EAGAqB,EAAG,SAAUvG,CAAI,CAAEiC,CAAK,CAAEiC,CAAQ,CAAE9D,CAAO,EACzC,IAAM6F,EAAYjG,EAAKkG,MAAM,GACvBG,EAAiB,CAACJ,EAAY7F,EAAQkG,YAAY,CAAG,GAAK,GAAK,EACrE,OAAQrE,GAEN,IAAK,IACH,OAAOQ,OAAO4D,EAEhB,KAAK,KACH,OAAO/E,EAAgB+E,EAAgBpE,EAAMI,MAAM,CAErD,EAFwBf,GAEnB,KACH,OAAO4C,EAASG,aAAa,CAACgC,EAAgB,CAAE/B,KAAM,KAAM,EAC9D,KAAK,MACH,OAAOJ,EAASiC,GAAG,CAACF,EAAW,CAC7B7B,MAAO,cACPc,QAAS,YACX,EAEF,KAAK,QACH,OAAOhB,EAASiC,GAAG,CAACF,EAAW,CAC7B7B,MAAO,SACPc,QAAS,YACX,EAEF,KAAK,SACH,OAAOhB,EAASiC,GAAG,CAACF,EAAW,CAC7B7B,MAAO,QACPc,QAAS,YACX,EAEF,KAAK,IAEH,OAAOhB,EAASiC,GAAG,CAACF,EAAW,CAC7B7B,MAAO,OACPc,QAAS,YACX,EACJ,CACF,EAGAsB,EAAG,SAAUxG,CAAI,CAAEiC,CAAK,CAAEiC,CAAQ,EAChC,IAAM+B,EAAYjG,EAAKkG,MAAM,GACvBO,EAA6B,IAAdR,EAAkB,EAAIA,EAC3C,OAAQhE,GAEN,IAAK,IACH,OAAOQ,OAAOgE,EAEhB,KAAK,KACH,OAAOnF,EAAgBmF,EAAcxE,EAAMI,MAAM,CAEnD,EAFwBf,GAEnB,KACH,OAAO4C,EAASG,aAAa,CAACoC,EAAc,CAAEnC,KAAM,KAAM,EAE5D,KAAK,MACH,OAAOJ,EAASiC,GAAG,CAACF,EAAW,CAC7B7B,MAAO,cACPc,QAAS,YACX,EAEF,KAAK,QACH,OAAOhB,EAASiC,GAAG,CAACF,EAAW,CAC7B7B,MAAO,SACPc,QAAS,YACX,EAEF,KAAK,SACH,OAAOhB,EAASiC,GAAG,CAACF,EAAW,CAC7B7B,MAAO,QACPc,QAAS,YACX,EAEF,KAAK,IAEH,OAAOhB,EAASiC,GAAG,CAACF,EAAW,CAC7B7B,MAAO,OACPc,QAAS,YACX,EACJ,CACF,EAGAtE,EAAG,SAAUZ,CAAI,CAAEiC,CAAK,CAAEiC,CAAQ,EAEhC,IAAMvB,EADQ3C,EAAK4C,QAAQ,GACQ,IAAM,EAAI,KAAO,KAEpD,OAAQX,GACN,IAAK,IACL,IAAK,KACH,OAAOiC,EAASwC,SAAS,CAAC/D,EAAoB,CAC5CyB,MAAO,cACPc,QAAS,YACX,EACF,KAAK,MACH,OAAOhB,EACJwC,SAAS,CAAC/D,EAAoB,CAC7ByB,MAAO,cACPc,QAAS,YACX,GACCyB,WAAW,EAChB,KAAK,QACH,OAAOzC,EAASwC,SAAS,CAAC/D,EAAoB,CAC5CyB,MAAO,SACPc,QAAS,YACX,EACF,KAAK,IAEH,OAAOhB,EAASwC,SAAS,CAAC/D,EAAoB,CAC5CyB,MAAO,OACPc,QAAS,YACX,EACJ,CACF,EAGArE,EAAG,SAAUb,CAAI,CAAEiC,CAAK,CAAEiC,CAAQ,EAChC,IACIvB,EADEiE,EAAQ5G,EAAK4C,QAAQ,GAU3B,OAPED,EADY,IAAI,GACKkE,EAAclD,IAAI,CAC9BiD,GAAa,GACDC,EAAcnD,QAAQ,CAEtBkD,EAAQ,IAAM,EAAI,KAAO,KAGxC3E,GACN,IAAK,IACL,IAAK,KACH,OAAOiC,EAASwC,SAAS,CAAC/D,EAAoB,CAC5CyB,MAAO,cACPc,QAAS,YACX,EACF,KAAK,MACH,OAAOhB,EACJwC,SAAS,CAAC/D,EAAoB,CAC7ByB,MAAO,cACPc,QAAS,YACX,GACCyB,WAAW,EAChB,KAAK,QACH,OAAOzC,EAASwC,SAAS,CAAC/D,EAAoB,CAC5CyB,MAAO,SACPc,QAAS,YACX,EACF,KAAK,IAEH,OAAOhB,EAASwC,SAAS,CAAC/D,EAAoB,CAC5CyB,MAAO,OACPc,QAAS,YACX,EACJ,CACF,EAGA4B,EAAG,SAAU9G,CAAI,CAAEiC,CAAK,CAAEiC,CAAQ,EAChC,IACIvB,EADEiE,EAAQ5G,EAAK4C,QAAQ,GAY3B,OATED,EADEiE,GAAS,GACUC,CADN,CACoB/C,OAAO,CACjC8C,GAAS,GACGC,CADC,CACahD,SAAS,CACnC+C,GAAS,EACGC,CADA,CACcjD,OAAO,CAErBiD,EAAc9C,KAAK,CAGlC9B,GACN,IAAK,IACL,IAAK,KACL,IAAK,MACH,OAAOiC,EAASwC,SAAS,CAAC/D,EAAoB,CAC5CyB,MAAO,cACPc,QAAS,YACX,EACF,KAAK,QACH,OAAOhB,EAASwC,SAAS,CAAC/D,EAAoB,CAC5CyB,MAAO,SACPc,QAAS,YACX,EACF,KAAK,IAEH,OAAOhB,EAASwC,SAAS,CAAC/D,EAAoB,CAC5CyB,MAAO,OACPc,QAAS,YACX,EACJ,CACF,EAGApC,EAAG,SAAU9C,CAAI,CAAEiC,CAAK,CAAEiC,CAAQ,EAChC,GAAIjC,SAAgB,CAClB,IAAI2E,EAAQ5G,EAAK4C,QAAQ,GAAK,GAE9B,OADc,IAAVgE,IAAaA,EAAQ,IAClB1C,EAASG,aAAa,CAACuC,EAAO,CAAEtC,KAAM,MAAO,EACtD,CAEA,OAAOvC,EAAgBe,CAAC,CAAC9C,EAAMiC,EACjC,EAGAc,EAAG,GAJqBhB,MAIX/B,CAAI,CAAEiC,CAAK,CAAEiC,CAAQ,QAChC,MAAoB,GACXA,EAASG,aAAa,CAACrE,EAAK4C,QAAQ,GAAI,CAAE0B,KAAM,MAAO,GAGzDvC,EAAgBgB,CAAC,CAAC/C,EAAMiC,EACjC,EAGA8E,EAAG,GAJqBhF,MAIX/B,CAAI,CAAEiC,CAAK,CAAEiC,CAAQ,EAChC,IAAM0C,EAAQ5G,EAAK4C,QAAQ,GAAK,SAEhC,MAAoB,CAAhBX,EACKiC,EAASG,aAAa,CAACuC,EAAO,CAAEtC,KAAM,MAAO,GAG/ChD,EAAgBsF,EAAO3E,EAAMI,MAAM,CAC5C,EADwBf,EAIrB,SAAUtB,CAAI,CAAEiC,CAAK,CAAEiC,CAAQ,EAChC,IAAI0C,EAAQ5G,EAAK4C,QAAQ,SAGzB,CAFc,IAAVgE,IAAaA,EAAQ,IAEX,MAAM,CAAhB3E,GACKiC,EAASG,aAAa,CAACuC,EAAO,CAAEtC,KAAM,MAAO,GAG/ChD,EAAgBsF,EAAO3E,EAAMI,MAAM,CAC5C,EAGAW,EAAG,SAAUhD,CAAI,CAAEiC,CAAK,CAAEiC,CAAQ,QAClB,MAAM,CAAhBjC,EACKiC,EAASG,aAAa,CAACrE,EAAKiD,UAAU,GAAI,CAAEqB,KAAM,QAAS,GAG7DvC,EAAgBiB,CAAC,CAAChD,EAAMiC,EACjC,EAGAiB,EAAG,GAJqBnB,MAIX/B,CAAI,CAAEiC,CAAK,CAAEiC,CAAQ,QAChC,MAAoB,CAAhBjC,EACKiC,EAASG,aAAa,CAACrE,EAAKgB,UAAU,GAAI,CAAEsD,KAAM,QAAS,GAG7DvC,EAAgBmB,CAAC,CAAClD,EAAMiC,EACjC,EAGAkB,EAAG,GAJqBpB,MAIX/B,CAAI,CAAEiC,CAAK,EACtB,OAAOF,EAAgBoB,CAAC,CAACnD,EAAMiC,EACjC,EAGA+E,EAAG,GAJqBjF,MAIX/B,CAAI,CAAEiC,CAAK,CAAEgF,CAAS,EACjC,IAAMC,EAAiBlH,EAAKmH,iBAAiB,GAE7C,GAAuB,GAAG,CAAtBD,EACF,MAAO,IAGT,OAAQjF,GAEN,IAAK,IACH,OAAOmF,EAAkCF,EAK3C,KAAK,OACL,IAAK,KACH,OAAOG,EAAeH,EAKxB,KAAK,IAGH,OAAOG,EAAeH,EAAgB,IAC1C,CACF,EAGAI,EAAG,SAAUtH,CAAI,CAAEiC,CAAK,CAAEgF,CAAS,EACjC,IAAMC,EAAiBlH,EAAKmH,iBAAiB,GAE7C,OAAQlF,GAEN,IAAK,IACH,OAAOmF,EAAkCF,EAK3C,KAAK,OACL,IAAK,KACH,OAAOG,EAAeH,EAKxB,KAAK,IAGH,OAAOG,EAAeH,EAAgB,IAC1C,CACF,EAGAK,EAAG,SAAUvH,CAAI,CAAEiC,CAAK,CAAEgF,CAAS,EACjC,IAAMC,EAAiBlH,EAAKmH,iBAAiB,GAE7C,OAAQlF,GAEN,IAAK,IACL,IAAK,KACL,IAAK,MACH,MAAO,MAAQuF,EAAoBN,EAAgB,IAErD,KAAK,IAEH,MAAO,MAAQG,EAAeH,EAAgB,IAClD,CACF,EAGAO,EAAG,SAAUzH,CAAI,CAAEiC,CAAK,CAAEgF,CAAS,EACjC,IAAMC,EAAiBlH,EAAKmH,iBAAiB,GAE7C,OAAQlF,GAEN,IAAK,IACL,IAAK,KACL,IAAK,MACH,MAAO,MAAQuF,EAAoBN,EAAgB,IAErD,KAAK,IAEH,MAAO,MAAQG,EAAeH,EAAgB,IAClD,CACF,EAGAQ,EAAG,SAAU1H,CAAI,CAAEiC,CAAK,CAAEgF,CAAS,EAEjC,OAAO3F,EADWI,KAAK2B,KAAK,CAAC,EAAQ,CACdsE,IAAW1F,EAAMI,MAAM,CAChD,EAGAuF,EAAG,SAAU5H,CAAI,CAAEiC,CAAK,CAAEgF,CAAS,EACjC,OAAO3F,EAAgB,CAACtB,EAAMiC,EAAMI,MAAM,CAC5C,CACF,EAAE,SAEOmF,EAAoBK,CAAM,MAAEC,EAAAA,UAAAA,MAAAA,CAAAA,GAAAA,KAAAA,IAAAA,SAAAA,CAAAA,EAAAA,CAAAA,SAAAA,CAAAA,EAAAA,CAAY,GACzChG,EAAO+F,EAAS,EAAI,IAAM,IAC1BE,EAAYrG,KAAKC,GAAG,CAACkG,GACrBjB,EAAQlF,KAAK2B,KAAK,CAAC0E,EAAY,IAC/BC,EAAUD,EAAY,UACZ,GAAG,GACVjG,EAAOW,OAAOmE,GAEhB9E,EAAOW,OAAOmE,GAASkB,EAAYxG,EAAgB0G,EAAS,EACrE,CAEA,QAH2D1G,CAGlD8F,EAAkCS,CAAM,CAAEC,CAAS,SAC1D,EAAa,IAAO,EAEXhG,CAFc,EACC,EAAI,IAAM,KAClBR,EAAgBI,KAAKC,GAAG,CAACkG,GAAU,CAApBvG,EAAwB,GAEhD+F,EAAeQ,EAAQC,EAChC,CAEA,SAAST,EAAeQ,CAAM,MAAEC,EAAAA,UAAAA,MAAAA,CAAAA,GAAAA,KAAAA,IAAAA,SAAAA,CAAAA,EAAAA,CAAAA,SAAAA,CAAAA,EAAAA,CAAY,GAEpCC,EAAYrG,KAAKC,GAAG,CAACkG,GAG3B,MAAO/F,CAJM+F,EAAS,EAAI,EAIZjB,EAJkB,KAElBtF,EAAgBI,KAAK2B,KAAK,CAAC0E,EAAY,IAAK,GAEpCD,EADNxG,EAAgByG,EAAY,GAAI,EAElD,CADoCC,KADH1G,gCC1uBjC,IAAM2G,EACJ,wDAIIC,EAA6B,oCAE7BC,EAAsB,eACtBC,EAAoB,MACpBC,EAAgC,WAoS/B,SAASC,EAAOtI,CAAI,CAAEuI,CAAS,CAAEnI,CAAO,MAM3CA,EAAAA,EAEAoI,EAAAA,EAKApI,EAAAA,EAEAoI,EAAAA,EAbapI,EAAAA,EAGbA,EAAAA,EAAAA,EAAAA,EAOAA,EAAAA,EAAAA,EAAAA,EAXF,IAAMoI,EAAiBC,CAAAA,EAAAA,EAAAA,CAAAA,CAAiBA,GAClCC,EAAStI,OAAAA,EAAAA,OAAAA,QAAAA,EAAAA,KAAAA,EAAAA,EAASsI,MAAAA,EAATtI,CAAAA,CAAmBoI,EAAeE,MAAAA,EAAlCtI,EAA4CuI,EAAAA,CAAaA,CAElEC,EACJxI,IAHaA,GAGbA,EAAAA,OAAAA,EAAAA,OAAAA,EAAAA,OAAAA,QAAAA,EAAAA,KAAAA,EAAAA,EAASwI,SAATxI,YAASwI,EAATxI,QACAA,GAAAA,CADAA,MACAA,EAAAA,EAASsI,MAATtI,GAAAA,MAAAA,GAAAA,EAAAA,OAAiBA,EAAjBA,KAAAA,EAAAA,EAA0BwI,GAA1BxI,kBAA0BwI,EAD1BxI,EAEAoI,EAAeI,QAFfxI,aAEewI,EAFfxI,SAGAoI,EAAAA,CAHApI,CAGesI,MAAAA,GAAfF,OAAAA,EAAAA,EAAAA,OAAuBpI,EAAvBoI,KAAAA,EAAAA,EAAgCI,GAAhCJ,kBAAgCI,EAHhCxI,EAIA,EAEIkG,EACJlG,MAPAA,CAOAA,EAAAA,OAAAA,EAAAA,MAAAA,GAAAA,OAAAA,QAAAA,EAAAA,KAAAA,EAAAA,EAASkG,SAATlG,GAAqB,EAArBA,QACAA,GAAAA,CADAA,MACAA,EAAAA,EAASsI,MAATtI,GAAAA,OAAAA,EAAAA,EAAAA,OAAiBA,EAAjBA,KAAAA,EAAAA,EAA0BkG,GAA1BlG,SAA0BkG,EAD1BlG,EAEAoI,EAAelC,QAFflG,IAEekG,EAFflG,SAGAoI,EAAAA,CAHApI,CAGesI,MAAAA,GAAfF,OAAAA,EAAAA,EAAAA,OAAuBpI,EAAvBoI,KAAAA,EAAAA,EAAgClC,GAAhCkC,SAAgClC,EAHhClG,EAIA,EAEIyI,EAAe5I,CAAAA,EAAAA,EAAAA,CAAAA,CAAMA,CAACD,QAAMI,EAAAA,KAAAA,EAAAA,EAASE,EAAE,EAE7C,GAAI,CAACwI,CAF6B1I,EAE7B0I,EAAAA,CAAAA,CAAOA,CAACD,GACX,MAAUE,MADgB,KACL,sBAGvB,IAAIC,EAAQT,EACTU,KAAK,CAACf,GACNgB,GAAG,CAAC,IACH,IAAMC,EAAiBC,CAAS,CAAC,EAAE,OACnC,MAAID,GAA6C,KAAK,CAAxBA,EAErBE,GADeC,EAAAA,CAAc,CAACH,EAAAA,EAChBC,EAAWV,EAAOa,UAAU,EAE5CH,CACT,GACCI,IAAI,CAAC,IACLP,KAAK,CAAChB,GACNiB,GAAG,CAAC,IAEH,GAAkB,MAAM,CAApBE,EACF,MAAO,CAAEK,SAAS,EAAOC,MAAO,GAAI,EAGtC,IAAMP,EAAiBC,CAAS,CAAC,EAAE,CACnC,GAAuB,KAAK,CAAxBD,EACF,MAAO,CAAEM,SAAS,EAAOC,MAAOC,SAkD/BA,CAAwB,EAC/B,IAAMC,EAAUC,EAAMZ,KAAK,CAACd,UAE5B,EAIOyB,CAAO,CAJV,EAIa,CAACE,EAJJ,KAIW,CAAC1B,EAAmB,KAHpCyB,CAIX,EA1D2DT,EAAW,EAGhE,GAAIpF,CAAU,CAACmF,EAAe,CAC5B,CAD8B,KACvB,CAAEM,SAAS,EAAMC,MAAON,CAAU,EAG3C,GAAID,EAAeF,KAAK,CAACZ,GACvB,MAAUU,WACR,YAFqD,qDAGnDI,EACA,KAIN,MAAO,CAAEM,SAAS,EAAOC,MAAON,CAAU,CAC5C,GAGEV,EAAOxE,QAAQ,CAAC6F,YAAY,EAAE,CAChCf,EAAQN,EAAOxE,QAAQ,CAAC6F,YAAY,CAAClB,EAAcG,EAAAA,EAGrD,IAAMgB,EAAmB,uBACvBpB,eACAtC,SACAoC,CACF,EAEA,OAAOM,EACJE,GAAG,CAAC,IACH,GAAI,CAACe,EAAKR,OAAO,CAAE,OAAOQ,EAAKP,KAAK,CAEpC,IAAMzH,EAAQgI,EAAKP,KAAK,CAYxB,OATE,QAAEtJ,EAAAA,KAAAA,EAAAA,EAAS8J,SAAT9J,kBAAoC,GACpC+J,CAAAA,EAAAA,EAAAA,EAAAA,CAAwBA,CAAClI,IAC1B,QAAC7B,EAAAA,KAAAA,EAAAA,EAASgK,SAAThK,mBAASgK,GACTC,CAAAA,EAAAA,EAAAA,EAAAA,CAAyBA,CAACpI,EAAAA,GAC5B,CACAqI,EAAAA,EAAAA,EAAAA,CAAyBA,CAACrI,EAAOsG,EAAW9F,OAAOzC,IAI9CuK,CADWvG,GAAU,CAAC/B,CAAK,CAAC,EAAE,CAAC,EACrB4G,EAAc5G,EAAOyG,EAAOxE,QAAQ,CAAE8F,EACzD,GACCR,IAAI,CAAC,GACV,kDCtYO,SAASgB,EAAYC,CAAS,CAAEC,CAAW,CAAEtK,CAAO,EACzD,GAAM,CAACuK,EAAYC,EAAa,CAAGC,CAAAA,EAAAA,EAAAA,CAAAA,CAAcA,OAC/CzK,EAAAA,KAAAA,EAAAA,EAASE,EAAE,CACXmK,EACAC,GAEF,CAJEtK,MAKAuK,EAAWxI,WAAW,KAAOyI,EAAazI,WAAW,IACrDwI,EAAWnI,QAAQ,KAAOoI,EAAapI,QAAQ,EAEnD,kDCZO,SAASsI,EAAW9K,CAAI,CAAEI,CAAO,EACtC,IAAMwF,EAAQ3F,CAAAA,EAAAA,EAAAA,CAAAA,CAAMA,CAACD,QAAMI,EAAAA,KAAAA,EAAAA,EAASE,EAAE,EAEtC,KAF2BF,EAC3BwF,EAAMmF,QAAQ,CAAC,EAAG,EAAG,EAAG,GACjBnF,CACT,mDCKO,SAASoF,EAAchL,CAAI,CAAE0J,CAAK,QACvC,YAAI,OAAO1J,EAA4BA,EAAK0J,GAExC1J,GAAwB,UAAhB,OAAOA,GAAqBiL,EAAAA,EAAmBA,IAAIjL,EACtDA,CAAI,CAACiL,EAAZ,EAA+BA,CAAC,CAACvB,GAE/B1J,aAAgBkL,KAAa,CAAP,GAAWlL,EAAKmL,WAAW,CAACzB,GAE/C,IAAIwB,KAAKxB,EAClB,0GEHO,SAASnE,EAAQvF,CAAI,CAAEI,CAAO,EACnC,IAAMwF,EAAQ3F,CAAAA,EAAAA,EAAAA,CAAAA,CAAMA,CAACD,QAAMI,EAAAA,KAAAA,EAAAA,EAASE,EAAE,EAMtC,KAN2BF,EAMpBsB,KAAK0J,KAAK,CAACvF,CALL,CAACwF,EAAAA,EAAAA,CAAAA,CAAWA,CAACzF,EAAOxF,GDC5B,QCDuC,CDC9BkL,CAAoB,CAAElL,CAAO,GCDiBkL,GDK1DlL,EAAAA,EAEAoI,EAAAA,EAHApI,EAAAA,EAAAA,EAAAA,EAFF,IAAMoI,EAAiBC,CAAAA,EAAAA,EAAAA,CAAAA,CAAiBA,GAClCG,EACJxI,OAAAA,EAAAA,OAAAA,EAAAA,OAAAA,EAAAA,OAAAA,QAAAA,EAAAA,KAAAA,EAAAA,EAASwI,SAATxI,YAASwI,EAATxI,QACAA,GAAAA,CADAA,MACAA,EAAAA,EAASsI,MAATtI,GAAAA,OAAAA,EAAAA,EAAAA,OAAiBA,EAAjBA,KAAAA,EAAAA,EAA0BwI,GAA1BxI,kBAA0BwI,EAD1BxI,EAEAoI,EAAeI,QAFfxI,aAEewI,EAFfxI,SAGAoI,EAAAA,CAHApI,CAGesI,MAAAA,GAAfF,MAAAA,GAAAA,EAAAA,OAAuBpI,EAAvBoI,KAAAA,EAAAA,EAAgCI,GAAhCJ,kBAAgCI,EAHhCxI,EAIA,EAEIgC,EAAOqC,CAAAA,EAAAA,EAAAA,CANXrE,CAMsBqE,CAACzE,EAAMI,GACzBmL,EAAYP,CAAAA,EAAAA,EAAAA,CAAAA,CAAaA,CAAC5K,OAAAA,EAAAA,KAAAA,EAAAA,EAASE,EAAE,GAAIN,EAAM,EAArBI,CAIhC,OAAOwF,EAHG4F,WAAW,CAACpJ,EAAM,EAAGwG,GAC/B2C,EAAUR,QAAQ,CAAC,EAAG,EAAG,EAAG,GACdM,CAAAA,EAAAA,EAAAA,CAAAA,CAAWA,CAACE,EAAWnL,EAEvC,EChB+DwF,EAAOxF,EAAAA,EAK3CqL,EAAAA,EAAkBA,EAAI,CACjD,8DCtBO,SAASC,EAAW1L,CAAI,CAAEmB,CAAM,CAAEf,CAAO,EAC9C,IAAMwF,EAAQ3F,CAAAA,EAAAA,EAAAA,CAAAA,CAAMA,CAACD,QAAMI,EAAAA,KAAAA,EAAAA,EAASE,EAAE,EAEtC,KAF2BF,EAC3BwF,EAAM+F,OAAO,CAAC/F,EAAM7F,OAAO,GAAKoB,EAASyK,EAAAA,EAAoBA,EACtDhG,CACT,mDCTO,SAASM,EAAOlG,CAAI,CAAEI,CAAO,EAClC,MAAOH,CAAAA,EAAAA,EAAAA,CAAAA,CAAMA,CAACD,QAAMI,EAAAA,KAAAA,EAAAA,EAASE,EAAE,EAAE4F,KAAb9F,CAAmB,EACzC,8DCMO,SAASyL,EAAIC,CAAK,CAAE1L,CAAO,EAEhC,IADI2L,EACA7G,EAAU9E,QAAAA,KAAAA,EAAAA,EAASE,EAAE,CAAXF,OAEd0L,EAAME,OAAO,CAAC,IAER,GAA4B,UAAhB,OAAOhM,IACrBkF,EAAU8F,EAAAA,CAAaA,CAACiB,IAAI,CAAC,KAAMjM,EAAAA,EAErC,IAAMkM,EAAQjM,CAAAA,EAAAA,EAAAA,CAAAA,CAAMA,CAACD,EAAMkF,IACvB,CAAC6G,GAAUA,EAASG,GAASC,MAAM,CAACD,EAAAA,IAAQH,EAASG,CAAAA,CAC3D,GAEOlB,CAAAA,EAAAA,EAAAA,CAAAA,CAAaA,CAAC9F,EAAS6G,GAAUK,IAC1C,mDClBO,SAASC,EAAWrM,CAAI,CAAEgI,CAAO,CAAE5H,CAAO,EAC/C,IAAM8L,EAAQjM,CAAAA,EAAAA,EAAAA,CAAAA,CAAMA,CAACD,QAAMI,EAAAA,KAAAA,EAAAA,EAASE,EAAE,EAEtC,KAF2BF,EAC3B8L,EAAMG,UAAU,CAACrE,GACVkE,CACT,mDCHO,SAASI,EAAatM,CAAI,CAAEI,CAAO,EACxC,IAAMwF,EAAQ3F,CAAAA,EAAAA,EAAAA,CAAAA,CAAMA,CAACD,QAAMI,EAAAA,KAAAA,EAAAA,EAASE,EAAE,EAGtC,KAH2BF,EAC3BwF,EAAM2G,OAAO,CAAC,GACd3G,EAAMmF,QAAQ,CAAC,EAAG,EAAG,EAAG,GACjBnF,CACT,mDCNO,SAAS4G,EAAYxM,CAAI,CAAEmB,CAAM,CAAEf,CAAO,EAC/C,MAAOqM,CAAAA,EAAAA,EAAAA,CAAAA,CAAWA,CAACzM,EAAM,CAACmB,EAAQf,EACpC,yECcO,SAASsM,EAASC,CAAQ,CAAEvM,CAAO,MAGfA,MAGrBJ,EAUA6H,EAfE+E,EAAc,IAAM5B,CAAAA,EAAAA,EAAAA,CAAAA,CAAaA,OAAC5K,EAAAA,KAAAA,EAAAA,EAASE,EAAE,CAAE8L,KAE/CS,CAFkCzM,CAEfA,OAAAA,QAAAA,EAAAA,KAAAA,EAAAA,EAASyM,SAATzM,OAASyM,EAATzM,EAA6B,EAChD0M,EAAcC,MADK3M,GAuDlB2M,CAA0B,EACjC,IAEIC,EAFEF,EAAc,CAAC,EACfG,EAAQC,EAAWC,KAAK,CAACC,EAASC,iBAAiB,EAKzD,GAAIJ,EAAM5K,MAAM,CAAG,EACjB,CADoB,MACbyK,EAiBT,GAdI,IAAIQ,IAAI,CAACL,CAAK,CAAC,EAAE,EACnBD,CADsB,CACTC,CAAK,CAAC,EAAE,EAErBH,EAAY9M,IAAI,CAAGiN,CAAK,CAAC,EAAE,CAC3BD,EAAaC,CAAK,CAAC,EAAE,CACjBG,EAASG,iBAAiB,CAACD,IAAI,CAACR,EAAY9M,IAAI,GAAG,CACrD8M,EAAY9M,IAAI,CAAGkN,EAAWC,KAAK,CAACC,EAASG,iBAAiB,CAAC,CAAC,EAAE,CAClEP,EAAaE,EAAWM,MAAM,CAC5BV,EAAY9M,IAAI,CAACqC,MAAM,CACvB6K,EAAW7K,MAAM,IAKnB2K,EAAY,CACd,IAAM/K,EAAQmL,EAASK,QAAQ,CAACC,IAAI,CAACV,GACjC/K,GACF6K,EAAYzM,EADH,EACO,CAAG2M,EAAWlD,OAAO,CAAC7H,CAAK,CAAC,EAAE,CAAE,IAChD6K,EAAYW,QAAQ,CAAGxL,CAAK,CAAC,EAAE,EAE/B6K,EAAYzM,IAAI,CAAG2M,CAEvB,CAEA,OAAOF,CACT,EA1FsCH,GAGpC,GAAIG,EAAY9M,IAAI,CAAE,CACpB,IAAM2N,EAAkBC,SAwFnBA,CAAoB,CAAEf,CAAgB,EAC7C,IAAMgB,EAAQ,OACZ,uBACG,GAAIhB,CAAAA,CAAe,CACpB,sBACC,GAAIA,CAAAA,CAAe,CACpB,QAGEiB,EAAWZ,EAAWjE,KAAK,CAAC4E,GAElC,GAAI,CAACC,EAAU,MAAO,CAAE1L,KAAMgK,IAAK2B,eAAgB,EAAG,EAEtD,IAAM3L,EAAO0L,CAAQ,CAAC,EAAE,CAAGE,SAASF,CAAQ,CAAC,EAAE,EAAI,KAC7CG,EAAUH,CAAQ,CAAC,EAAE,CAAGE,SAASF,CAAQ,CAAC,EAAE,EAAI,KAGtD,MAAO,CACL1L,KAAM6L,SAAmB7L,EAAO6L,MAChCF,eAAgBb,EAAWgB,KAAK,CAAC,CAACJ,CAAQ,CAAC,EAAE,EAAIA,CAAQ,CAAC,IAAIzL,MAAM,CACtE,CACF,EA7GsCyK,EAAY9M,IAAI,CAAE6M,GACpD7M,EA8GJ,SAAmBkN,CAAU,CAAE9K,CAAI,QA0GFkD,EAAMa,EAbjB/D,CAaoB,GAbhB,MA3FxB,GAAa,OAATA,EAAe,OAAO,IAAI8I,KAAKkB,KAEnC,IAAM0B,EAAWZ,EAAWjE,KAAK,CAACkF,GAElC,GAAI,CAACL,EAAU,OAAO,IAAI5C,KAAKkB,KAE/B,IAAMgC,EAAa,CAAC,CAACN,CAAQ,CAAC,EAAE,CAC1BnI,EAAY0I,EAAcP,CAAQ,CAAC,EAAE,EACrCvL,EAAQ8L,EAAcP,CAAQ,CAAC,EAAE,EAAI,EACrC3H,EAAMkI,EAAcP,CAAQ,CAAC,EAAE,EAC/BxI,EAAO+I,EAAcP,CAAQ,CAAC,EAAE,EAChC7H,EAAYoI,EAAcP,CAAQ,CAAC,EAAE,EAAI,EAE/C,GAAIM,SACF,CA0FsBE,EA3FR,CACV,CAACC,CA0FsB,CA1FCjJ,IAAMW,EA2F7BX,GAAQ,GAAKA,GAAQ,CA3FoB,GA2Fda,GAAO,GAAKA,GAAO,GAxF5CqI,SAwDFA,CAA4B,CAAElJ,CAAI,CAAEa,CAAG,EAC9C,IAAMnG,EAAO,IAAIkL,KAAK,GACtBlL,EAAKyO,cAAc,CAAC5J,EAAa,EAAG,GACpC,IAAM6J,EAAqB1O,EAAK2O,SAAS,IAAM,EAG/C,OADA3O,EAAK4O,UAAU,CAAC5O,EAAK6O,UAAU,GADlB,CAACvJ,CACsBO,GADf,EAAK,EAAIM,EAAM,EAAIuI,CAAAA,GAEjC1O,CACT,EA/D4BoC,EAAMkD,EAAMW,GAF3B,IAAIiF,KAAKkB,KAGb,CACL,IAAMpM,EAAO,IAAIkL,KAAK,SACtB,CACE,CAAC4D,CAAa1M,EAsEQG,EAtEFA,EAsESvC,CAAF,CAtEAmG,EAsEM,GAE1B,GACT5D,CAxEGwM,EAwEM,IACT/O,GAAQ,GACRA,IAASgP,CAAY,CAACzM,EAAbyM,GAAwBC,CAAAA,CAAgB7M,GAAQ,GAAK,GAAC,CAAC,GAIrCA,EA9EFA,EA8EM,CAAEuD,EA9EFA,IA+Eb,GADwB,IACLsJ,EAAgB7M,GAAQ,IAAxB6M,GAA8B,CAAE,CA/EtCtJ,GAC7B,EAGG8I,cAAc,CAACrM,EAAMG,EAAOb,KAAKmK,GAAG,CAAClG,EAAWQ,IAC9CnG,GAHE,IAAIkL,KAAKkB,IAIpB,CACF,EA7IqBuB,EAAgBI,cAAc,CAAEJ,EAAgBvL,IAAI,CACvE,CAEA,GAAI,CAACpC,GAAQmM,MAAM,CAACnM,GAAO,OAAO4M,IAElC,IAAMjF,EAAY,CAAC3H,EACfK,EAAO,EAGX,GAAIyM,EAAYzM,IAAI,EAAE,MACpBA,EAAO6O,SAyIFA,CAAoB,YAC3B,IAAMpB,EAAWd,EAAW/D,KAAK,CAACkG,GAClC,GAAI,CAACrB,EAAU,OAAO1B,IAEtB,CAF2B,GAErBxF,EAAQwI,EAActB,CAAQ,CAAC,EAAE,EACjC9F,EAAUoH,EAActB,CAAQ,CAAC,EAAE,EACnCuB,EAAUD,EAActB,CAAQ,CAAC,EAAE,QAEzC,CAiEoBlH,EAjEFA,CAAd,CAAC0I,CAiEoB,CAjEAtH,EAiEWqH,EAjEFA,CAiEA,CAClC,GAD2C,CACzB,CAAdzI,EACiB,CAnEuB,GAmEnCoB,GAA6B,IAAZqH,EAIxBA,GAAW,GACXA,EAAU,IACVrH,GAAW,GACXA,EAAU,IACVpB,GAAS,GACTA,EAAQ,IAvERA,EAAQvF,EAAAA,EAAkBA,CAAG2G,EAAU4D,EAAAA,EAAoBA,CAAa,IAAVyD,EAJvDjD,GAMX,EAxJqBU,EAAYzM,IAAI,GAChB,OAAOuM,IAG1B,GAAIE,EAAYW,QAAQ,EAAE,GAEpBtB,MADJtE,EAAS0H,SAyJJA,CAA4B,MAmEXC,IAlExB,EAkE8B,CAlEP,MAAnBC,EAAwB,OAAO,EAEnC,IAAM3B,EAAW2B,EAAexG,KAAK,CAACyG,GACtC,GAAI,CAAC5B,EAAU,OAAO,EAEtB,IAAMhM,EAAuB,MAAhBgM,CAAQ,CAAC,EAAE,CAAW,CAAC,EAAI,EAClClH,EAAQoH,SAASF,CAAQ,CAAC,EAAE,EAC5B9F,EAAU,CAAS,CAAC,EAAE,EAAIgG,SAASF,CAAQ,CAAC,EAAE,GAAM,QAE1D,GAAsBlH,CAAlB,CAAC+I,CAyD2B3H,EAzDHA,IA0DX,CADqB,EAChBA,GA1DgB,IAIhClG,EAAQ8E,GAAQvF,EAAAA,EAAkBA,CAAG2G,EAAU4D,EAAAA,EAAmB,EAHhEQ,GAIX,EAxK2BU,EAAYW,QAAQ,GACxB,OAAOb,GAAAA,KACrB,CACL,IAAMgD,EAAU,IAAI1E,KAAKvD,EAAYtH,GAC/B0L,EAAS9L,CAAAA,EAAAA,EAAAA,CAAAA,CAAMA,CAAC,QAAGG,EAAAA,KAAAA,EAAAA,EAASE,EAAE,EAYpC,KAZyBF,EACzB2L,EAAOP,WAAW,CAChBoE,EAAQC,cAAc,GACtBD,EAAQE,WAAW,GACnBF,EAAQf,UAAU,IAEpB9C,EAAOhB,QAAQ,CACb6E,EAAQG,WAAW,GACnBH,EAAQI,aAAa,GACrBJ,EAAQK,aAAa,GACrBL,EAAQM,kBAAkB,IAErBnE,CACT,CAEA,MAAO9L,CAAAA,EAAAA,EAAAA,CAAAA,CAAMA,CAAC0H,EAAYtH,EAAOwH,QAAQzH,EAAAA,KAAAA,EAAAA,EAASE,EAAE,CACtD,CAEA,IAAM8M,CAHqChN,CAG1B,CACfiN,kBAAmB,OACnBE,kBAAmB,QACnBE,SAAU,YACZ,EAEMU,EACJ,gEACIgB,EACJ,4EACIO,EAAgB,gCAgGtB,SAASrB,EAAc3E,CAAK,EAC1B,OAAOA,EAAQsE,SAAStE,GAAS,CACnC,CAmBA,SAAS0F,EAAc1F,CAAK,EAC1B,OAAO,GAAUyG,WAAWzG,EAAMI,OAAO,CAAC,IAAK,OAAU,CAC3D,CA+BA,IAAMkF,EAAe,CAAC,GAAI,KAAM,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAG,CAEvE,SAASC,EAAgB7M,CAAI,EAC3B,OAAOA,EAAO,KAAQ,GAAMA,EAAO,GAAM,GAAKA,EAAO,KAAQ,CAC/D,8DCjOO,SAASgO,EAAWpQ,CAAI,CAAEgF,CAAO,CAAE5E,CAAO,EAC/C,IAAM8L,EAAQjM,CAAAA,EAAAA,EAAAA,CAAAA,CAAMA,CAACD,QAAMI,EAAAA,KAAAA,EAAAA,EAASE,EAAE,EAChC+P,EAAa3O,GADQtB,EACHiD,KAAK,CAAC6I,EAAM1J,QAAQ,GAAK,GAAK,EAEtD,MAAO8N,CAAAA,EAAAA,EAAAA,CAAAA,CAAQA,CAACpE,EAAOA,EAAM1J,QAAQ,GAAKqD,GAD7Bb,EAAUqL,CAAAA,EAEzB,mDCNO,SAASE,EAAWvQ,CAAI,CAAEI,CAAO,EACtC,IAAMwF,EAAQ3F,CAAAA,EAAAA,EAAAA,CAAAA,CAAMA,CAACD,QAAMI,EAAAA,KAAAA,EAAAA,EAASE,EAAE,EAChCiC,EAAQqD,EAAMpD,CADOpC,OACC,GAG5B,OAFAwF,EAAM4F,WAAW,CAAC5F,EAAMzD,WAAW,GAAII,EAAQ,EAAG,GAClDqD,EAAMmF,QAAQ,CAAC,GAAI,GAAI,GAAI,KACpBnF,CACT,8DCFO,SAAS4K,EAAI1E,CAAK,CAAE1L,CAAO,EAEhC,IADI2L,EACA7G,QAAU9E,EAAAA,KAAAA,EAAAA,EAASE,EAAE,CAWzB,MAXcF,CAEd0L,EAAME,OAAO,CAAC,IAEP9G,GAA2B,UAAhB,OAAOlF,IACrBkF,EAAU8F,EAAAA,CAAaA,CAACiB,IAAI,CAAC,KAAMjM,EAAAA,EAErC,IAAMkM,EAAQjM,CAAAA,EAAAA,EAAAA,CAAAA,CAAMA,CAACD,EAAMkF,IACvB,CAAC6G,GAAUA,EAASG,GAASC,MAAM,CAACD,EAAAA,IAAQH,EAASG,CAAAA,CAC3D,GAEOlB,CAAAA,EAAAA,EAAAA,CAAAA,CAAaA,CAAC9F,EAAS6G,GAAUK,IAC1C,mDClBO,SAASqE,EAASzQ,CAAI,CAAEI,CAAO,EACpC,IAAMwF,EAAQ3F,CAAAA,EAAAA,EAAAA,CAAAA,CAAMA,CAACD,QAAMI,EAAAA,KAAAA,EAAAA,EAASE,EAAE,EAEtC,KAF2BF,EAC3BwF,EAAMmF,QAAQ,CAAC,GAAI,GAAI,GAAI,KACpBnF,CACT,mDCJO,SAAS8K,EAAW1Q,CAAI,CAAEqP,CAAO,CAAEjP,CAAO,EAC/C,IAAMwF,EAAQ3F,CAAAA,EAAAA,EAAAA,CAAAA,CAAMA,CAACD,QAAMI,EAAAA,KAAAA,EAAAA,EAASE,EAAE,EAEtC,KAF2BF,EAC3BwF,EAAM8K,UAAU,CAACrB,GACVzJ,CACT,mDCJO,SAAS+K,EAAU3Q,CAAI,CAAEI,CAAO,EACrC,IAAMwF,EAAQ3F,CAAAA,EAAAA,EAAAA,CAAAA,CAAMA,CAACD,QAAMI,EAAAA,KAAAA,EAAAA,EAASE,EAAE,EAChC8B,EAAOwD,EAAMzD,CADQ/B,UACG,GAG9B,OAFAwF,EAAM4F,WAAW,CAACpJ,EAAO,EAAG,EAAG,GAC/BwD,EAAMmF,QAAQ,CAAC,GAAI,GAAI,GAAI,KACpBnF,CACT,8DCLO,SAASgL,EAAQ5Q,CAAI,CAAEmB,CAAM,CAAEf,CAAO,EAC3C,IAAMwF,EAAQ3F,CAAAA,EAAAA,EAAAA,CAAAA,CAAMA,CAACD,QAAMI,EAAAA,KAAAA,EAAAA,EAASE,EAAE,OAAXF,EAC3B,MAAUe,GAAgB6J,CAAAA,EAAAA,EAAAA,CAAAA,CAAaA,CAAC5K,CAAAA,QAAAA,KAAAA,EAAAA,EAASE,EAAAA,CAATF,EAAeJ,EAAMoM,MAGxDjL,GAELyE,EAAM2G,GAFO,IAEA,CAAC3G,EAAM3E,OAAO,GAAKE,GAFZyE,EAItB,mDCEO,SAAS3F,EAAO0M,CAAQ,CAAEzH,CAAO,EAEtC,MAAO8F,CAAAA,EAAAA,EAAAA,CAAAA,CAAaA,CAAC9F,GAAWyH,EAAUA,EAC5C,8DCdO,SAASvL,EAAgBpB,CAAI,CAAEmB,CAAM,CAAEf,CAAO,EACnD,MAAO4K,CAAAA,EAAAA,EAAAA,CAAAA,CAAaA,CAAC5K,OAAAA,EAAAA,KAAAA,EAAAA,EAASE,EAAAA,GAAMN,EAAM,CAACC,CAAtBG,EAAsBH,EAAAA,CAAAA,CAAMA,CAACD,GAAQmB,EAC5D,8DEDO,SAASmP,EAAStQ,CAAI,CAAEuC,CAAK,CAAEnC,CAAO,EAC3C,IAAMwF,EAAQ3F,CAAAA,EAAAA,EAAAA,CAAAA,CAAMA,CAACD,QAAMI,EAAAA,KAAAA,EAAAA,EAASE,EAAE,EAChC8B,EAAOwD,EAAMzD,CADQ/B,UACG,GACxB+F,EAAMP,EAAM3E,OAAO,GAEnB4P,EAAW7F,CAAAA,EAAAA,EAAAA,CAAAA,CAAaA,CAAC5K,OAAAA,EAAAA,KAAAA,EAAAA,EAASE,EAAE,GAAIN,EAAM,EAArBI,CAC/ByQ,EAASrF,WAAW,CAACpJ,EAAMG,EAAO,IAClCsO,EAAS9F,QAAQ,CAAC,EAAG,EAAG,EAAG,GAC3B,IAAM+F,EDbD,SAASC,CAAmB,CAAE3Q,CAAO,ECaR2Q,IDZ5BnL,EAAQ3F,CAAAA,EAAAA,EAAAA,CAAAA,CAAMA,CAACD,EAAMI,IAAAA,CAAAA,GACrBgC,CADqBhC,CACdwF,EAAMzD,KADiB7B,EAAE,IACR,CADHF,EAErB4Q,EAAapL,EAAMpD,QAAQ,GAC3ByO,EAAiBjG,CAAAA,EAAAA,EAAAA,CAAAA,CAAaA,CAACpF,EAAO,GAG5C,OAFAqL,EAAezF,WAAW,CAACpJ,EAAM4O,EAAa,EAAG,GACjDC,EAAelG,QAAQ,CAAC,EAAG,EAAG,EAAG,GAC1BkG,EAAehQ,OAAO,EAC/B,ECKqC4P,GAInC,OADAjL,EAAM0K,QAAQ,CAAC/N,EAAOb,KAAK8O,GAAG,CAACrK,EAAK2K,IAC7BlL,CACT,+FEdO,SAASH,EAAWzF,CAAI,CAAEI,CAAO,EACtC,IAAMwF,EAAQ3F,CAAAA,EAAAA,EAAAA,CAAAA,CAAMA,CAACD,EAAMI,QAAAA,KAAAA,EAAAA,EAASE,EAAE,CAAXF,CAM3B,OAAOsB,KAAK0J,KAAK,CAACvF,CALL,CAACqL,EAAAA,EAAAA,CAAAA,CAAcA,CAACtL,GDExB,MCFiC,GDExBuL,CAAuB,EAAS,EAC9C,IAAM/O,EAAOwC,CAAAA,EAAAA,EAAAA,CAAAA,CAAcA,CAAC5E,MAAMI,CADKA,GAEjCgR,EAAkBpG,CAAAA,EAAAA,EAAAA,CAAAA,CAAaA,CAAC5K,CAAAA,CAAqB,GAG3D,OAFAgR,EAAgB5F,OADsBpL,IACX,CAACgC,EAAM,EAAG,GACrCgP,EAAgBrG,QAAQ,CAAC,EAAG,EAAG,EAAG,CAFI3K,EAG/B8Q,CAAAA,EAAAA,EAAAA,CAAAA,CAAcA,CAACE,EACxB,ECR4DxL,EAAAA,EAKjC6F,EAAAA,EAAkBA,EAAI,CACjD,8DCuBO,SAAS4F,EAAiB5G,CAAS,CAAEC,CAAW,CAAEtK,CAAO,EAC9D,GAAM,CAACuK,EAAYC,EAAa,CAAGC,CAAAA,EAAAA,EAAAA,CAAAA,CAAcA,OAC/CzK,EAAAA,KAAAA,EAAAA,EAASE,EAAE,CACXmK,EACAC,GAGI5I,CALJ1B,CAKWkR,EAAgB3G,EAAYC,GACnC2G,EAAa7P,KAAKC,GAAG,CACzBmE,CAAAA,EAAAA,EAAAA,CAAAA,CAAwBA,CAAC6E,EAAYC,IAGvCD,EAAW4B,OAAO,CAAC5B,EAAW1J,OAAO,GAAKa,EAAOyP,GAIjD,IAAMC,EAAmBC,OACvBH,EAAgB3G,EAAYC,KAAkB,CAAC9I,GAG3CiK,EAASjK,GAAQyP,EAAaC,CAAAA,CAAbD,CAEvB,OAAkB,IAAXxF,EAAe,EAAIA,CAC5B,CAMA,SAASuF,EAAgB7G,CAAS,CAAEC,CAAW,EAC7C,IAAM7E,EACJ4E,EAAUtI,WAAW,GAAKuI,EAAYvI,WAAW,IACjDsI,EAAUjI,QAAQ,GAAKkI,EAAYlI,QAAQ,IAC3CiI,EAAUxJ,OAAO,GAAKyJ,EAAYzJ,OAAO,IACzCwJ,EAAU7H,QAAQ,GAAK8H,EAAY9H,QAAQ,IAC3C6H,EAAUxH,UAAU,GAAKyH,EAAYzH,UAAU,IAC/CwH,EAAUzJ,UAAU,GAAK0J,EAAY1J,UAAU,IAC/CyJ,EAAUlH,eAAe,GAAKmH,EAAYnH,eAAe,UAE3D,EAAW,EAAU,CAAP,EACVsC,EAAO,EAAU,CAAP,CAGPA,CACT,mDC5EO,SAAS6L,EAAU1R,CAAI,CAAEmB,CAAM,CAAEf,CAAO,EAC7C,MAAOuR,CAAAA,EAAAA,EAAAA,CAAAA,CAASA,CAAC3R,EAAM,CAACmB,EAAQf,EAClC,2GGzBO,OAAMwR,EAGXC,SAASC,CAAQ,CAAEC,CAAQ,CAAE,CAC3B,OAAO,CACT,oBAJAC,WAAAA,CAAc,EAKhB,CAEO,MAAMC,UAAoBL,EAqB/BC,SAAS7R,CAAI,CAAEI,CAAO,CAAE,CACtB,OAAO,IAAI,CAAC8R,aAAa,CAAClS,EAAM,IAAI,CAAC0J,KAAK,CAAEtJ,EAC9C,CAEA+R,IAAInS,CAAI,CAAEoS,CAAK,CAAEhS,CAAO,CAAE,CACxB,OAAO,IAAI,CAACiS,QAAQ,CAACrS,EAAMoS,EAAO,IAAI,CAAC1I,KAAK,CAAEtJ,EAChD,CA1BA+K,YACEzB,CAAK,CAELwI,CAAa,CAEbG,CAAQ,CAERC,CAAQ,CACRN,CAAW,CACX,CACA,KAAK,GACL,IAAI,CAACtI,KAAK,CAAGA,EACb,IAAI,CAACwI,aAAa,CAAGA,EACrB,IAAI,CAACG,QAAQ,CAAGA,EAChB,IAAI,CAACC,QAAQ,CAAGA,EACZN,IACF,IAAI,CAACA,IADU,OACC,CAAGA,CAAAA,CAEvB,CASF,CAEO,MAAMO,UAA2BX,EAStCO,IAAInS,CAAI,CAAEoS,CAAK,CAAE,QACf,EAAUI,cAAc,CAASxS,CAAP,CACnBgL,CAAAA,EAAAA,EAAAA,CAAAA,CAAaA,CAAChL,EAAMyS,SDxBfA,CAAc,CAAEtH,CAAW,MAcpBA,EAGnBA,EAhBF,IAAMe,EAAQwG,CAakB,WAE9B,OAAOvH,EAfmBA,IAgB1BA,CAAAA,OAAAA,EAAAA,EAAYwH,SAAAA,EAAZxH,KAAAA,EAAAA,EAAuBA,GAAvBA,QAAuBA,IAAgBA,EAfrC,IAAIA,EAAY,GAChBH,CAAAA,EAAAA,EAAAA,CAAAA,CAAaA,CAACG,EAAa,GAQ/B,OAPAe,EAAMV,WAAW,CAACxL,EAAKmC,WAAW,GAAInC,EAAKwC,QAAQ,GAAIxC,EAAKiB,OAAO,IACnEiL,EAAMnB,QAAQ,CACZ/K,EAAK4C,QAAQ,GACb5C,EAAKiD,UAAU,GACfjD,EAAKgB,UAAU,GACfhB,EAAKuD,eAAe,IAEf2I,CACT,ECYyClM,EAAM,IAAI,CAACkF,OAAO,EACzD,CARAiG,YAAYjG,CAAO,CAAE0N,CAAS,CAAE,CAC9B,KAAK,QAJPN,QAAAA,CAzC6B,EAyClBO,CAAAA,IAAAA,CACXb,WAAAA,CAAc,CAAC,EAIb,IAAI,CAAC9M,OAAO,CAAGA,GAAY,IAAU8F,CAAAA,EAAAA,CAAV,CAAUA,CAAAA,CAAaA,CAAC4H,EAAW5S,EAAAA,CAAI,CAOtE,CCtDO,MAAM8S,EACXC,IAAI7F,CAAU,CAAEjL,CAAK,CAAEgH,CAAK,CAAE7I,CAAO,CAAE,CACrC,IAAM2L,EAAS,IAAI,CAACiH,KAAK,CAAC9F,EAAYjL,EAAOgH,EAAO7I,UAC/C2L,EAIE,CACLkH,CALE,IAAS,EAKH,IAAIhB,EACVlG,EAAOrC,KAAK,CACZ,CAFqBuI,GAEjB,CAACJ,QAAQ,CACb,IAAI,CAACM,GAAG,CACR,IAAI,CAACG,QAAQ,CACb,IAAI,CAACN,WAAW,EAElBkB,KAAMnH,EAAOmH,IAAI,EAXV,IAaX,CAEArB,SAASC,CAAQ,CAAEqB,CAAM,CAAEpB,CAAQ,CAAE,CACnC,OAAO,CACT,CACF,CCtBO,MAAMqB,UAAkBN,EAG7BE,IAHmCF,EAG7B5F,CAAU,CAAEjL,CAAK,CAAEgH,CAAK,CAAE,CAC9B,OAAQhH,GAEN,IAAK,IACL,IAAK,KACL,IAAK,MACH,OACEgH,EAAM9E,GAAG,CAAC+I,EAAY,CAAE9I,MAAO,aAAc,IAC7C6E,EAAM9E,GAAG,CAAC+I,EAAY,CAAE9I,MAAO,QAAS,EAI5C,KAAK,QACH,OAAO6E,EAAM9E,GAAG,CAAC+I,EAAY,CAAE9I,MAAO,QAAS,EAEjD,KAAK,IAEH,OACE6E,EAAM9E,GAAG,CAAC+I,EAAY,CAAE9I,MAAO,MAAO,IACtC6E,EAAM9E,GAAG,CAAC+I,EAAY,CAAE9I,MAAO,aAAc,IAC7C6E,EAAM9E,GAAG,CAAC+I,EAAY,CAAE9I,MAAO,QAAS,EAE9C,CACF,CAEA+N,IAAInS,CAAI,CAAEoS,CAAK,CAAE1I,CAAK,CAAE,CAItB,OAHA0I,EAAMjO,GAAG,CAAGuF,EACZ1J,EAAKwL,WAAW,CAAC9B,EAAO,EAAG,GAC3B1J,EAAK+K,QAAQ,CAAC,EAAG,EAAG,EAAG,GAChB/K,CACT,mBAjCK,iBACLsS,QAAAA,CAAW,SAkCXe,kBAAAA,CAAqB,CAAC,IAAK,IAAK,IAAK,IAAI,CAC3C,gBCtCO,IAAMC,EAAkB,CAC7B/Q,MAAO,iBACPvC,KAAM,qBACN2F,UAAW,kCACXL,KAAM,qBACNiO,QAAS,qBACTC,QAAS,qBACTC,QAAS,iBACTC,QAAS,iBACTC,OAAQ,YACRC,OAAQ,YAERC,YAAa,MACbC,UAAW,WACXC,YAAa,WACbC,WAAY,WAEZC,gBAAiB,SACjBC,kBAAmB,QACnBC,gBAAiB,aACjBC,kBAAmB,aACnBC,iBAAkB,YACpB,EAAE,EAE8B,CAC9BC,qBAAsB,2BACtBC,MAAO,0BACPC,qBAAsB,oCACtBC,SAAU,2BACVC,wBAAyB,qCAC3B,ECtBO,SAASC,EAASC,CAAa,CAAEC,CAAK,SAC3C,EAIO,CACLnL,CALE,KAKKmL,EAAMD,EAAclL,EALT,GAKc,EAChCwJ,KAAM0B,EAAc1B,IAAI,EALjB0B,CAOX,CAEO,SAASE,EAAoBC,CAAO,CAAE7H,CAAU,EACrD,IAAM8H,EAAc9H,EAAWjE,KAAK,CAAC8L,UAEhCC,EAIE,CACLtL,CALE,KAKKsE,IALS,KAKAgH,CAAW,CAAC,EAAE,CAAE,IAChC9B,KAAMhG,EAAWgB,KAAK,CAAC8G,CAAW,CAAC,EAAE,CAAC3S,MAAM,CAC9C,EANS,IAOX,CAEO,SAAS4S,EAAqBF,CAAO,CAAE7H,CAAU,EACtD,IAAM8H,EAAc9H,EAAWjE,KAAK,CAAC8L,GAErC,GAAI,CAACC,EACH,OAAO,IADS,CAKlB,GAAuB,KAAK,CAAxBA,CAAW,CAAC,EAAE,CAChB,MAAO,CACLtL,MAAO,EACPwJ,KAAMhG,EAAWgB,KAAK,CAAC,EACzB,EAGF,IAAMpM,EAA0B,MAAnBkT,CAAW,CAAC,EAAE,CAAW,EAAI,CAAC,EACrCpO,EAAQoO,CAAW,CAAC,EAAE,CAAGhH,SAASgH,CAAW,CAAC,EAAE,CAAE,IAAM,EACxDhN,EAAUgN,CAAW,CAAC,EAAE,CAAGhH,SAASgH,CAAW,CAAC,EAAE,CAAE,IAAM,EAC1D3F,EAAU2F,CAAW,CAAC,EAAE,CAAGhH,SAASgH,CAAW,CAAC,EAAE,CAAE,IAAM,EAEhE,MAAO,CACLtL,MACE5H,GACC8E,EAAQvF,EAAAA,EAAkBA,CACzB2G,EAAU4D,EAAAA,EAAoBA,CAC9ByD,EAAU6F,EAAAA,EAAmB,EACjChC,KAAMhG,EAAWgB,KAAK,CAAC8G,CAAW,CAAC,EAAE,CAAC3S,MAAM,CAC9C,CACF,CAEO,SAAS8S,EAAqBjI,CAAU,EAC7C,OAAO4H,EAAoBxB,EAAgBW,aAADX,EAAgB,CAAEpG,EAC9D,CAEO,SAASkI,EAAaC,CAAC,CAAEnI,CAAU,EACxC,OAAQmI,GACN,KAAK,EACH,OAAOP,EAAoBxB,EAAgBO,WAAW,CAAE3G,CAAdoG,CAC5C,MAAK,EACH,OAAOwB,EAAoBxB,EAAgBQ,SAAS,CAAE5G,EACxD,CAD4CoG,KACvC,EACH,OAAOwB,EAAoBxB,EAAgBS,WAAW,CAAE7G,CAAdoG,CAC5C,MAAK,EACH,OAAOwB,EAAoBxB,EAAgBU,UAAU,CAAE9G,EACzD,SACE,OAAO4H,EAAoB,OAAW,UAAYO,EAAI,KAAMnI,EAChE,CACF,CAEO,SAASoI,EAAmBD,CAAC,CAAEnI,CAAU,EAC9C,OAAQmI,GACN,KAAK,EACH,OAAOP,EAAoBxB,EAAgBY,aAADZ,IAAkB,CAAEpG,EAChE,MAAK,EACH,OAAO4H,EAAoBxB,EAAgBa,aAADb,EAAgB,CAAEpG,EAC9D,MAAK,EACH,OAAO4H,EAAoBxB,EAAgBc,aAADd,IAAkB,CAAEpG,EAChE,MAAK,EACH,OAAO4H,EAAoBxB,EAAgBe,aAADf,GAAiB,CAAEpG,EAC/D,SACE,OAAO4H,EAAoB,OAAW,YAAcO,EAAI,KAAMnI,EAClE,CACF,CAEO,SAASqI,EAAqB7O,CAAS,EAC5C,OAAQA,GACN,IAAK,UACH,OAAO,CACT,KAAK,UACH,OAAO,EACT,KAAK,KACL,IAAK,OACL,IAAK,YACH,OAAO,EACT,KAAK,IAIH,OAAO,CACX,CACF,CAEO,SAAS8O,EAAsB7Q,CAAY,CAAE8Q,CAAW,EAC7D,IAOI1J,EAPE2J,EAAcD,EAAc,EAK5BE,EAAiBD,EAAcD,EAAc,EAAIA,EAGvD,GAAIE,GAAkB,GACpB5J,CADwB,CACfpH,GAAgB,QACpB,CACL,IAAMiR,EAAWD,EAAiB,GAGlC5J,EAASpH,EAF4C,IAA7BjD,KAAK2B,IAELwS,CAFU,CAACD,EAAW,KAEHE,KADjBnR,CAC2C,EAD3BiR,CACCE,CADU,IAEvD,CAEA,OAAOJ,EAAc3J,EAAS,EAAIA,CACpC,CAEO,SAASkD,EAAgB7M,CAAI,EAClC,OAAOA,EAAO,KAAQ,GAAMA,EAAO,GAAM,GAAKA,EAAO,KAAQ,CAC/D,CC7HO,MAAM2T,UAAmBjD,EAI9BE,IAJoCF,EAI9B5F,CAAU,CAAEjL,CAAK,CAAEgH,CAAK,CAAE,CAC9B,IAAM+M,EAAgB,GAAW,OAC/B5T,EACA6T,eAA0B,OAAVhU,EAClB,EAEA,OAAQA,GACN,IAAK,IACH,OAAO0S,EAASS,EAAa,EAAGlI,EAAjByH,CAA8BqB,EAC/C,GAD8BZ,EACzB,KACH,OAAOT,EACL1L,EAAM5E,IADOsQ,SACM,CAACzH,EAAY,CAC9B5I,KAAM,MACR,GACA0R,EAEJ,SACE,OAAOrB,EAASS,EAAanT,EAAMI,EAApBsS,IAA0B,CAAEzH,CAAfkI,EAA4BY,EAC5D,CACF,CAEAnE,SAASjM,CAAK,CAAE8D,CAAK,CAAE,CACrB,OAAOA,EAAMuM,cAAc,EAAIvM,EAAMtH,IAAI,CAAG,CAC9C,CAEA+P,IAAInS,CAAI,CAAEoS,CAAK,CAAE1I,CAAK,CAAE,CACtB,IAAM+L,EAAczV,EAAKmC,WAAW,GAEpC,GAAIuH,EAAMuM,cAAc,CAAE,CACxB,IAAMC,EAAyBV,EAC7B9L,EAAMtH,IAAI,CACVqT,GAIF,OAFAzV,EAJoDwV,WAIpC,CAACU,EAAwB,EAAG,GAC5ClW,EAAK+K,QAAQ,CAAC,EAAG,EAAG,EAAG,GAChB/K,CACT,CAEA,IAAMoC,EACJ,CAAE,OAASgQ,GAAwB,EAApB,IAAYjO,GAAG,CAAsB,EAAIuF,EAAMtH,IAAI,CAA3BsH,EAAMtH,IAAI,CAGnD,OAFApC,EAAKwL,WAAW,CAACpJ,EAAM,EAAG,GAC1BpC,EAAK+K,QAAQ,CAAC,EAAG,EAAG,EAAG,GAChB/K,CACT,mBA/CK,iBACLsS,QAAAA,CAAW,SACXe,kBAAAA,CAAqB,CAAC,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAI,CA8CzE,0BCpDO,OAAM8C,UAA4BrD,EAGvCE,IAH6CF,EAGvC5F,CAAU,CAAEjL,CAAK,CAAEgH,CAAK,CAAE,CAC9B,IAAM+M,EAAgB,GAAW,OAC/B5T,EACA6T,eAA0B,OAAVhU,EAClB,EAEA,OAAQA,GACN,IAAK,IACH,OAAO0S,EAASS,EAAa,EAAGlI,EAAjByH,CAA8BqB,EAC/C,GAD8BZ,EACzB,KACH,OAAOT,EACL1L,EAAM5E,IADOsQ,SACM,CAACzH,EAAY,CAC9B5I,KAAM,MACR,GACA0R,EAEJ,SACE,OAAOrB,EAASS,EAAanT,EAAMI,EAApBsS,IAA0B,CAAEzH,CAAfkI,EAA4BY,EAC5D,CACF,CAEAnE,SAASjM,CAAK,CAAE8D,CAAK,CAAE,CACrB,OAAOA,EAAMuM,cAAc,EAAIvM,EAAMtH,IAAI,CAAG,CAC9C,CAEA+P,IAAInS,CAAI,CAAEoS,CAAK,CAAE1I,CAAK,CAAEtJ,CAAO,CAAE,CAC/B,IAAMqV,EAAchR,CAAAA,EAAAA,EAAAA,CAAAA,CAAWA,CAACzE,EAAMI,GAEtC,GAAIsJ,EAAMuM,cAAc,CAAE,CACxB,IAAMC,EAAyBV,EAC7B9L,EAAMtH,IAAI,CACVqT,GAQF,OANAzV,EAJoDwV,WAIpC,CACdU,EACA,EACA9V,EAAQwI,qBAAqB,EAE/B5I,EAAK+K,QAAQ,CAAC,EAAG,EAAG,EAAG,GAChBM,CAAAA,EAAAA,EAAAA,CAAAA,CAAWA,CAACrL,EAAMI,EAC3B,CAEA,IAAMgC,EACF,QAASgQ,GAAUA,EAAN,IAAYjO,GAAG,CAAsB,EAAIuF,EAAMtH,IAAI,CAA3BsH,EAAMtH,IAAI,CAGnD,OAFApC,EAAKwL,WAAW,CAACpJ,EAAM,EAAGhC,EAAQwI,qBAAqB,EACvD5I,EAAK+K,QAAQ,CAAC,EAAG,EAAG,EAAG,GAChBM,CAAAA,EAAAA,EAAAA,CAAAA,CAAWA,CAACrL,EAAMI,EAC3B,mBAlDK,iBACLkS,QAAAA,CAAW,SAmDXe,kBAAAA,CAAqB,CACnB,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACD,CACH,eCpEO,OAAM+C,UAA0BtD,EAGrCE,IAH2CF,EAGrC5F,CAAU,CAAEjL,CAAK,CAAE,OACT,KAAK,CAAfA,EACKqT,EAAmB,EAAGpI,GAGxBoI,EAAmBrT,EAAMI,MAAM,CAHXiT,EAI7B,CAEAnD,IAAInS,CAAI,CAAEqW,CAAM,CAAE3M,CAAK,CAAE,CACvB,IAAM4M,EAAkBtL,CAAAA,EAAAA,EAAAA,CAAAA,CAAaA,CAAChL,EAAM,GAG5C,OAFAsW,EAAgB9K,WAAW,CAAC9B,EAAO,EAAG,GACtC4M,EAAgBvL,QAAQ,CAAC,EAAG,EAAG,EAAG,GAC3BmG,CAAAA,EAAAA,EAAAA,CAAAA,CAAcA,CAACoF,EACxB,mBAhBK,iBACLhE,QAAAA,CAAW,SAiBXe,kBAAAA,CAAqB,CACnB,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACD,CACH,CCtCO,MAAMkD,UAA2BzD,EAGtCE,IAH4CF,EAGtC5F,CAAU,CAAEjL,CAAK,CAAE,OACT,KAAK,CAAfA,EACKqT,EAAmB,EAAGpI,GAGxBoI,EAAmBrT,EAAMI,MAAM,CAHXiT,EAI7B,CAEAnD,IAH2BmD,CAGnB,CAAEe,CAAM,CAAE3M,CAAK,CAAE,CAGvB,OAFA1J,EAAKwL,WAAW,CAAC9B,EAAO,EAAG,GAC3B1J,EAAK+K,QAAQ,CAAC,EAAG,EAAG,EAAG,GAChB/K,CACT,mBAfK,iBACLsS,QAAAA,CAAW,SAgBXe,kBAAAA,CAAqB,CAAC,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAI,CAC9E,CClBO,MAAMmD,UAAsB1D,EAGjCE,IAHuCF,EAGjC5F,CAAU,CAAEjL,CAAK,CAAEgH,CAAK,CAAE,CAC9B,OAAQhH,GAEN,IAAK,IACL,IAAK,KACH,OAAOmT,EAAanT,EAAMI,MAAM,CAAE6K,CAAfkI,CAErB,KAAK,KACH,OAAOnM,EAAM5E,aAAa,CAAC6I,EAAY,CAAE5I,KAAM,SAAU,EAE3D,KAAK,MACH,OACE2E,EAAMjE,OAAO,CAACkI,EAAY,CACxB9I,MAAO,cACPc,QAAS,YACX,IACA+D,EAAMjE,OAAO,CAACkI,EAAY,CACxB9I,MAAO,SACPc,QAAS,YACX,EAIJ,KAAK,QACH,OAAO+D,EAAMjE,OAAO,CAACkI,EAAY,CAC/B9I,MAAO,SACPc,QAAS,YACX,EAEF,KAAK,IAEH,OACE+D,EAAMjE,OAAO,CAACkI,EAAY,CACxB9I,MAAO,OACPc,QAAS,YACX,IACA+D,EAAMjE,OAAO,CAACkI,EAAY,CACxB9I,MAAO,cACPc,QAAS,YACX,IACA+D,EAAMjE,OAAO,CAACkI,EAAY,CACxB9I,MAAO,SACPc,QAAS,YACX,EAEN,CACF,CAEA2M,SAASjM,CAAK,CAAE8D,CAAK,CAAE,CACrB,OAAOA,GAAS,GAAKA,GAAS,CAChC,CAEAyI,IAAInS,CAAI,CAAEqW,CAAM,CAAE3M,CAAK,CAAE,CAGvB,OAFA1J,EAAKsQ,QAAQ,CAAC,CAAC5G,GAAQ,EAAK,EAAG,GAC/B1J,EAAK+K,QAAQ,CAAC,EAAG,EAAG,EAAG,GAChB/K,CACT,mBA3DK,iBACLsS,QAAAA,CAAW,SA4DXe,kBAAAA,CAAqB,CACnB,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACD,CACH,CC7EO,MAAMoD,UAAgC3D,EAG3CE,IAHiDF,EAG3C5F,CAAU,CAAEjL,CAAK,CAAEgH,CAAK,CAAE,CAC9B,OAAQhH,GAEN,IAAK,IACL,IAAK,KACH,OAAOmT,EAAanT,EAAMI,MAAM,CAAE6K,CAAfkI,CAErB,KAAK,KACH,OAAOnM,EAAM5E,aAAa,CAAC6I,EAAY,CAAE5I,KAAM,SAAU,EAE3D,KAAK,MACH,OACE2E,EAAMjE,OAAO,CAACkI,EAAY,CACxB9I,MAAO,cACPc,QAAS,YACX,IACA+D,EAAMjE,OAAO,CAACkI,EAAY,CACxB9I,MAAO,SACPc,QAAS,YACX,EAIJ,KAAK,QACH,OAAO+D,EAAMjE,OAAO,CAACkI,EAAY,CAC/B9I,MAAO,SACPc,QAAS,YACX,EAEF,KAAK,IAEH,OACE+D,EAAMjE,OAAO,CAACkI,EAAY,CACxB9I,MAAO,OACPc,QAAS,YACX,IACA+D,EAAMjE,OAAO,CAACkI,EAAY,CACxB9I,MAAO,cACPc,QAAS,YACX,IACA+D,EAAMjE,OAAO,CAACkI,EAAY,CACxB9I,MAAO,SACPc,QAAS,YACX,EAEN,CACF,CAEA2M,SAASjM,CAAK,CAAE8D,CAAK,CAAE,CACrB,OAAOA,GAAS,GAAKA,GAAS,CAChC,CAEAyI,IAAInS,CAAI,CAAEqW,CAAM,CAAE3M,CAAK,CAAE,CAGvB,OAFA1J,EAAKsQ,QAAQ,CAAC,CAAC5G,GAAQ,EAAK,EAAG,GAC/B1J,EAAK+K,QAAQ,CAAC,EAAG,EAAG,EAAG,GAChB/K,CACT,mBA3DK,iBACLsS,QAAAA,CAAW,SA4DXe,kBAAAA,CAAqB,CACnB,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACD,CACH,CC5EO,MAAMqD,UAAoB5D,EAmB/BE,IAnBqCF,EAmB/B5F,CAAU,CAAEjL,CAAK,CAAEgH,CAAK,CAAE,CAC9B,IAAM+M,EAAgB,GAAWtM,EAAQ,EAEzC,OAAQzH,GAEN,IAAK,IACH,OAAO0S,EACLG,EAAoBxB,EAAgB/Q,EADvBoS,GAC4B,CAAEzH,GAC3C8I,EAGJ,EAJuC1C,EAAhBwB,CAIlB,KACH,OAAOH,EAASS,EAAa,EAAGlI,EAAjByH,CAA8BqB,EAE/C,GAF8BZ,EAEzB,KACH,OAAOT,EACL1L,EAAM5E,IADOsQ,SACM,CAACzH,EAAY,CAC9B5I,KAAM,OACR,GACA0R,EAGJ,KAAK,MACH,OACE/M,EAAM1G,KAAK,CAAC2K,EAAY,CACtB9I,MAAO,cACPc,QAAS,YACX,IACA+D,EAAM1G,KAAK,CAAC2K,EAAY,CAAE9I,MAAO,SAAUc,QAAS,YAAa,EAIrE,KAAK,QACH,OAAO+D,EAAM1G,KAAK,CAAC2K,EAAY,CAC7B9I,MAAO,SACPc,QAAS,YACX,EAEF,KAAK,IAEH,OACE+D,EAAM1G,KAAK,CAAC2K,EAAY,CAAE9I,MAAO,OAAQc,QAAS,YAAa,IAC/D+D,EAAM1G,KAAK,CAAC2K,EAAY,CACtB9I,MAAO,cACPc,QAAS,YACX,IACA+D,EAAM1G,KAAK,CAAC2K,EAAY,CAAE9I,MAAO,SAAUc,QAAS,YAAa,EAEvE,CACF,CAEA2M,SAASjM,CAAK,CAAE8D,CAAK,CAAE,CACrB,OAAOA,GAAS,GAAKA,GAAS,EAChC,CAEAyI,IAAInS,CAAI,CAAEqW,CAAM,CAAE3M,CAAK,CAAE,CAGvB,OAFA1J,EAAKsQ,QAAQ,CAAC5G,EAAO,GACrB1J,EAAK+K,QAAQ,CAAC,EAAG,EAAG,EAAG,GAChB/K,CACT,mBA9EK,iBACLqT,kBAAAA,CAAqB,CACnB,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACD,MAEDf,QAAAA,CAAW,IA8Db,CC/EO,MAAMqE,UAA8B7D,EAGzCE,IAH+CF,EAGzC5F,CAAU,CAAEjL,CAAK,CAAEgH,CAAK,CAAE,CAC9B,IAAM+M,EAAgB,GAAWtM,EAAQ,EAEzC,OAAQzH,GAEN,IAAK,IACH,OAAO0S,EACLG,EAAoBxB,EAAgB/Q,EADvBoS,GAC4B,CAAEzH,GAC3C8I,EAGJ,EAJuC1C,EAAhBwB,CAIlB,KACH,OAAOH,EAASS,EAAa,EAAGlI,EAAjByH,CAA8BqB,EAE/C,GAF8BZ,EAEzB,KACH,OAAOT,EACL1L,EAAM5E,IADOsQ,SACM,CAACzH,EAAY,CAC9B5I,KAAM,OACR,GACA0R,EAGJ,KAAK,MACH,OACE/M,EAAM1G,KAAK,CAAC2K,EAAY,CACtB9I,MAAO,cACPc,QAAS,YACX,IACA+D,EAAM1G,KAAK,CAAC2K,EAAY,CAAE9I,MAAO,SAAUc,QAAS,YAAa,EAIrE,KAAK,QACH,OAAO+D,EAAM1G,KAAK,CAAC2K,EAAY,CAC7B9I,MAAO,SACPc,QAAS,YACX,EAEF,KAAK,IAEH,OACE+D,EAAM1G,KAAK,CAAC2K,EAAY,CAAE9I,MAAO,OAAQc,QAAS,YAAa,IAC/D+D,EAAM1G,KAAK,CAAC2K,EAAY,CACtB9I,MAAO,cACPc,QAAS,YACX,IACA+D,EAAM1G,KAAK,CAAC2K,EAAY,CAAE9I,MAAO,SAAUc,QAAS,YAAa,EAEvE,CACF,CAEA2M,SAASjM,CAAK,CAAE8D,CAAK,CAAE,CACrB,OAAOA,GAAS,GAAKA,GAAS,EAChC,CAEAyI,IAAInS,CAAI,CAAEqW,CAAM,CAAE3M,CAAK,CAAE,CAGvB,OAFA1J,EAAKsQ,QAAQ,CAAC5G,EAAO,GACrB1J,EAAK+K,QAAQ,CAAC,EAAG,EAAG,EAAG,GAChB/K,CACT,mBA9DK,iBACLsS,QAAAA,CAAW,SA+DXe,kBAAAA,CAAqB,CACnB,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACD,CACH,eE5EO,OAAMuD,UAAwB9D,EAGnCE,IAHyCF,EAGnC5F,CAAU,CAAEjL,CAAK,CAAEgH,CAAK,CAAE,CAC9B,OAAQhH,GACN,IAAK,IACH,OAAO6S,EAAoBxB,EAAgBhO,IAAI,CAAE4H,EACnD,KAAK,CADuCoG,EAAhBwB,EAE1B,OAAO7L,EAAM5E,aAAa,CAAC6I,EAAY,CAAE5I,KAAM,MAAO,EACxD,SACE,OAAO8Q,EAAanT,EAAMI,MAAM,CAAE6K,CAAfkI,CACvB,CACF,CAEAvD,SAASjM,CAAK,CAAE8D,CAAK,CAAE,CACrB,OAAOA,GAAS,GAAKA,GAAS,EAChC,CAEAyI,IAAInS,CAAI,CAAEqW,CAAM,CAAE3M,CAAK,CAAEtJ,CAAO,CAAE,CAChC,MAAOiL,CAAAA,EAAAA,EAAAA,CAAAA,CAAWA,CDkBf,OClBuBwL,EDkBdA,CAAY,CAAEvR,CAAI,CAAElF,CAAO,EACzC,IAAM8L,EAAQjM,CAAAA,EAAAA,EAAAA,CAAAA,CAAMA,CAACD,EAAMI,QAAAA,KAAAA,EAAAA,EAASE,EAAE,CAAXF,CACrByF,EAAON,CAAAA,EAAAA,EAAAA,CAAAA,CAAOA,CAAC2G,EAAO9L,GAAWkF,EAEvC,OADA4G,EAAMK,OAAO,CAACL,EAAMjL,OAAO,GAAY,EAAP4E,GACzB5F,CAAAA,EAAAA,EAAAA,CAAAA,CAAMA,CAACiM,QAAO9L,EAAAA,KAAAA,EAAAA,EAASE,EAAE,CAClC,ECvB+BN,EAAM0J,EDsBdtJ,GCtB+BA,EACpD,mBApBK,iBACLkS,QAAAA,CAAW,SAqBXe,kBAAAA,CAAqB,CACnB,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACD,CACH,eErCO,OAAMyD,UAAsBhE,EAGjCE,IAHuCF,EAGjC5F,CAAU,CAAEjL,CAAK,CAAEgH,CAAK,CAAE,CAC9B,OAAQhH,GACN,IAAK,IACH,OAAO6S,EAAoBxB,EAAgBhO,IAAI,CAAE4H,EACnD,KAAK,CADuCoG,EAAhBwB,EAE1B,OAAO7L,EAAM5E,aAAa,CAAC6I,EAAY,CAAE5I,KAAM,MAAO,EACxD,SACE,OAAO8Q,EAAanT,EAAMI,MAAM,CAAE6K,CAAfkI,CACvB,CACF,CAEAvD,SAASjM,CAAK,CAAE8D,CAAK,CAAE,CACrB,OAAOA,GAAS,GAAKA,GAAS,EAChC,CAEAyI,IAAInS,CAAI,CAAEqW,CAAM,CAAE3M,CAAK,CAAE,CACvB,MAAOwH,CAAAA,EAAAA,EAAAA,CAAAA,CAAcA,CDIlB,SAAS6F,CCJoBA,CDIHzR,CAAI,CAAElF,CAAO,EAC5C,IAAMwF,EAAQ3F,CAAAA,EAAAA,EAAAA,CAAAA,CAAMA,CAACD,EAAMI,IAAAA,CAAAA,GACrByF,CADqBzF,CACdqF,CAAAA,EAAAA,EAAAA,CAAAA,CAAUA,CAACG,CADc,KAAXxF,CACIA,GAAWkF,EAE1C,OADAM,EAAM2G,OAAO,CAAC3G,EAAM3E,OAAO,GAAY,EAAP4E,GACzBD,CACT,ECTqC5F,EAAM0J,GACzC,mBApBK,iBACL4I,QAAAA,CAAW,SAqBXe,kBAAAA,CAAqB,CACnB,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACD,CACH,CCrCA,IAAM2D,EAAgB,CAAC,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAG,CAChEC,EAA0B,CAC9B,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAAI,GAC7C,OAGYC,UAAmBpE,EAI9BE,IAJoCF,EAI9B5F,CAAU,CAAEjL,CAAK,CAAEgH,CAAK,CAAE,CAC9B,OAAQhH,GACN,IAAK,IACH,OAAO6S,EAAoBxB,EAAgBtT,IAAI,CAAEkN,EACnD,KAAK,CADuCoG,EAAhBwB,EAE1B,OAAO7L,EAAM5E,aAAa,CAAC6I,EAAY,CAAE5I,KAAM,MAAO,EACxD,SACE,OAAO8Q,EAAanT,EAAMI,MAAM,CAAE6K,CAAfkI,CACvB,CACF,CAEAvD,SAAS7R,CAAI,CAAE0J,CAAK,CAAE,CAEpB,IAAMyN,EAAalI,EADNjP,EAAKmC,WAAW,CACMC,GAC7BG,EAAQvC,EAAKwC,QAAQ,UAC3B,EACSkH,GAAS,GAAKA,GAASuN,CADhB,CACwC1U,EAAM,CAErDmH,GAAS,GAAKA,GAASsN,CAAa,CAACzU,EAEhD,CAEA4P,IAAInS,CAAI,CAAEqW,CAAM,CAAE3M,CAAK,CAAE,CAGvB,OAFA1J,EAAKuM,OAAO,CAAC7C,GACb1J,EAAK+K,QAAQ,CAAC,EAAG,EAAG,EAAG,GAChB/K,CACT,mBA9BK,iBACLsS,QAAAA,CAAW,QACXN,WAAAA,CAAc,OA8BdqB,kBAAAA,CAAqB,CACnB,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACD,CACH,CCpDO,MAAM+D,UAAwBtE,EAKnCE,IALyCF,EAKnC5F,CAAU,CAAEjL,CAAK,CAAEgH,CAAK,CAAE,CAC9B,OAAQhH,GACN,IAAK,IACL,IAAK,KACH,OAAO6S,EAAoBxB,EAAgB3N,SAAS,CAAEuH,EACxD,CAD4CoG,EAAhBwB,EACvB,KACH,OAAO7L,EAAM5E,aAAa,CAAC6I,EAAY,CAAE5I,KAAM,MAAO,EACxD,SACE,OAAO8Q,EAAanT,EAAMI,MAAM,CAAE6K,CAAfkI,CACvB,CACF,CAEAvD,SAAS7R,CAAI,CAAE0J,CAAK,CAAE,QAEDuF,EADNjP,EAAKmC,WAAW,CACMC,GAE1BsH,GAAS,GAAKA,GAAS,IAEvBA,GAAS,GAAKA,GAAS,GAElC,CAEAyI,IAAInS,CAAI,CAAEqW,CAAM,CAAE3M,CAAK,CAAE,CAGvB,OAFA1J,EAAKsQ,QAAQ,CAAC,EAAG5G,GACjB1J,EAAK+K,QAAQ,CAAC,EAAG,EAAG,EAAG,GAChB/K,CACT,mBA/BK,iBACLsS,QAAAA,CAAW,QAEX+E,WAAAA,CAAc,OA8BdhE,kBAAAA,CAAqB,CACnB,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACD,CACH,gBCxBO,SAASiE,EAAOtX,CAAI,CAAEmG,CAAG,CAAE/F,CAAO,MAIrCA,EAAAA,EAEAoI,EAAAA,EAHApI,EAAAA,EAAAA,EAAAA,EAFF,IAAMoI,EAAiBC,CAAAA,EAAAA,EAAAA,CAAAA,CAAiBA,GAClCnC,EACJlG,OAAAA,EAAAA,OAAAA,EAAAA,OAAAA,EAAAA,OAAAA,EAAAA,QAAAA,KAAAA,EAAAA,EAASkG,GAATlG,SAASkG,EAATlG,QACAA,GAAAA,CADAA,MACAA,EAAAA,EAASsI,MAATtI,GAAAA,MAAAA,GAAAA,EAAiBA,OAAAA,EAAjBA,KAAAA,EAAAA,EAA0BkG,GAA1BlG,SAA0BkG,EAD1BlG,EAEAoI,EAAelC,QAFflG,IAEekG,EAFflG,SAGAoI,EAAAA,CAHApI,CAGesI,MAAAA,GAAfF,OAAAA,EAAAA,EAAuBpI,OAAAA,EAAvBoI,KAAAA,EAAAA,EAAgClC,GAAhCkC,SAAgClC,EAHhClG,EAIA,EAEI8L,EAAQjM,CAAAA,EAAAA,EAAAA,CANZG,CAMkBH,CAACD,QAAMI,EAAAA,KAAAA,EAAAA,EAASE,EAAE,EAChCiX,EAAarL,EAAMhG,CADE9F,KACI,GAKzBoX,EAAQ,EAAIlR,EACZT,EACJM,EAAM,GAAKA,EAAM,EACbA,EAAO,CAACoR,EAAaC,CAAAA,CAAI,CAAK,EAC9B,EAPYrR,EAAM,GACM,EAAK,EAMhBqR,CAAAA,CAAI,CAAK,EAAM,CAACD,EAAaC,CAAAA,CAAI,CAAK,EACzD,MAAO5G,CAAAA,EAAAA,EAAAA,CAAAA,CAAOA,CAAC1E,EAAOrG,EAAMzF,EAC9B,CCpDO,MAAMqX,UAAkB3E,EAG7BE,IAHmCF,EAG7B5F,CAAU,CAAEjL,CAAK,CAAEgH,CAAK,CAAE,CAC9B,OAAQhH,GAEN,IAAK,IACL,IAAK,KACL,IAAK,MACH,OACEgH,EAAM9C,GAAG,CAAC+G,EAAY,CACpB9I,MAAO,cACPc,QAAS,YACX,IACA+D,EAAM9C,GAAG,CAAC+G,EAAY,CAAE9I,MAAO,QAASc,QAAS,YAAa,IAC9D+D,EAAM9C,GAAG,CAAC+G,EAAY,CAAE9I,MAAO,SAAUc,QAAS,YAAa,EAInE,KAAK,QACH,OAAO+D,EAAM9C,GAAG,CAAC+G,EAAY,CAC3B9I,MAAO,SACPc,QAAS,YACX,EAEF,KAAK,SACH,OACE+D,EAAM9C,GAAG,CAAC+G,EAAY,CAAE9I,MAAO,QAASc,QAAS,YAAa,IAC9D+D,EAAM9C,GAAG,CAAC+G,EAAY,CAAE9I,MAAO,SAAUc,QAAS,YAAa,EAInE,KAAK,IAEH,OACE+D,EAAM9C,GAAG,CAAC+G,EAAY,CAAE9I,MAAO,OAAQc,QAAS,YAAa,IAC7D+D,EAAM9C,GAAG,CAAC+G,EAAY,CACpB9I,MAAO,cACPc,QAAS,YACX,IACA+D,EAAM9C,GAAG,CAAC+G,EAAY,CAAE9I,MAAO,QAASc,QAAS,YAAa,IAC9D+D,EAAM9C,GAAG,CAAC+G,EAAY,CAAE9I,MAAO,SAAUc,QAAS,YAAa,EAErE,CACF,CAEA2M,SAASjM,CAAK,CAAE8D,CAAK,CAAE,CACrB,OAAOA,GAAS,GAAKA,GAAS,CAChC,CAEAyI,IAAInS,CAAI,CAAEqW,CAAM,CAAE3M,CAAK,CAAEtJ,CAAO,CAAE,CAGhC,MADAJ,CADAA,EAAOsX,EAAOtX,EAAM0J,EAAOtJ,EAAAA,EACtB2K,QAAQ,CAAC,EAAG,EAAG,EAAG,GAChB/K,CACT,mBAtDK,iBACLsS,QAAAA,CAAW,QAuDXe,kBAAAA,CAAqB,CAAC,IAAK,IAAK,IAAK,IAAK,IAAK,IAAI,CACrD,CCvDO,MAAMqE,UAAuB5E,EAElCE,IAFwCF,EAElC5F,CAAU,CAAEjL,CAAK,CAAEgH,CAAK,CAAE7I,CAAO,CAAE,CACvC,IAAM4V,EAAgB,IAEpB,IAAM2B,EAA8C,EAA9BjW,KAAKkW,KAAK,CAAC,IAAS,EAAK,GAC/C,MAASlO,GAAQtJ,EAAQkG,YAAY,EAAG,EAAK,EAAKqR,CACpD,EAEA,OAAQ1V,GAEN,IAAK,IACL,IAAK,KACH,OAAO0S,EAASS,EAAanT,EAAMI,EAApBsS,IAA0B,CAAEzH,CAAfkI,EAA4BY,EAE1D,KAAK,KACH,OAAOrB,EACL1L,EAAM5E,IADOsQ,SACM,CAACzH,EAAY,CAC9B5I,KAAM,KACR,GACA0R,EAGJ,KAAK,MACH,OACE/M,EAAM9C,GAAG,CAAC+G,EAAY,CACpB9I,MAAO,cACPc,QAAS,YACX,IACA+D,EAAM9C,GAAG,CAAC+G,EAAY,CAAE9I,MAAO,QAASc,QAAS,YAAa,IAC9D+D,EAAM9C,GAAG,CAAC+G,EAAY,CAAE9I,MAAO,SAAUc,QAAS,YAAa,EAInE,KAAK,QACH,OAAO+D,EAAM9C,GAAG,CAAC+G,EAAY,CAC3B9I,MAAO,SACPc,QAAS,YACX,EAEF,KAAK,SACH,OACE+D,EAAM9C,GAAG,CAAC+G,EAAY,CAAE9I,MAAO,QAASc,QAAS,YAAa,IAC9D+D,EAAM9C,GAAG,CAAC+G,EAAY,CAAE9I,MAAO,SAAUc,QAAS,YAAa,EAInE,KAAK,IAEH,OACE+D,EAAM9C,GAAG,CAAC+G,EAAY,CAAE9I,MAAO,OAAQc,QAAS,YAAa,IAC7D+D,EAAM9C,GAAG,CAAC+G,EAAY,CACpB9I,MAAO,cACPc,QAAS,YACX,IACA+D,EAAM9C,GAAG,CAAC+G,EAAY,CAAE9I,MAAO,QAASc,QAAS,YAAa,IAC9D+D,EAAM9C,GAAG,CAAC+G,EAAY,CAAE9I,MAAO,SAAUc,QAAS,YAAa,EAErE,CACF,CAEA2M,SAASjM,CAAK,CAAE8D,CAAK,CAAE,CACrB,OAAOA,GAAS,GAAKA,GAAS,CAChC,CAEAyI,IAAInS,CAAI,CAAEqW,CAAM,CAAE3M,CAAK,CAAEtJ,CAAO,CAAE,CAGhC,MADAJ,CADAA,EAAOsX,EAAOtX,EAAM0J,EAAOtJ,EAAAA,EACtB2K,QAAQ,CAAC,EAAG,EAAG,EAAG,GAChB/K,CACT,mBArEK,iBACLsS,QAAAA,CAAW,QAsEXe,kBAAAA,CAAqB,CACnB,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACD,CACH,CCxFO,MAAMwE,UAAiC/E,EAG5CE,IAHkDF,EAG5C5F,CAAU,CAAEjL,CAAK,CAAEgH,CAAK,CAAE7I,CAAO,CAAE,CACvC,IAAM4V,EAAgB,IAEpB,IAAM2B,EAA8C,EAA9BjW,KAAKkW,KAAK,CAAC,CAAClO,GAAQ,EAAK,GAC/C,MAAO,CAAEA,EAAQtJ,EAAQkG,YAAY,CAAG,GAAK,EAAKqR,CACpD,EAEA,OAAQ1V,GAEN,IAAK,IACL,IAAK,KACH,OAAO0S,EAASS,EAAanT,EAAMI,EAApBsS,IAA0B,CAAEzH,CAAfkI,EAA4BY,EAE1D,KAAK,KACH,OAAOrB,EACL1L,EAAM5E,IADOsQ,SACM,CAACzH,EAAY,CAC9B5I,KAAM,KACR,GACA0R,EAGJ,KAAK,MACH,OACE/M,EAAM9C,GAAG,CAAC+G,EAAY,CACpB9I,MAAO,cACPc,QAAS,YACX,IACA+D,EAAM9C,GAAG,CAAC+G,EAAY,CAAE9I,MAAO,QAASc,QAAS,YAAa,IAC9D+D,EAAM9C,GAAG,CAAC+G,EAAY,CAAE9I,MAAO,SAAUc,QAAS,YAAa,EAInE,KAAK,QACH,OAAO+D,EAAM9C,GAAG,CAAC+G,EAAY,CAC3B9I,MAAO,SACPc,QAAS,YACX,EAEF,KAAK,SACH,OACE+D,EAAM9C,GAAG,CAAC+G,EAAY,CAAE9I,MAAO,QAASc,QAAS,YAAa,IAC9D+D,EAAM9C,GAAG,CAAC+G,EAAY,CAAE9I,MAAO,SAAUc,QAAS,YAAa,EAInE,KAAK,IAEH,OACE+D,EAAM9C,GAAG,CAAC+G,EAAY,CAAE9I,MAAO,OAAQc,QAAS,YAAa,IAC7D+D,EAAM9C,GAAG,CAAC+G,EAAY,CACpB9I,MAAO,cACPc,QAAS,YACX,IACA+D,EAAM9C,GAAG,CAAC+G,EAAY,CAAE9I,MAAO,QAASc,QAAS,YAAa,IAC9D+D,EAAM9C,GAAG,CAAC+G,EAAY,CAAE9I,MAAO,SAAUc,QAAS,YAAa,EAErE,CACF,CAEA2M,SAASjM,CAAK,CAAE8D,CAAK,CAAE,CACrB,OAAOA,GAAS,GAAKA,GAAS,CAChC,CAEAyI,IAAInS,CAAI,CAAEqW,CAAM,CAAE3M,CAAK,CAAEtJ,CAAO,CAAE,CAGhC,MADAJ,CADAA,EAAOsX,EAAOtX,EAAM0J,EAAP4N,EAAclX,EACtB2K,QAAQ,CAAC,EAAG,EAAG,EAAG,GAChB/K,CACT,mBAtEK,iBACLsS,QAAAA,CAAW,QAuEXe,kBAAAA,CAAqB,CACnB,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACD,CACH,CGzFO,MAAMyE,UAAqBhF,EAGhCE,IAHsCF,EAGhC5F,CAAU,CAAEjL,CAAK,CAAEgH,CAAK,CAAE,CAC9B,IAAM+M,EAAgB,GACpB,GAAiB,CAAbtM,EACK,EAEFA,EAGT,OAAQzH,GAEN,IAAK,IACL,IAAK,KACH,OAAOmT,EAAanT,EAAMI,MAAM,CAAE6K,CAAfkI,CAErB,KAAK,KACH,OAAOnM,EAAM5E,aAAa,CAAC6I,EAAY,CAAE5I,KAAM,KAAM,EAEvD,KAAK,MACH,OAAOqQ,EACL1L,EAAM9C,GAAG,CADIwO,EACS,CACpBvQ,MAAO,cACPc,QAAS,YACX,IACE+D,EAAM9C,GAAG,CAAC+G,EAAY,CACpB9I,MAAO,QACPc,QAAS,YACX,IACA+D,EAAM9C,GAAG,CAAC+G,EAAY,CACpB9I,MAAO,SACPc,QAAS,YACX,GACF8Q,EAGJ,KAAK,QACH,OAAOrB,EACL1L,EAAM9C,GAAG,CADIwO,EACS,CACpBvQ,MAAO,SACPc,QAAS,YACX,GACA8Q,EAGJ,KAAK,SACH,OAAOrB,EACL1L,EAAM9C,GAAG,CADIwO,EACS,CACpBvQ,MAAO,QACPc,QAAS,YACX,IACE+D,EAAM9C,GAAG,CAAC+G,EAAY,CACpB9I,MAAO,SACPc,QAAS,YACX,GACF8Q,EAGJ,KAAK,IAEH,OAAOrB,EACL1L,EAAM9C,GAAG,CADIwO,EACS,CACpBvQ,MAAO,OACPc,QAAS,YACX,IACE+D,EAAM9C,GAAG,CAAC+G,EAAY,CACpB9I,MAAO,cACPc,QAAS,YACX,IACA+D,EAAM9C,GAAG,CAAC+G,EAAY,CACpB9I,MAAO,QACPc,QAAS,YACX,IACA+D,EAAM9C,GAAG,CAAC+G,EAAY,CACpB9I,MAAO,SACPc,QAAS,YACX,GACF8Q,EAEN,CACF,CAEAnE,SAASjM,CAAK,CAAE8D,CAAK,CAAE,CACrB,OAAOA,GAAS,GAAKA,GAAS,CAChC,CAEAyI,IAAInS,CAAI,CAAEqW,CAAM,CAAE3M,CAAK,CAAE,CAGvB,MADA1J,CADAA,ED9DG,SAAS+X,CAAc,CAAE5R,CAAG,EAAS,EAC1C,IAAM+F,EAAQjM,CAAAA,EAAAA,EAAAA,CAAAA,CAAMA,CAACD,EAAMI,IAAAA,CAAAA,GACrBmX,CADqBnX,CDNtB,OCM+BE,EAAE,CDNV,CAAEF,CAAO,ECMVA,IDLrB+F,EAAMlG,CAAAA,EAAAA,EAAAA,CAAAA,CAAMA,CAACD,QAAMI,EAAAA,KAAAA,EAAAA,EAASE,EAAE,EAAE4F,KAAb9F,CAAmB,GAC5C,OAAe,IAAR+F,EAAY,EAAIA,CACzB,ECI+B+F,OAFM9L,GAInC,MAAOwQ,CAAAA,EAAAA,EAAAA,CAAAA,CAAOA,CAAC1E,EADF/F,EAAMoR,EACSnX,CAANyF,CACxB,ECyDqB7F,EAAM0J,EAAAA,EAClBqB,QAAQ,CAAC,EAAG,EAAG,EAAG,GAChB/K,CACT,mBA3FK,iBACLsS,QAAAA,CAAW,QA4FXe,kBAAAA,CAAqB,CACnB,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACA,IACD,CACH,CChHO,MAAM2E,UAAmBlF,EAG9BE,IAHoCF,EAG9B5F,CAAU,CAAEjL,CAAK,CAAEgH,CAAK,CAAE,CAC9B,OAAQhH,GACN,IAAK,IACL,IAAK,KACL,IAAK,MACH,OACEgH,EAAMvC,SAAS,CAACwG,EAAY,CAC1B9I,MAAO,cACPc,QAAS,YACX,IACA+D,EAAMvC,SAAS,CAACwG,EAAY,CAC1B9I,MAAO,SACPc,QAAS,YACX,EAGJ,KAAK,QACH,OAAO+D,EAAMvC,SAAS,CAACwG,EAAY,CACjC9I,MAAO,SACPc,QAAS,YACX,EACF,KAAK,IAEH,OACE+D,EAAMvC,SAAS,CAACwG,EAAY,CAC1B9I,MAAO,OACPc,QAAS,YACX,IACA+D,EAAMvC,SAAS,CAACwG,EAAY,CAC1B9I,MAAO,cACPc,QAAS,YACX,IACA+D,EAAMvC,SAAS,CAACwG,EAAY,CAC1B9I,MAAO,SACPc,QAAS,YACX,EAEN,CACF,CAEAiN,IAAInS,CAAI,CAAEqW,CAAM,CAAE3M,CAAK,CAAE,CAEvB,OADA1J,EAAK+K,QAAQ,CAACwK,EAAqB7L,GAAQ,EAAG,EAAG,GAC1C1J,CACT,OAFoCuV,YA5C/B,iBACLjD,QAAAA,CAAW,QA+CXe,kBAAAA,CAAqB,CAAC,IAAK,IAAK,IAAK,IAAK,IAAK,IAAI,CACrD,CCjDO,MAAM4E,UAA2BnF,EAGtCE,IAH4CF,EAGtC5F,CAAU,CAAEjL,CAAK,CAAEgH,CAAK,CAAE,CAC9B,OAAQhH,GACN,IAAK,IACL,IAAK,KACL,IAAK,MACH,OACEgH,EAAMvC,SAAS,CAACwG,EAAY,CAC1B9I,MAAO,cACPc,QAAS,YACX,IACA+D,EAAMvC,SAAS,CAACwG,EAAY,CAC1B9I,MAAO,SACPc,QAAS,YACX,EAGJ,KAAK,QACH,OAAO+D,EAAMvC,SAAS,CAACwG,EAAY,CACjC9I,MAAO,SACPc,QAAS,YACX,EACF,KAAK,IAEH,OACE+D,EAAMvC,SAAS,CAACwG,EAAY,CAC1B9I,MAAO,OACPc,QAAS,YACX,IACA+D,EAAMvC,SAAS,CAACwG,EAAY,CAC1B9I,MAAO,cACPc,QAAS,YACX,IACA+D,EAAMvC,SAAS,CAACwG,EAAY,CAC1B9I,MAAO,SACPc,QAAS,YACX,EAEN,CACF,CAEAiN,IAAInS,CAAI,CAAEqW,CAAM,CAAE3M,CAAK,CAAE,CAEvB,OADA1J,EAAK+K,QAAQ,CAACwK,EAAqB7L,GAAQ,EAAG,EAAG,GAC1C1J,CACT,OAFoCuV,YA5C/B,iBACLjD,QAAAA,CAAW,QA+CXe,kBAAAA,CAAqB,CAAC,IAAK,IAAK,IAAK,IAAK,IAAK,IAAI,CACrD,CChDO,MAAM6E,UAAwBpF,EAGnCE,IAHyCF,EAGnC5F,CAAU,CAAEjL,CAAK,CAAEgH,CAAK,CAAE,CAC9B,OAAQhH,GACN,IAAK,IACL,IAAK,KACL,IAAK,MACH,OACEgH,EAAMvC,SAAS,CAACwG,EAAY,CAC1B9I,MAAO,cACPc,QAAS,YACX,IACA+D,EAAMvC,SAAS,CAACwG,EAAY,CAC1B9I,MAAO,SACPc,QAAS,YACX,EAGJ,KAAK,QACH,OAAO+D,EAAMvC,SAAS,CAACwG,EAAY,CACjC9I,MAAO,SACPc,QAAS,YACX,EACF,KAAK,IAEH,OACE+D,EAAMvC,SAAS,CAACwG,EAAY,CAC1B9I,MAAO,OACPc,QAAS,YACX,IACA+D,EAAMvC,SAAS,CAACwG,EAAY,CAC1B9I,MAAO,cACPc,QAAS,YACX,IACA+D,EAAMvC,SAAS,CAACwG,EAAY,CAC1B9I,MAAO,SACPc,QAAS,YACX,EAEN,CACF,CAEAiN,IAAInS,CAAI,CAAEqW,CAAM,CAAE3M,CAAK,CAAE,CAEvB,OADA1J,EAAK+K,QAAQ,CAACwK,EAAqB7L,GAAQ,EAAG,EAAG,GAC1C1J,CACT,OAFoCuV,YA5C/B,iBACLjD,QAAAA,CAAW,QA+CXe,kBAAAA,CAAqB,CAAC,IAAK,IAAK,IAAK,IAAI,CAC3C,CCjDO,MAAM8E,WAAwBrF,EAGnCE,IAHyCF,EAGnC5F,CAAU,CAAEjL,CAAK,CAAEgH,CAAK,CAAE,CAC9B,OAAQhH,GACN,IAAK,IACH,OAAO6S,EAAoBxB,EAAgBI,OAAO,CAAExG,EACtD,GAD4CoG,EACvC,KACH,OAAOrK,EAAM5E,aAAa,CAAC6I,EAAY,CAAE5I,KAAM,MAAO,EACxD,SACE,OAAO8Q,EAAanT,EAAMI,MAAM,CAAE6K,CAAfkI,CACvB,CACF,CAEAvD,SAASjM,CAAK,CAAE8D,CAAK,CAAE,CACrB,OAAOA,GAAS,GAAKA,GAAS,EAChC,CAEAyI,IAAInS,CAAI,CAAEqW,CAAM,CAAE3M,CAAK,CAAE,CACvB,IAAM0O,EAAOpY,EAAK4C,QAAQ,IAAM,GAQhC,OAPIwV,GAAQ1O,EAAQ,GAClB1J,CADsB,CACjB+K,QAAQ,CAACrB,EAAQ,GAAI,EAAG,EAAG,GACvB,GAAmB,IAAI,CAAdA,EAGlB1J,EAAK+K,QAAQ,CAACrB,EAAO,EAAG,EAAG,GAF3B1J,EAAK+K,QAAQ,CAAC,EAAG,EAAG,EAAG,GAIlB/K,CACT,mBA5BK,iBACLsS,QAAAA,CAAW,QA6BXe,kBAAAA,CAAqB,CAAC,IAAK,IAAK,IAAK,IAAK,IAAI,CAChD,CC/BO,MAAMgF,WAAwBvF,EAGnCE,IAHyCF,EAGnC5F,CAAU,CAAEjL,CAAK,CAAEgH,CAAK,CAAE,CAC9B,OAAQhH,GACN,IAAK,IACH,OAAO6S,EAAoBxB,EAAgBC,OAAO,CAAErG,EACtD,GAD4CoG,EAAhBwB,KAE1B,OAAO7L,EAAM5E,aAAa,CAAC6I,EAAY,CAAE5I,KAAM,MAAO,EACxD,SACE,OAAO8Q,EAAanT,EAAMI,MAAM,CAAE6K,CAAfkI,CACvB,CACF,CAEAvD,SAASjM,CAAK,CAAE8D,CAAK,CAAE,CACrB,OAAOA,GAAS,GAAKA,GAAS,EAChC,CAEAyI,IAAInS,CAAI,CAAEqW,CAAM,CAAE3M,CAAK,CAAE,CAEvB,OADA1J,EAAK+K,QAAQ,CAACrB,EAAO,EAAG,EAAG,GACpB1J,CACT,mBArBK,iBACLsS,QAAAA,CAAW,QAsBXe,kBAAAA,CAAqB,CAAC,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAI,CAC1D,CCxBO,MAAMiF,WAAwBxF,EAGnCE,IAHyCF,EAGnC5F,CAAU,CAAEjL,CAAK,CAAEgH,CAAK,CAAE,CAC9B,OAAQhH,GACN,IAAK,IACH,OAAO6S,EAAoBxB,EAAgBG,OAAO,CAAEvG,EACtD,GAD4CoG,EACvC,KACH,OAAOrK,EAAM5E,aAAa,CAAC6I,EAAY,CAAE5I,KAAM,MAAO,EACxD,SACE,OAAO8Q,EAAanT,EAAMI,MAAM,CAAE6K,CAAfkI,CACvB,CACF,CAEAvD,SAASjM,CAAK,CAAE8D,CAAK,CAAE,CACrB,OAAOA,GAAS,GAAKA,GAAS,EAChC,CAEAyI,IAAInS,CAAI,CAAEqW,CAAM,CAAE3M,CAAK,CAAE,CAOvB,OANa1J,EAAK4C,QAAQ,IAAM,IACpB8G,EAAQ,GAClB1J,CADsB,CACjB+K,QAAQ,CAACrB,EAAQ,GAAI,EAAG,EAAG,GAEhC1J,EAAK+K,QAAQ,CAACrB,EAAO,EAAG,EAAG,GAEtB1J,CACT,mBA1BK,iBACLsS,QAAAA,CAAW,QA2BXe,kBAAAA,CAAqB,CAAC,IAAK,IAAK,IAAK,IAAK,IAAI,CAChD,CC7BO,MAAMkF,WAAwBzF,EAGnCE,IAHyCF,EAGnC5F,CAAU,CAAEjL,CAAK,CAAEgH,CAAK,CAAE,CAC9B,OAAQhH,GACN,IAAK,IACH,OAAO6S,EAAoBxB,EAAgBE,OAAO,CAAEtG,EACtD,GAD4CoG,EACvC,KACH,OAAOrK,EAAM5E,aAAa,CAAC6I,EAAY,CAAE5I,KAAM,MAAO,EACxD,SACE,OAAO8Q,EAAanT,EAAMI,MAAM,CAAE6K,CAAfkI,CACvB,CACF,CAEAvD,SAASjM,CAAK,CAAE8D,CAAK,CAAE,CACrB,OAAOA,GAAS,GAAKA,GAAS,EAChC,CAEAyI,IAAInS,CAAI,CAAEqW,CAAM,CAAE3M,CAAK,CAAE,CAGvB,OADA1J,EAAK+K,QAAQ,CADCrB,GAAS,GAAKA,EAAQ,GAAKA,EACpB,EAAG,EAAG,GACpB1J,CACT,mBAtBK,iBACLsS,QAAAA,CAAW,QAuBXe,kBAAAA,CAAqB,CAAC,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAI,CAC1D,CCzBO,MAAMmF,WAAqB1F,EAGhCE,IAHsCF,EAGhC5F,CAAU,CAAEjL,CAAK,CAAEgH,CAAK,CAAE,CAC9B,OAAQhH,GACN,IAAK,IACH,OAAO6S,EAAoBxB,EAAgBK,MAAM,CAAEzG,EACrD,IAD4CoG,CACvC,CADuBwB,IAE1B,OAAO7L,EAAM5E,aAAa,CAAC6I,EAAY,CAAE5I,KAAM,QAAS,EAC1D,SACE,OAAO8Q,EAAanT,EAAMI,MAAM,CAAE6K,CAAfkI,CACvB,CACF,CAEAvD,SAASjM,CAAK,CAAE8D,CAAK,CAAE,CACrB,OAAOA,GAAS,GAAKA,GAAS,EAChC,CAEAyI,IAAInS,CAAI,CAAEqW,CAAM,CAAE3M,CAAK,CAAE,CAEvB,OADA1J,EAAKqM,UAAU,CAAC3C,EAAO,EAAG,GACnB1J,CACT,mBArBK,iBACLsS,QAAAA,CAAW,QAsBXe,kBAAAA,CAAqB,CAAC,IAAK,IAAI,CACjC,CCxBO,MAAMoF,WAAqB3F,EAGhCE,IAHsCF,EAGhC5F,CAAU,CAAEjL,CAAK,CAAEgH,CAAK,CAAE,CAC9B,OAAQhH,GACN,IAAK,IACH,OAAO6S,EAAoBxB,EAAgBM,MAAM,CAAE1G,EACrD,IAD4CoG,CACvC,CADuBwB,IAE1B,OAAO7L,EAAM5E,aAAa,CAAC6I,EAAY,CAAE5I,KAAM,QAAS,EAC1D,SACE,OAAO8Q,EAAanT,EAAMI,MAAM,CAAE6K,CAAfkI,CACvB,CACF,CAEAvD,SAASjM,CAAK,CAAE8D,CAAK,CAAE,CACrB,OAAOA,GAAS,GAAKA,GAAS,EAChC,CAEAyI,IAAInS,CAAI,CAAEqW,CAAM,CAAE3M,CAAK,CAAE,CAEvB,OADA1J,EAAK0Q,UAAU,CAAChH,EAAO,GAChB1J,CACT,mBArBK,iBACLsS,QAAAA,CAAW,QAsBXe,kBAAAA,CAAqB,CAAC,IAAK,IAAI,CACjC,CCzBO,MAAMqF,WAA+B5F,EAG1CE,IAHgDF,EAG1C5F,CAAU,CAAEjL,CAAK,CAAE,CAGvB,OAAO0S,EAASS,EAAanT,EAAMI,EAApBsS,IAA0B,CAAEzH,CAAfkI,EAFN,GACpB1T,KAAK2B,EACiD2S,GAD5C,CAACtM,EAAQhI,KAAK+B,GAAG,CAAC,GAAI,CAACxB,EAAMI,MAAM,CAAG,IAEpD,CAEA8P,IAAInS,CAAI,CAAEqW,CAAM,CAAE3M,CAAK,CAAE,CAEvB,OADA1J,EAAK2Y,eAAe,CAACjP,GACd1J,CACT,mBAZK,iBACLsS,QAAAA,CAAW,QAaXe,kBAAAA,CAAqB,CAAC,IAAK,IAAI,CACjC,gBCXO,OAAMuF,WAA+B9F,EAG1CE,IAHgDF,EAG1C5F,CAAU,CAAEjL,CAAK,CAAE,CACvB,OAAQA,GACN,IAAK,IACH,OAAOgT,EACL4D,EAAiBvE,cAADuE,EADS5D,IACY,CACrC/H,EAEJ,KAAK,KACH,OAAO+H,EAAqB4D,EAAiBtE,KAAK,CAAErH,EACtD,KAAK,CADyC2L,EAAjB5D,IAE3B,OAAOA,EACL4D,EAAiBrE,cAADqE,EADS5D,IACY,CACrC/H,EAEJ,KAAK,QACH,OAAO+H,EACL4D,EAAiBnE,cAADmE,EADS5D,OACe,CACxC/H,EAEJ,KAAK,IAEH,OAAO+H,EAAqB4D,EAAiBpE,QAAQ,CAAEvH,EAC3D,CACF,CAEAiF,CAJkD0G,EAAjB5D,CAI7BjV,CAAI,CAAEoS,CAAK,CAAE1I,CAAK,CAAE,QACtB,EAAU8I,cAAc,CAASxS,CAAP,CACnBgL,CAAAA,EAAAA,EAAAA,CAAAA,CAAaA,CAClBhL,EACAA,EAAKD,OAAO,GAAK+Y,CAAAA,EAAAA,GAAAA,CAAAA,CAA+BA,CAAC9Y,GAAQ0J,EAE7D,mBAlCK,iBACL4I,QAAAA,CAAW,QAmCXe,kBAAAA,CAAqB,CAAC,IAAK,IAAK,IAAI,CACtC,CCrCO,MAAM0F,WAA0BjG,EAGrCE,IAH2CF,EAGrC5F,CAAU,CAAEjL,CAAK,CAAE,CACvB,OAAQA,GACN,IAAK,IACH,OAAOgT,EACL4D,EAAiBvE,cAADuE,EADS5D,IACY,CACrC/H,EAEJ,KAAK,KACH,OAAO+H,EAAqB4D,EAAiBtE,KAAK,CAAErH,EACtD,KAAK,CADyC2L,EAAjB5D,IAE3B,OAAOA,EACL4D,EAAiBrE,cAADqE,EADS5D,IACY,CACrC/H,EAEJ,KAAK,QACH,OAAO+H,EACL4D,EAAiBnE,cAADmE,EADS5D,OACe,CACxC/H,EAEJ,KAAK,IAEH,OAAO+H,EAAqB4D,EAAiBpE,QAAQ,CAAEvH,EAC3D,CACF,CAEAiF,CAJkD0G,EAAjB5D,CAI7BjV,CAAI,CAAEoS,CAAK,CAAE1I,CAAK,CAAE,QACtB,EAAU8I,cAAc,CAASxS,CAAP,CACnBgL,CAAAA,EAAAA,EAAAA,CAAAA,CAAaA,CAClBhL,EACAA,EAAKD,OAAO,GAAK+Y,CAAAA,EAAAA,GAAAA,CAAAA,CAA+BA,CAAC9Y,GAAQ0J,EAE7D,mBAlCK,iBACL4I,QAAAA,CAAW,QAmCXe,kBAAAA,CAAqB,CAAC,IAAK,IAAK,IAAI,CACtC,CCxCO,MAAM2F,WAA+BlG,EAG1CE,IAHgDF,EAG1C5F,CAAU,CAAE,CAChB,OAAOiI,EAAqBjI,EAC9B,CAEAiF,IAAInS,CAAI,CAAEqW,CAAM,CAAE3M,CAAK,CAAE,CACvB,IAJ2ByL,EAIpB,CAACnK,CAAAA,EAAAA,EAAAA,CAAAA,CAAaA,CAAChL,EAAc,IAAR0J,GAAe,CAAE8I,gBAAgB,CAAK,EAAE,mBARjE,iBACLF,QAAAA,CAAW,QAUXe,kBAAAA,CAAqB,IACvB,CCZO,MAAM4F,WAAoCnG,EAG/CE,IAHqDF,EAG/C5F,CAAU,CAAE,CAChB,OAAOiI,EAAqBjI,EAC9B,CAEAiF,IAAInS,CAAI,CAAEqW,CAAM,CAAE3M,CAAK,CAAE,CACvB,IAJ2ByL,EAIpB,CAACnK,CAAAA,EAAAA,EAAAA,CAAAA,CAAaA,CAAChL,EAAM0J,GAAQ,CAAE8I,gBAAgB,CAAK,EAAE,mBAR1D,iBACLF,QAAAA,CAAW,QAUXe,kBAAAA,CAAqB,IACvB,CC0DO,IAAM6F,GAAU,CACrBjV,EAAG,IAAImP,EACPpR,EAAG,IAAI+T,CADS3C,CAEhB7O,EAAG,IAAI4R,EADUJ,EAEd,IAAIK,EACPtR,EAAG,IAAIyR,EACPxR,CAH0BoR,CAGvB,IAAIK,CAFiBJ,CAGxBjR,EAAG,IAAIsR,EAFkBF,EAGtB,CAFiBC,GAEbE,EACPtR,EAAG,IAAIuR,EACPtR,CAFkBqR,CAEf,GAH2BD,CAGvBG,EACPuC,EAAG,IAAIrC,EACPpU,EAAG,CAHyBiU,EACNC,CAEfM,EACPxR,EAAG,CAFiBoR,GAEbM,EADUF,EAEd,IAAIO,EACPrR,EAAG,GAFmBgR,CAEfM,CADSD,CAEhBlR,EAAG,IAAIsR,EACPrR,EAAG,EAFkBkR,EAEdI,EACPlX,EAAG,IAAIoX,EACPnX,EAAG,IAH4BgX,EAEdG,EAEd,IAAIE,EACPpV,EAAG,IAAIqV,EAFkBF,CAGzBlV,EAAG,EAFmBmV,EAEfG,GACPtR,EAAG,CAFmBoR,GAEfG,GACPc,EAAG,CAFmBf,GAEfE,GACPvV,EAAG,CAFmBsV,GAEfE,GACPtV,EAAG,CAFmBqV,GAEfE,GADYD,EAEhB,IAAIE,GACP1R,EAAG,IAAI4R,GACPtR,EAAG,IAAIyR,GACPrR,CAH6BgR,CAG1B,IAAIM,GACPpR,CAH6BgR,CAG1B,GAFqBG,CAEjBE,EACT,EAAE,GCzEA,MDuE6BD,cACKC,oCCpE9B/Q,GAA6B,oCAE7BC,GAAsB,eACtBC,GAAoB,MAEpBiR,GAAsB,KACtBhR,GAAgC,WA4S/B,SAAS2K,GAAMsG,CAAO,CAAE/Q,CAAS,CAAEgR,CAAa,CAAEnZ,CAAO,MAO5DA,EAAAA,EAEAoI,EAAAA,EAKApI,EAAAA,EAEAoI,EAAAA,EAbapI,EAAAA,EAGbA,EAAAA,EAAAA,EAAAA,EAOAA,EAAAA,EAAAA,EAAAA,EAZF,IAAMwM,EAAc,IAAM5B,CAAAA,EAAAA,EAAAA,CAAAA,CAAaA,CAAC5K,OAAAA,EAAAA,KAAAA,EAAAA,EAASE,EAAAA,GAAMiZ,EAAenN,EAA9BhM,GAClCoI,E3C/TCgR,OAAOC,MAAM,CAAC,C2C+TEhR,E3C/TEiR,CAAAA,EAAAA,EAAAA,CAAAA,CAAyBA,I2CgU5ChR,EAAStI,EADyBqI,KACzBrI,EAAAA,OAAAA,QAAAA,EAAAA,KAAAA,EAAAA,EAASsI,MAAM,EAAftI,CAAAA,CAAmBoI,EAAeE,MAAAA,EAAlCtI,EAA4CuI,EAAAA,CAAaA,CAElEC,EACJxI,IAHaA,GAGbA,EAAAA,OAAAA,EAAAA,OAAAA,EAAAA,OAAAA,QAAAA,EAAAA,KAAAA,EAAAA,EAASwI,SAATxI,YAASwI,EAATxI,QACAA,GAAAA,CADAA,MACAA,EAAAA,EAASsI,MAATtI,GAAAA,OAAAA,EAAAA,EAAAA,OAAwB,EAAxBA,KAAAA,EAAAA,EAA0BwI,GAA1BxI,kBAA0BwI,EAD1BxI,EAEAoI,EAAeI,QAFfxI,aAEewI,EAFfxI,SAGAoI,EAAAA,CAHApI,CAGesI,MAAAA,GAAfF,OAAAA,EAAAA,EAAAA,OAAuBpI,EAAvBoI,KAAAA,EAAAA,EAAgCI,GAAhCJ,kBAAgCI,EAHhCxI,EAIA,EAEIkG,EACJlG,MAPAA,CAOAA,EAAAA,OAAAA,EAAAA,OAAAA,EAAAA,OAAAA,QAAAA,EAAAA,KAAAA,EAAAA,EAASkG,SAATlG,GAASkG,EAATlG,QACAA,GAAAA,CADAA,KACAA,GAAAA,EAASsI,MAATtI,GAAAA,OAAAA,EAAAA,EAAAA,OAAwB,EAAxBA,KAAAA,EAAAA,EAA0BkG,GAA1BlG,SAA0BkG,EAD1BlG,EAEAoI,EAAelC,QAFflG,IAEekG,EAFflG,SAGAoI,EAAAA,CAHApI,CAGesI,MAAAA,GAAfF,OAAAA,EAAAA,EAAAA,OAA8B,EAA9BA,KAAAA,EAAAA,EAAgClC,GAAhCkC,SAAgClC,EAHhClG,EAIA,EAEF,GAAI,CAACmI,EACH,EAPAnI,KAOOkZ,EAAU1M,IAAgB3M,CAAAA,EAAAA,EAAAA,CAAAA,CAAMA,CAACsZ,QAAenZ,EAAAA,KAAAA,EAAAA,EAASE,EAAE,EAEpE,IAAMqZ,CAFmDvZ,CAEpC,uBACnBwI,eACAtC,SACAoC,CACF,EAIMkR,EAAU,CAAC,IAAIrH,QAAmBnS,EAAAA,KAAAA,EAAAA,CAADmS,CAAUjS,EAAE,CAAEiZ,GAAe,CAE9DM,EAAStR,EACZU,KAAK,CAACf,IACNgB,GAAG,CAAC,IACH,IAAMC,EAAiBC,CAAS,CAAC,EAAE,QACnC,KAAsBE,EAAAA,CAAcA,CAE3BD,CAF6B,IACdC,CAAc,CAACH,EAAe,EAC/BC,EAAWV,EAAOa,UAAU,EAE5CH,CACT,GACCI,IAAI,CAAC,IACLP,KAAK,CAAChB,IAEH6R,EAAa,EAAE,CAErB,IAAK,IAAI7X,KAAS4X,EAAQ,CAEtB,QAACzZ,EAAAA,KAAAA,EAAAA,EAAS8J,SAAT9J,kBAAS8J,GACVC,CAAAA,EAAAA,EAAAA,EAAAA,CAAwBA,CAAClI,IAEzBqI,CAAAA,EAAAA,CADA,CACAA,EAAAA,CAAyBA,CAACrI,EAAOsG,EAAW+Q,GAG5C,QAAClZ,EAAAA,KAAAA,EAAAA,EAASgK,SAAThK,mBAASgK,GACVC,CAAAA,EAAAA,EAAAA,EAAAA,CAAyBA,CAACpI,IAE1BqI,CAAAA,EAAAA,CADA,CACAA,EAAAA,CAAyBA,CAACrI,EAAOsG,EAAW+Q,GAG9C,IAAMnQ,EAAiBlH,CAAK,CAAC,EAAE,CACzB8X,EAASb,EAAO,CAAC/P,EAAe,CACtC,GAAI4Q,EAAQ,CACV,GAAM,oBAAE1G,CAAkB,CAAE,CAAG0G,EAC/B,GAAIC,MAAMC,OAAO,CAAC5G,GAAqB,CACrC,IAAM6G,EAAoBJ,EAAWK,IAAI,CACvC,GACE9G,EAAmB+G,QAAQ,CAACC,EAAUpY,KAAK,GAC3CoY,EAAUpY,KAAK,GAAKkH,GAExB,GAAI+Q,EACF,MAAM,WADe,sCAE2DjY,MAAAA,CAAvCiY,EAAkBI,SAAS,CAAC,WAAiB,OAANrY,EAAM,sBAG1F,MAAO,GAAkC,MAA9B8X,EAAO1G,kBAAkB,EAAYyG,EAAWzX,MAAM,CAAG,EAClE,CADqE,KAC/D,WACJ,sCAA6C,OAANJ,EAAM,2CAIjD6X,EAAWS,IAAI,CAAC,CAAEtY,MAAOkH,EAAgBmR,UAAWrY,CAAM,GAE1D,IAAMuY,EAAcT,EAAOhH,GAAG,CAC5BuG,EACArX,EACAyG,EAAOO,KAAK,CACZ0Q,GAGF,GAAI,CAACa,EACH,OAAO5N,IADS,EAIV2N,IAAI,CAACC,EAAYvH,MAAM,EAE/BqG,EAAUkB,EAAYtH,IAAI,KACrB,CACL,GAAI/J,EAAeF,KAAK,CAACZ,IACvB,MAAM,WACJ,WAFqD,sDAGnDc,EACA,KAYN,GAPc,MAAM,CAAhBlH,EACFA,EAAQ,IACoB,KAAK,CAAxBkH,IACTlH,EAqDC4H,EAAMZ,IArDCU,CAqDI,CAACxB,GAAoB,CAAC,EAAE,CAAC2B,OAAO,CAAC1B,GAAmB,IArDrCnG,EAIE,GAAG,CAA9BqX,EAAQmB,OAAO,CAACxY,GAGlB,OAAO2K,IAFP0M,EAAUA,EAAQpL,KAAK,CAACjM,EAAMI,MAAM,CAIxC,CACF,CAGA,GAAIiX,EAAQjX,MAAM,CAAG,GAAKgX,GAAoB/L,IAAI,CAACgM,GACjD,OAD2D,IAI7D,IAAMoB,EAAwBd,EAC3B1Q,GAAG,CAAC,GAAY+J,EAAOX,QAAQ,EAC/B3R,IAAI,CAAC,CAACC,EAAGC,IAAMA,EAAID,GACnB+Z,MAAM,CAAC,CAACrI,EAAUsI,EAAO3N,IAAUA,EAAMwN,OAAO,CAACnI,KAAcsI,GAC/D1R,GAAG,CAAC,GACH0Q,EACGe,MAAM,CAAC,GAAY1H,EAAOX,QAAQ,GAAKA,GACvC3R,IAAI,CAAC,CAACC,EAAGC,IAAMA,EAAEmR,WAAW,CAAGpR,EAAEoR,WAAW,GAEhD9I,GAAG,CAAE2R,GAAgBA,CAAW,CAAC,EAAE,EAElC7a,EAAOC,CAAAA,EAAAA,EAAAA,CAAAA,CAAMA,CAACsZ,QAAenZ,EAAAA,KAAAA,EAAAA,EAASE,EAAE,EAE5C,GAAI6L,EAF6B/L,IAEvB,CAACJ,GAAO,OAAO4M,IAEzB,IAAMwF,EAAQ,CAAC,EACf,IAAK,IAAMa,KAAUyH,EAAuB,CAC1C,GAAI,CAACzH,EAAOpB,QAAQ,CAAC7R,EAAM2Z,GACzB,OAAO/M,IAGT,CAJ0C,GAIpCb,EAASkH,EAAOd,GAAG,CAACnS,EAAMoS,EAAOuH,GAEnCK,MAAMC,OAAO,CAAClO,IAChB/L,EAAO+L,CAAM,CAAC,CADW,CACT,CAChByN,OAAOC,MAAM,CAACrH,EAAOrG,CAAM,CAAC,EAAE,GAG9B/L,EAAO+L,CAEX,CAEA,OAAO/L,CACT,mDCjeO,SAAS8a,EAAW9a,CAAI,CAAEI,CAAO,EAGtC,OAAO4E,KADc3B,KAAK,CAACuC,CADb3F,EAAAA,EAAAA,CAAAA,CAAMA,CAACD,QAAMI,EAAAA,KAAAA,EAAAA,EAASE,EAAE,EACLkC,KADNpC,GACc,GAAK,GAAK,CAErD,oFCeO,SAASqE,EAAYzE,CAAI,CAAEI,CAAO,MAOrCA,EAAAA,EAEAoI,EAAAA,EAHApI,EAAAA,EAAAA,EAAAA,EALF,IAAMwF,EAAQ3F,CAAAA,EAAAA,EAAAA,CAAAA,CAAMA,CAACD,QAAMI,EAAAA,KAAAA,EAAAA,EAASE,EAAE,EAChC8B,EAAOwD,EAAMzD,CADQ/B,UACG,GAExBoI,EAAiBC,CAAAA,EAAAA,EAAAA,CAAAA,CAAiBA,GAClCG,EACJxI,OAAAA,EAAAA,MAAAA,GAAAA,OAAAA,EAAAA,OAAAA,QAAAA,EAAAA,KAAAA,EAAAA,EAASwI,SAATxI,YAASwI,EAATxI,QACAA,GAAAA,CADAA,MACAA,EAAAA,EAASsI,MAATtI,GAAAA,OAAAA,EAAAA,EAAiBA,OAAAA,EAAjBA,KAAAA,EAAAA,EAA0BwI,GAA1BxI,kBAA0BwI,EAD1BxI,EAEAoI,EAAeI,QAFfxI,aAEewI,EAFfxI,SAGAoI,EAAAA,CAHApI,CAGesI,MAAAA,GAAfF,OAAAA,EAAAA,EAAuBpI,OAAAA,EAAvBoI,KAAAA,EAAAA,EAAgCI,GAAhCJ,kBAAgCI,EAHhCxI,EAIA,EAEI2a,EAAsB/P,CAAAA,EAAAA,EAAAA,CAN1B5K,CAMuC4K,CAAC5K,OAAAA,EAAAA,KAAAA,EAAAA,EAASE,EAAAA,GAAMN,EAAM,EAArBI,CAC1C2a,EAAoBvP,WAAW,CAACpJ,EAAO,EAAG,EAAGwG,GAC7CmS,EAAoBhQ,QAAQ,CAAC,EAAG,EAAG,EAAG,GACtC,IAAMiQ,EAAkB3P,CAAAA,EAAAA,EAAAA,CAAAA,CAAWA,CAAC0P,EAAqB3a,GAEnD6a,EAAsBjQ,CAAAA,EAAAA,EAAAA,CAAAA,CAAaA,CAAC5K,OAAAA,EAAAA,KAAAA,EAAAA,EAASE,EAAAA,GAAMN,EAAM,EAArBI,CAC1C6a,EAAoBzP,WAAW,CAACpJ,EAAM,EAAGwG,GACzCqS,EAAoBlQ,QAAQ,CAAC,EAAG,EAAG,EAAG,GACtC,IAAMmQ,EAAkB7P,CAAAA,EAAAA,EAAAA,CAAAA,CAAWA,CAAC4P,EAAqB7a,SAEzD,CAAKwF,GAAS,CAACoV,EACN5Y,EAAO,EACL,CAACwD,GAAS,CAACsV,EACb9Y,EAEAA,EAAO,CAElB,UALyC,yCC3ClC,SAASa,EAAWjD,CAAI,CAAEI,CAAO,EACtC,MAAOH,CAAAA,EAAAA,EAAAA,CAAAA,CAAMA,CAACD,QAAMI,EAAAA,KAAAA,EAAAA,EAASE,EAAE,EAAE2C,KAAb7C,KAAuB,EAC7C,8DCGO,SAAS+a,EAA6B1Q,CAAS,CAAEC,CAAW,CAAEtK,CAAO,EAC1E,GAAM,CAACuK,EAAYC,EAAa,CAAGC,CAAAA,EAAAA,EAAAA,CAAAA,CAAcA,OAC/CzK,EAAAA,KAAAA,EAAAA,EAASE,EAAE,CACXmK,EACAC,GAMF,CAREtK,MAQKgb,GAHWzQ,CAGK0Q,CAHMlZ,WAAW,GAAKyI,EAAazI,WAAW,KAChD2Y,CAAAA,EAAAA,EAAAA,CAAAA,CAAUA,CAACnQ,GAAcmQ,CAAAA,EAAAA,EAAAA,CAAAA,CAAUA,CAAClQ,EAAAA,CAG3D,mDChBO,SAAShI,EAAS5C,CAAI,CAAEI,CAAO,EACpC,MAAOH,CAAAA,EAAAA,EAAAA,CAAAA,CAAMA,CAACD,QAAMI,EAAAA,KAAAA,EAAAA,EAASE,EAAE,EAAEsC,KAAbxC,GAAqB,EAC3C,mDCDO,SAASkb,EAAW7Q,CAAS,CAAEC,CAAW,CAAEtK,CAAO,EACxD,GAAM,CAACuK,EAAYC,EAAa,CAAGC,CAAAA,EAAAA,EAAAA,CAAAA,CAAcA,OAC/CzK,EAAAA,KAAAA,EAAAA,EAASE,EAAE,CACXmK,EACAC,GAEF,CAJEtK,MAIKuK,EAAWxI,WAAW,KAAOyI,EAAazI,WAAW,EAC9D,mDCJO,SAAS4D,EAAY/F,CAAI,CAAEI,CAAO,EACvC,IAAM8L,EAAQjM,CAAAA,EAAAA,EAAAA,CAAAA,CAAMA,CAACD,EAAMI,QAAAA,KAAAA,EAAAA,EAASE,EAAE,CAAXF,CAG3B,OAFA8L,EAAMV,WAAW,CAACU,EAAM/J,WAAW,GAAI,EAAG,GAC1C+J,EAAMnB,QAAQ,CAAC,EAAG,EAAG,EAAG,GACjBmB,CACT,oCCjCA,IAAMqP,EAAuB,CAC3BC,iBAAkB,CAChBC,IAAK,qBACLC,MAAO,6BACT,EAEAC,SAAU,CACRF,IAAK,WACLC,MAAO,mBACT,EAEAE,YAAa,gBAEbC,iBAAkB,CAChBJ,IAAK,qBACLC,MAAO,6BACT,EAEAI,SAAU,CACRL,IAAK,WACLC,MAAO,mBACT,EAEAK,YAAa,CACXN,IAAK,eACLC,MAAO,uBACT,EAEAM,OAAQ,CACNP,IAAK,SACLC,MAAO,iBACT,EAEAO,MAAO,CACLR,IAAK,QACLC,MAAO,gBACT,EAEAQ,YAAa,CACXT,IAAK,eACLC,MAAO,uBACT,EAEAS,OAAQ,CACNV,IAAK,SACLC,MAAO,iBACT,EAEAU,aAAc,CACZX,IAAK,gBACLC,MAAO,wBACT,EAEAW,QAAS,CACPZ,IAAK,UACLC,MAAO,kBACT,EAEAY,YAAa,CACXb,IAAK,eACLC,MAAO,uBACT,EAEAa,OAAQ,CACNd,IAAK,SACLC,MAAO,iBACT,EAEAc,WAAY,CACVf,IAAK,cACLC,MAAO,sBACT,EAEAe,aAAc,CACZhB,IAAK,gBACLC,MAAO,wBACT,CACF,EC7EO,SAASgB,EAAkBC,CAAI,EACpC,OAAO,eAACvc,EAAAA,UAAAA,MAAAA,CAAAA,GAAAA,KAAAA,IAAAA,SAAAA,CAAAA,EAAAA,CAAAA,SAAAA,CAAAA,EAAAA,CAAU,CAAC,EAEXgE,EAAQhE,EAAQgE,KAAK,CAAG3B,OAAOrC,EAAQgE,KAAK,EAAIuY,EAAKC,YAAY,CAEvE,OADeD,EAAKE,OAAO,CAACzY,EAAM,EAAIuY,EAAKE,OAAO,CAACF,EAAKC,YAAY,CAAC,CAGzE,CCgBO,IAAMrT,EAAa,CACxBvJ,KAAM0c,EAAkB,CACtBG,QAvBgB,CAClBC,KAAM,mBACNC,KAAM,aACNC,OAAQ,WACRC,MAAO,YACT,EAmBIL,aAAc,MAChB,GAEAvc,KAAMqc,EAAkB,CACtBG,QArBgB,CAqBPK,KApBL,iBACNH,KAAM,cACNC,OAAQ,YACRC,MAAO,QACT,EAiBIL,aAAc,MAChB,GAEAO,SAAUT,EAAkB,CAC1BG,QAnBoB,CACtBC,KAAM,yBACNC,KAAM,yBACNC,OAAQ,qBACRC,MAAO,oBACT,EAeIL,aAAc,MAChB,EACF,EAAE,ECtC2B,CAC3BQ,SAAU,qBACVC,UAAW,mBACXC,MAAO,eACPC,SAAU,kBACVC,SAAU,cACV9B,MAAO,GACT,ECgCO,SAAS+B,EAAgBd,CAAI,EAClC,MAAO,CAACjT,EAAOtJ,SAGTsd,EACJ,GAAIxY,gBAHY9E,OAAAA,EAAAA,KAAAA,EAAAA,EAAS8E,OAAAA,EAAT9E,OAA0BA,EAAQ8E,OAAO,EAAI,eAG7ByX,EAAKgB,gBAAgB,CAAE,CACrD,IAAMf,EAAeD,EAAKiB,sBAAsB,EAAIjB,EAAKC,YAAY,CAC/DxY,EAAQhE,OAAAA,EAAAA,KAAAA,EAAAA,EAASgE,KAAAA,EAAQ3B,EAAjBrC,KAAwBA,EAAQgE,KAAK,EAAIwY,EAEvDc,EACEf,EAAKgB,gBAAgB,CAACvZ,EAAM,EAAIuY,EAAKgB,gBAAgB,CAACf,EAAa,KAChE,CACL,IAAMA,EAAeD,EAAKC,YAAY,CAChCxY,EAAQhE,OAAAA,EAAAA,KAAAA,EAAAA,EAASgE,KAAAA,EAAQ3B,EAAjBrC,KAAwBA,EAAQgE,KAAK,EAAIuY,EAAKC,YAAY,CAExEc,EAAcf,EAAKkB,MAAM,CAACzZ,EAAM,EAAIuY,EAAKkB,MAAM,CAACjB,EAAa,CAK/D,OAAOc,CAAW,CAHJf,EAAKmB,gBAAgB,CAAGnB,EAAKmB,gBAAgB,CAACpU,GAASA,EAG5C,CAE7B,CE7DO,SAASqU,EAAapB,CAAI,EAC/B,OAAO,SAACqB,CAAAA,MAsBFtU,EAtBUtJ,EAAAA,UAAAA,MAAAA,CAAAA,GAAAA,KAAAA,IAAAA,SAAAA,CAAAA,EAAAA,CAAAA,SAAAA,CAAAA,EAAAA,CAAU,CAAC,EACnBgE,EAAQhE,EAAQgE,KAAK,CAErB6Z,EACJ,GAAUtB,EAAKuB,aAAa,CAAC9Z,EAAM,EACnCuY,EAAKuB,aAAa,CAACvB,EAAKwB,iBAAiB,CAAC,CACtCnJ,EAAcgJ,EAAO/U,KAAK,CAACgV,GAEjC,GAAI,CAACjJ,EACH,OAAO,IADS,CAGlB,IAAMoJ,EAAgBpJ,CAAW,CAAC,EAAE,CAE9BqJ,EACJ,GAAU1B,EAAK0B,aAAa,CAACja,EAAM,EACnCuY,EAAK0B,aAAa,CAAC1B,EAAK2B,iBAAiB,CAAC,CAEtCC,EAAMvE,MAAMC,OAAO,CAACoE,GACtBG,SA8BCA,CAAe,CAAEC,CAAS,EACjC,IAAK,IAAIF,EAAM,EAAGA,EAAMtR,EAAM5K,MAAM,CAAEkc,IACpC,EAD2C,CACvCE,EAAUxR,CAAK,CAACsR,EAAI,EACtB,CADyB,MAClBA,CAIb,EArCkBF,EAAe,GAAatJ,EAAQzH,IAAI,CAAC8Q,IAEnDM,SAgBCA,CAAc,CAAED,CAAS,EAChC,IAAK,IAAMF,KAAOI,EAChB,GACEnF,EAFsB,KAEf7G,SAAS,CAACiM,cAAc,CAACC,IAAI,CAACF,EAAQJ,IAC7CE,EAAUE,CAAM,CAACJ,EAAI,EAErB,CADA,MACOA,CAIb,EA1BgBF,EAAe,GAAatJ,EAAQzH,IAAI,CAAC8Q,IAYrD,OARA1U,EAAQiT,EAAK3G,aAAa,CAAG2G,EAAK3G,aAAa,CAACuI,GAAOA,EAQhD,CAAE7U,MAPTA,EAAQtJ,EAAQ4V,aAAa,CAEzB5V,EAAQ4V,aAAa,CAACtM,GACtBA,EAIYwJ,KAFH8K,EAAO9P,KAAK,CAACkQ,EAAc/b,MAAM,CAEzB,CACvB,CACF,CGrBO,IAAMyc,EAAO,CAClBC,KAAM,QACNC,eT+D4B,CAAC/c,EAAOgd,EAAO7e,KAG3C,IAFI2L,CShE0BiT,CTkExBE,EAAa3D,CAAoB,CAACtZ,EAAM,CAS9C,GAPE8J,EADwB,UAAtB,OAAOmT,EACAA,EACU,GAAG,CAAbD,EACAC,EAAWzD,GAAG,CAEdyD,EAAWxD,KAAK,CAAC5R,OAAO,CAAC,YAAamV,EAAMrd,QAAQ,UAG3DxB,EAAAA,KAAAA,EAAAA,EAAS+e,SAAT/e,CACF,CADsB,EAClBA,EAAQgf,UAAU,EAAIhf,EAAQgf,UAAU,CAAG,EAC7C,CADgD,KACzC,MAAQrT,OAEf,OAAOA,EAAS,OAIpB,OAAOA,CACT,ESnFExC,WAAYA,EACZ8V,QADsB9V,ONRM,CAACtH,EAAO2D,EAAO0Z,EAAWvN,IACtDwN,CAAoB,CAACtd,EAAM,CAAC,SEkJN,CACtBoC,QI1IkBH,MJiHE,CAACsb,EAAazN,KAClC,IAAMxQ,EAASkQ,OAAO+N,GAShBC,EAASle,EAAS,IACxB,GAAIke,EAAS,IAAMA,EAAS,GAC1B,CAD8B,MACtBA,EAAS,IACf,KAAK,EACH,OAAOle,EAAS,IAClB,MAAK,EACH,OAAOA,EAAS,IAClB,MAAK,EACH,OAAOA,EAAS,IACpB,CAEF,OAAOA,EAAS,IAClB,EAKE4C,IAAKsZ,EAAgB,CACnBI,OA9Jc,CAChB6B,IA4JoBjC,GA5JZ,CAAC,IAAK,IAAI,CAClBkC,YAAa,CAAC,KAAM,KAAK,CACzBC,KAAM,CAAC,gBAAiB,cAAc,EA4JpChD,aAAc,MAChB,GAEA5X,QAASyY,EAAgB,CACvBI,OA7JkB,CA6JVgC,IADcpC,GA3JhB,CAAC,IAAK,IAAK,IAAK,IAAI,CAC5BkC,YAAa,CAAC,KAAM,KAAM,KAAM,KAAK,CACrCC,KAAM,CAAC,cAAe,cAAe,cAAe,cAAc,EA2JhEhD,aAAc,OACdkB,iBAAkB,GAAa9Y,EAAU,CAC3C,GAEAzC,MAAOkb,EAAgB,CACrBI,OAzJgB,CAyJRiC,IADYrC,GAvJd,CAAC,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAI,CACpEkC,YAAa,CACX,MACA,MACA,MACA,MACA,MACA,MACA,MACA,MACA,MACA,MACA,MACA,MACD,CAEDC,KAAM,CACJ,UACA,WACA,QACA,QACA,MACA,OACA,OACA,SACA,YACA,UACA,WACA,WACD,EA4HChD,aAAc,MAChB,GAEAzW,IAAKsX,EAAgB,CACnBI,OA7Hc,CA6HNkC,IADUtC,GA3HZ,CAAC,IAAK,IAAK,IAAK,IAAK,IAAK,IAAK,IAAI,CAC3CR,MAAO,CAAC,KAAM,KAAM,KAAM,KAAM,KAAM,KAAM,KAAK,CACjD0C,YAAa,CAAC,MAAO,MAAO,MAAO,MAAO,MAAO,MAAO,MAAM,CAC9DC,KAAM,CACJ,SACA,SACA,UACA,YACA,WACA,SACA,WACD,EAkHChD,aAAc,MAChB,GAEAlW,UAAW+W,EAAgB,CACzBI,OAnHoB,CACtB6B,IAiH0BjC,GAjHlB,CACNuC,GAAI,IACJC,GAAI,IACJvc,SAAU,KACVC,KAAM,IACNC,QAAS,UACTC,UAAW,YACXC,QAAS,UACTC,MAAO,OACT,EACA4b,YAAa,CACXK,GAAI,KACJC,GAAI,KACJvc,SAAU,WACVC,KAAM,OACNC,QAAS,UACTC,UAAW,YACXC,QAAS,UACTC,MAAO,OACT,EACA6b,KAAM,CACJI,GAAI,OACJC,GAAI,OACJvc,SAAU,WACVC,KAAM,OACNC,QAAS,UACTC,UAAW,YACXC,QAAS,UACTC,MAAO,OACT,CACF,EAqFI6Y,aAAc,OACde,iBApF8B,CAChC+B,OAAQ,CACNM,GAAI,IACJC,GAAI,IACJvc,SAAU,KACVC,KAAM,IACNC,QAAS,iBACTC,UAAW,mBACXC,QAAS,iBACTC,MAAO,UACT,EACA4b,YAAa,CACXK,GAAI,KACJC,GAAI,KACJvc,SAAU,WACVC,KAAM,OACNC,QAAS,iBACTC,UAAW,mBACXC,QAAS,iBACTC,MAAO,UACT,EACA6b,KAAM,CACJI,GAAI,OACJC,GAAI,OACJvc,SAAU,WACVC,KAAM,OACNC,QAAS,iBACTC,UAAW,mBACXC,QAAS,iBACTC,MAAO,UACT,CACF,EAsDI6Z,uBAAwB,MAC1B,EACF,EAAE,MGjGmB,CACnBvZ,KCtEY4E,SFpBP,SAASiX,CAAwB,EACtC,OCyFkCA,SDzF1BlC,CAAAA,MAAQ5d,EAAAA,UAAAA,MAAAA,CAAAA,GAAAA,KAAAA,IAAAA,SAAAA,CAAAA,EAAAA,CAAAA,SAAAA,CAAAA,EAAAA,CAAU,CAAC,EACnB4U,EAAcgJ,EAAO/U,KAAK,CAAC0T,EAAKsB,YAAY,EAClD,GAAI,CAACjJ,EAAa,OAAO,KACzB,IAAMoJ,EAAgBpJ,CAAW,CAAC,EAAE,CAE9BwF,EAAcwD,EAAO/U,KAAK,CAAC0T,EAAKwD,YAAY,EAClD,GAAI,CAAC3F,EAAa,OAAO,KACzB,IAAI9Q,EAAQiT,EAAK3G,aAAa,CAC1B2G,EAAK3G,aAAa,CAACwE,CAAW,CAAC,EAAE,EACjCA,CAAW,CAAC,EAAE,CAOlB,OAAS9Q,MAJTA,EAAQtJ,EAAQ4V,aAAa,CAAG5V,EAAQ4V,aAAa,CAACtM,GAASA,EAI/CwJ,KAFH8K,EAAO9P,KAAK,CAACkQ,EAAc/b,MAAM,EAGhD,CACF,ECuEqC,CACjC4b,aAxF8B,CAwFhBmC,uBACdD,aAxF8B,CAwFhBE,MACdrK,cAAe,GAAWhI,SAAStE,EAAO,GAC5C,GAEAvF,IAAK4Z,EAAa,CAChBG,SADeH,KA1FM,CA2FNuC,OA1FT,UACRX,YAAa,6DACbC,KAAM,4DACR,EAwFIzB,kBAAmB,OACnBE,cAxFqB,CAwFNkC,IAvFZ,CAAC,MAAO,UAAU,EAwFrBjC,kBAAmB,KACrB,GAEAtZ,QAAS+Y,EAAa,CACpBG,SADmBH,KAxFM,CAyFVyC,OAxFT,WACRb,YAAa,YACbC,KAAM,gCACR,EAsFIzB,kBAAmB,OACnBE,cAtFyB,CAsFVoC,IArFZ,CAAC,KAAM,KAAM,KAAM,KAAK,EAsF3BnC,kBAAmB,MACnBtI,cAAe,GAAW4E,EAAQ,CACpC,GAEArY,MAAOwb,EAAa,CAClBG,SADiBH,KAvFM,CACzB2B,OAAQ,eACRC,YAAa,sDACbC,KAAM,2FACR,EAqFIzB,kBAAmB,OACnBE,cArFuB,CAqFRqC,OApFT,CACN,MACA,MACA,MACA,MACA,MACA,MACA,MACA,MACA,MACA,MACA,MACA,MACD,CAEDC,IAAK,CACH,OACA,MACA,QACA,OACA,QACA,QACA,QACA,OACA,MACA,MACA,MACA,MACD,EAyDCrC,kBAAmB,KACrB,GAEAnY,IAAK4X,EAAa,CAChBG,SADeH,KAzDM,CACvB2B,OAAQ,YACRzC,MAAO,2BACP0C,YAAa,kCACbC,KAAM,8DACR,EAsDIzB,kBAAmB,OACnBE,cAtDqB,CAsDNuC,OArDT,CAAC,MAAO,MAAO,MAAO,MAAO,MAAO,MAAO,MAAM,CACzDD,IAAK,CAAC,OAAQ,MAAO,OAAQ,MAAO,OAAQ,MAAO,OACrD,EAoDIrC,kBAAmB,KACrB,GAEA5X,UAAWqX,EAAa,CACtBG,SADqBH,KArDM,CAsDZ8C,OArDT,6DACRF,IAAK,gFACP,EAoDIxC,kBAAmB,MACnBE,cApD2B,CAoDZyC,IAnDZ,CACHd,GAAI,MACJC,GAAI,MACJvc,SAAU,OACVC,KAAM,OACNC,QAAS,WACTC,UAAW,aACXC,QAAS,WACTC,MAAO,QACT,CACF,EA0CIua,kBAAmB,KACrB,EACF,EC9GEle,QAAS,CACPkG,aAAc,EAAE,UAAU,YACH,CACzB,CACF,EAAE,iDCGK,SAASya,EAAS/gB,CAAI,CAAEmB,CAAM,CAAEf,CAAO,EAC5C,MAAO4gB,CAAAA,EAAAA,EAAAA,CAAAA,CAAQA,CAAChhB,EAAM,CAACmB,EAAQf,EACjC,+DChBA,aACA,qCACA,2BACA,iBACA,IACA,QACA,UACA,CAAQ,EACR,cACA,CAAK,YAEL,oBAwMA,mCAA8C,iBAAe,CAAG,WAAS,CAGzE,IAYA,EADA,CAVA,GAAK,gBAAK,CAUV,oBACA,SACA,cACA,MAAc,QAAY,MAI1B,CAAG,EAIH,OAHA,OACA,WACA,CAAG,EACM,aAAiB,YAC1B,0CAAwE,IAAa,IACrF,kBAEA,6CACA,CAAG,IACH,CAgPA,YACA,iBACA,aAIA,qGACA,CAAC,4DCndM,SAAS6gB,EAAUxW,CAAS,CAAEC,CAAW,CAAEtK,CAAO,EACvD,GAAM,CAAC8gB,EAAWC,EAAW,CAAGtW,CAAAA,EAAAA,EAAAA,CAAAA,CAAcA,OAC5CzK,EAAAA,KAAAA,EAAAA,EAASE,EAAE,CACXmK,EACAC,GAEF,CAJEtK,KAIK,CAAC0K,CAAAA,EAAAA,EAAAA,CAAAA,CAAUA,CAACoW,IAAe,CAACpW,CAAAA,EAAAA,EAAAA,CAAAA,CAAUA,CAACqW,EAChD,mDCfO,SAASC,EAA2B3W,CAAS,CAAEC,CAAW,CAAEtK,CAAO,EACxE,GAAM,CAACuK,EAAYC,EAAa,CAAGC,CAAAA,EAAAA,EAAAA,CAAAA,CAAcA,OAC/CzK,EAAAA,KAAAA,EAAAA,EAASE,EAAE,CACXmK,EACAC,GAMF,CAREtK,MAQKgb,IAHWzQ,CAGM0W,CAHKlf,WAAW,GAAKyI,EAAazI,WAAW,KAClDwI,EAAWnI,QAAQ,GAAKoI,EAAapI,QAAQ,GAGlE,mDCrCO,SAASqI,EAAe3F,CAAO,EAAE,0DAAG4G,CAAAA,CAAH,iBAAQ,CAC9C,IAAMwV,EAAYtW,EAAAA,CAAaA,CAACiB,IAAI,CAClC,KACA/G,GAAW4G,EAAMqO,IAAI,CAAC,GAA0B,UAAhB,OAAOna,IAEzC,OAAO8L,EAAM5C,GAAG,CAACoY,EACnB,8DCqBO,SAASC,EAAQvhB,CAAI,CAAEoC,CAAI,CAAEhC,CAAO,EACzC,IAAM8L,EAAQjM,CAAAA,EAAAA,EAAAA,CAAAA,CAAMA,CAACD,QAAMI,EAAAA,KAAAA,EAAAA,EAASE,EAAE,OAAXF,EAG3B,MAAU,CAAC8L,GAAelB,CAAAA,EAAAA,EAAP,CAAOA,CAAaA,CAAC5K,OAAAA,EAAAA,KAAAA,EAAAA,EAASE,EAAAA,GAAMN,EAAMoM,EAArBhM,IAExC8L,EAAMV,WAAW,CAACpJ,GACX8J,EACT,mDCxBO,SAAS4M,EAAgC9Y,CAAI,EAClD,IAAM4F,EAAQ3F,CAAAA,EAAAA,EAAAA,CAAAA,CAAMA,CAACD,GACfwhB,EAAU,IAAItW,KAClBA,KAAKuW,GAAG,CACN7b,EAAMzD,WAAW,GACjByD,EAAMpD,QAAQ,GACdoD,EAAM3E,OAAO,GACb2E,EAAMhD,QAAQ,GACdgD,EAAM3C,UAAU,GAChB2C,EAAM5E,UAAU,GAChB4E,EAAMrC,eAAe,KAIzB,OADAie,EAAQ/S,cAAc,CAAC7I,EAAMzD,WAAW,IACjC,EAASqf,CAClB,IADiB,+CCPV,SAASE,EAAQ1hB,CAAI,CAAEe,CAAa,EACzC,MAAO,CAACd,CAAAA,EAAAA,EAAAA,CAAAA,CAAMA,CAACD,GAAQ,CAACC,CAAAA,EAAAA,EAAAA,CAAAA,CAAMA,CAACc,EACjC,mDCGO,SAAS4gB,EAAQ3hB,CAAI,CAAEmB,CAAM,CAAEf,CAAO,EAC3C,MAAOwQ,CAAAA,EAAAA,EAAAA,CAAAA,CAAOA,CAAC5Q,EAAM,CAACmB,EAAQf,EAChC,8DCMO,SAASuR,EAAU3R,CAAI,CAAEmB,CAAM,CAAEf,CAAO,EAC7C,IAAMwF,EAAQ3F,CAAAA,EAAAA,EAAAA,CAAAA,CAAMA,CAACD,QAAMI,EAAAA,KAAAA,EAAAA,EAASE,EAAE,EACtC,GAAI6L,EADuB/L,IACjBe,GAAS,MAAO6J,CAAAA,EAAAA,EAAAA,CAAAA,CAAaA,CAAC5K,SAAAA,KAAAA,EAAAA,EAASE,EAAE,GAAIN,EAAMoM,KAC7D,GAAI,CAACjL,EAEH,MAFW,CAEJyE,EAET,IAAMgc,EAAahc,EAAM3E,OAAO,GAU1B4gB,EAAoB7W,CAAAA,EAAAA,EAAAA,CAAAA,CAAaA,CAAC5K,OAAAA,EAAAA,KAAAA,EAAAA,EAASE,EAAAA,GAAMN,EAAM4F,EAArBxF,OAAkC,UAG1E,CAFAyhB,EAAkBvR,QAAQ,CAAC1K,EAAMpD,QAAQ,GAAKrB,EAAS,EAAG,GAEtDygB,GADgBC,EAAkB5gB,OAAO,EAC3B6P,EAGT+Q,GASPjc,EAAM4F,MAZuB,KAYZ,CACfqW,EAAkB1f,WAAW,GAC7B0f,EAAkBrf,QAAQ,GAC1Bof,GAEKhc,EAEX,mDC5CO,SAASkc,EAAe9hB,CAAI,CAAEI,CAAO,EAC1C,IAAMwF,EAAQ3F,CAAAA,EAAAA,EAAAA,CAAAA,CAAMA,CAACD,QAAMI,EAAAA,KAAAA,EAAAA,EAASE,EAAE,EAChCyhB,EAAenc,EAAMpD,CADApC,OACQ,GAInC,OAFAwF,EAAM0K,QAAQ,CADAyR,EAAgBA,EAAe,EACvB,GACtBnc,EAAMmF,QAAQ,CAAC,EAAG,EAAG,EAAG,GACjBnF,CACT,mDCPO,SAASmF,EAAS/K,CAAI,CAAE4G,CAAK,CAAExG,CAAO,EAC3C,IAAMwF,EAAQ3F,CAAAA,EAAAA,EAAAA,CAAAA,CAAMA,CAACD,QAAMI,EAAAA,KAAAA,EAAAA,EAASE,EAAE,EAEtC,KAF2BF,EAC3BwF,EAAMmF,QAAQ,CAACnE,GACRhB,CACT,mDCJO,SAAS6G,EAAYzM,CAAI,CAAEmB,CAAM,CAAEf,CAAO,EAC/C,MAAOuR,CAAAA,EAAAA,EAAAA,CAAAA,CAASA,CAAC3R,EAAe,EAATmB,EAAYf,EACrC,mDCFO,SAAS4hB,EAAShiB,CAAI,CAAEmB,CAAM,CAAEf,CAAO,EAC5C,MAAO6hB,CAAAA,EAAAA,EAAAA,CAAAA,CAAQA,CAACjiB,EAAM,CAACmB,EAAQf,EACjC,mDCFO,SAAS8hB,EAAWliB,CAAI,CAAEmB,CAAM,CAAEf,CAAO,EAC9C,MAAOgB,CAAAA,EAAAA,EAAAA,CAAAA,CAAeA,CAACpB,EAAe,IAATmB,EAAef,EAC9C,mDCAO,SAAS8Q,EAAelR,CAAI,CAAEI,CAAO,EAC1C,MAAOiL,CAAAA,EAAAA,EAAAA,CAAAA,CAAWA,CAACrL,EAAM,CAAE,GAAGI,CAAO,CAAEkG,aAAc,CAAE,EACzD,yECJO,SAAS1B,EAAe5E,CAAI,CAAEI,CAAO,EAC1C,IAAMwF,EAAQ3F,CAAAA,EAAAA,EAAAA,CAAAA,CAAMA,CAACD,QAAMI,EAAAA,KAAAA,EAAAA,EAASE,EAAE,EAChC8B,EAAOwD,EAAMzD,CADQ/B,UACG,GAExB+hB,EAA4BnX,CAAAA,EAAAA,EAAAA,CAAAA,CAAaA,CAACpF,EAAO,GACvDuc,EAA0B3W,WAAW,CAACpJ,EAAO,EAAG,EAAG,GACnD+f,EAA0BpX,QAAQ,CAAC,EAAG,EAAG,EAAG,GAC5C,IAAMiQ,EAAkB9J,CAAAA,EAAAA,EAAAA,CAAAA,CAAcA,CAACiR,GAEjCC,EAA4BpX,CAAAA,EAAAA,EAAAA,CAAAA,CAAaA,CAACpF,EAAO,GACvDwc,EAA0B5W,WAAW,CAACpJ,EAAM,EAAG,GAC/CggB,EAA0BrX,QAAQ,CAAC,EAAG,EAAG,EAAG,GAC5C,IAAMmQ,EAAkBhK,CAAAA,EAAAA,EAAAA,CAAAA,CAAcA,CAACkR,UAEvC,EAAUriB,OAAO,IAAMib,EAAgBjb,OAAO,GACrCqC,CADyC,CAClC,EACLwD,EAAM7F,OAAO,IAAMmb,EAAgBnb,OAAO,GAC5CqC,CADgD,CAGhDA,EAAO,CAElB,mDCzBO,SAASigB,EAAQriB,CAAI,CAAEI,CAAO,EACnC,MAAOH,CAAAA,EAAAA,EAAAA,CAAAA,CAAMA,CAACD,QAAMI,EAAAA,KAAAA,EAAAA,EAASE,EAAE,EAAE6B,KAAb/B,MAAwB,EAC9C,mDCHO,SAASkiB,EAAQC,CAAQ,CAAEC,CAAS,EACzC,MAAO,CAACviB,CAAAA,EAAAA,EAAAA,CAAAA,CAAMA,CAACsiB,IAAc,CAACtiB,CAAAA,EAAAA,EAAAA,CAAAA,CAAMA,CAACuiB,EACvC,8DCMO,SAASC,EAAchY,CAAS,CAAEC,CAAW,CAAEtK,CAAO,EAC3D,GAAM,CAAC8gB,EAAWC,EAAW,CAAGtW,CAAAA,EAAAA,EAAAA,CAAAA,CAAcA,OAC5CzK,EAAAA,KAAAA,EAAAA,EAASE,EAAE,CACXmK,EACAC,GAEF,CAJEtK,KAIK,CAAC0hB,CAAAA,EAAAA,EAAAA,CAAAA,CAAcA,CAACZ,IAAe,CAACY,CAAAA,EAAAA,EAAAA,CAAAA,CAAcA,CAACX,EACxD,8DCJO,SAASuB,EAAU1iB,CAAI,CAAEI,CAAO,MAInCA,EAAAA,EAEAoI,EAAAA,EAHApI,EAAAA,EAAAA,EAAAA,EAFF,IAAMoI,EAAiBC,CAAAA,EAAAA,EAAAA,CAAAA,CAAiBA,GAClCnC,EACJlG,OAAAA,EAAAA,OAAAA,EAAAA,OAAAA,EAAAA,OAAAA,QAAAA,EAAAA,KAAAA,EAAAA,EAASkG,SAATlG,GAASkG,EAATlG,QACAA,GAAAA,CADAA,KACAA,GAAAA,EAASsI,MAATtI,GAAAA,OAAAA,EAAAA,EAAAA,OAAiBA,EAAjBA,KAAAA,EAAAA,EAA0BkG,GAA1BlG,SAA0BkG,EAD1BlG,EAEAoI,EAAelC,QAFflG,IAEekG,EAFflG,SAGAoI,EAAAA,CAHApI,CAGesI,MAAAA,GAAfF,MAAAA,GAAAA,EAAAA,OAAuBpI,EAAvBoI,KAAAA,EAAAA,EAAgClC,GAAhCkC,SAA4C,EAH5CpI,EAIA,EAEIwF,EAAQ3F,CAAAA,EAAAA,EAAAA,CANZG,CAMkBH,CAACD,QAAMI,EAAAA,KAAAA,EAAAA,EAASE,EAAE,EAChC6F,EAAMP,EAAMM,CADS9F,KACH,GAKxB,OAFAwF,EAAM2G,OAAO,CAAC3G,EAAM3E,OAAO,IAFd,CAACkF,EAAMG,EAAe,CAAC,GAAI,EAAK,EAAKH,EAAAA,CAAMG,CAAAA,CAAW,GAGnEV,EAAMmF,QAAQ,CAAC,GAAI,GAAI,GAAI,KACpBnF,CACT,mDCtBO,SAAS+c,EAA0BlY,CAAS,CAAEC,CAAW,CAAEtK,CAAO,EACvE,GAAM,CAACuK,EAAYC,EAAa,CAAGC,CAAAA,EAAAA,EAAAA,CAAAA,CAAcA,OAC/CzK,EAAAA,KAAAA,EAAAA,EAASE,EAAE,CACXmK,EACAC,GAEF,CAJEtK,MAIKuK,EAAWxI,WAAW,GAAKyI,EAAazI,WAAW,EAC5D,mBCHO,SAASygB,EAAOlZ,CAAK,EAC1B,OACEA,aAAiBwB,MAChB,iBAAOxB,GACoC,kBAA1C8P,OAAO7G,SAAS,CAAC/Q,QAAQ,CAACid,IAAI,CAACnV,EAErC,mGCEO,SAAS5D,EAAyB2E,CAAS,CAAEC,CAAW,CAAEtK,CAAO,EACtE,GAAM,CAACuK,EAAYC,EAAa,CAAGC,CAAAA,EAAAA,EAAAA,CAAAA,CAAcA,OAC/CzK,EAAAA,KAAAA,EAAAA,EAASE,EAAE,CACXmK,EACAC,GAGImY,CALJziB,CAKsB0K,CAAAA,EAAAA,EAAAA,CAAAA,CAAUA,CAACH,GAC7BmY,EAAoBhY,CAAAA,EAAAA,EAAAA,CAAAA,CAAUA,CAACF,GAUrC,OAAOlJ,KAAK0J,KAAK,CAAC,CAAC2X,EAPEjK,CAAAA,EAAAA,EAAAA,CAAAA,CAA+BA,CAAC+J,IAEnD,EAAqB/J,CAAAA,EAAAA,EAAAA,CAAAA,CAA+BA,CAACgK,EAAAA,CAKnBE,CAAe,CAAKC,EAAAA,EAAiBA,CAC3E,oCC3DA,IAAMC,EAAoB,CAACnO,EAASxL,KAClC,OAAQwL,GACN,IAAK,IACH,OAAOxL,EAAWvJ,IAAI,CAAC,CAAEoE,MAAO,OAAQ,EAC1C,KAAK,KACH,OAAOmF,EAAWvJ,IAAI,CAAC,CAAEoE,MAAO,QAAS,EAC3C,KAAK,MACH,OAAOmF,EAAWvJ,IAAI,CAAC,CAAEoE,MAAO,MAAO,EACzC,KAAK,IAEH,OAAOmF,EAAWvJ,IAAI,CAAC,CAAEoE,MAAO,MAAO,EAC3C,CACF,EAEM+e,EAAoB,CAACpO,EAASxL,KAClC,OAAQwL,GACN,IAAK,IACH,OAAOxL,EAAWlJ,IAAI,CAAC,CAAE+D,MAAO,OAAQ,EAC1C,KAAK,KACH,OAAOmF,EAAWlJ,IAAI,CAAC,CAAE+D,MAAO,QAAS,EAC3C,KAAK,MACH,OAAOmF,EAAWlJ,IAAI,CAAC,CAAE+D,MAAO,MAAO,EACzC,KAAK,IAEH,OAAOmF,EAAWlJ,IAAI,CAAC,CAAE+D,MAAO,MAAO,EAC3C,CACF,EAkCakF,EAAiB,CAC5B8Z,EAAGD,EACHE,EAlC4B,CAACtO,EAASxL,KACtC,IAQI+Z,EAREtO,EAAcD,EAAQ9L,KAAK,CAAC,cAAgB,EAAE,CAC9Csa,EAAcvO,CAAW,CAAC,EAAE,CAC5BwO,EAAcxO,CAAW,CAAC,EAAE,CAElC,GAAI,CAACwO,EACH,OAAON,EAAkBnO,EAASxL,GAKpC,OAAQga,GACN,IAAK,IACHD,EAAiB/Z,EAAW4T,QAAQ,CAAC,CAAE/Y,MAAO,OAAQ,GACtD,KACF,KAAK,KACHkf,EAAiB/Z,EAAW4T,QAAQ,CAAC,CAAE/Y,MAAO,QAAS,GACvD,KACF,KAAK,MACHkf,EAAiB/Z,EAAW4T,QAAQ,CAAC,CAAE/Y,MAAO,MAAO,GACrD,KACF,KAAK,IAEHkf,EAAiB/Z,EAAW4T,QAAQ,CAAC,CAAE/Y,MAAO,MAAO,EAEzD,CAEA,OAAOkf,EACJxZ,OAAO,CAAC,WAAYoZ,EAAkBK,EAAaha,IACnDO,OAAO,CAAC,WAAYqZ,EAAkBK,EAAaja,GACxD,CAKA,EAAE,iDCnCK,SAAS0Y,EAASjiB,CAAI,CAAEmB,CAAM,CAAEf,CAAO,EAC5C,MAAOwQ,CAAAA,EAAAA,EAAAA,CAAAA,CAAOA,CAAC5Q,EAAe,EAATmB,EAAYf,EACnC,oCC9BA,IAAIoI,EAAiB,CAAC,EAEf,SAASC,IACd,OAAOD,CACT,mDCwBO,SAASwY,EAAShhB,CAAI,CAAEmB,CAAM,CAAEf,CAAO,EAC5C,MAAOuR,CAAAA,EAAAA,EAAAA,CAAAA,CAASA,CAAC3R,EAAe,GAATmB,EAAaf,EACtC,qGCxBA,+BAA8C,iBAAe,CAAG,WAAS,CAIzE,oBAUA,EACA,EACA,EAXA,SACA,SAEA,sBACA,SAEA,qDACA,SAKA,6BACA,qBAEA,GADA,eACA,kBACA,QAAuB,EAAU,MACjC,iBACA,SAGA,QACA,CAGA,GADA,IADA,kBACA,UACA,sBACA,SAEA,QAAqB,EAAU,CAAV,KACrB,QAAa,6BACb,SAGA,QAAqB,EAAU,CAAV,KAAU,CAC/B,WACA,gCAGA,cACA,QAEA,CACA,QACA,CACA,iBACA,CAEA,oBACA,2BACA,EAEA,sCACA,mBACA,CAEA,gBACA,WACA,wBACA,CAEA,cACA,MAAc,QAAY,IAI1B,OAHA,OACA,WACA,CAAG,EACH,CACA,CAMA,cACA,YACA,OAEA,IACA,qBACA,sBACA,gBACA,WACA,UACA,YACA,WACA,CAAM,EAAI,CACV,eACA,uBACA,OACA,CAAI,EACJ,MAA0B,UAAc,EACxC,IACA,IACA,WACA,YACA,iBAAsB,CACtB,eACA,CAAG,EACH,MAAkD,UAAc,GAChE,SACA,KAEA,SAAsC,UAAc,OACpD,MAAoC,UAAc,OAClD,EAAuB,aAAiB,KACxC,gBACA,YACA,KAEA,CAAG,KACH,EAAsB,aAAiB,KACvC,gBACA,YACA,KAEA,CAAG,KACH,OACA,OACA,EAAuB,QAAY,OACnC,EAAsB,QAAY,OAClC,EAAkB,QAAY,IAC9B,UACA,OACA,OACA,OACA,EAAiB,aAAiB,MAClC,0BACA,OAEA,OACA,YACA,WACA,YACA,CACA,YACA,uBAEI,QAAe,iCACnB,OACA,KAKA,2BACA,CACA,8BACA,YACQ,WAAkB,MAC1B,IACA,CAAS,EAET,CAAK,CACL,CAAG,cACH,OACA,iCACA,0BACA,OACA,KACA,eACA,EAAO,EAEP,CAAG,MACH,MAAuB,QAAY,KACnC,OACA,aACA,KACA,YACA,GACG,IACH,OAGA,GAFA,iBACA,iBACA,MACA,aACA,wBAEA,GACA,CACA,CAAG,cACH,MAAe,SAAa,OAC5B,YACA,WACA,eACA,aACA,EAAG,QACH,EAAmB,SAAa,OAChC,YACA,UACA,EAAG,QACH,EAAyB,SAAa,MACtC,OACA,WACA,OACA,KACA,EACA,eACA,SAEA,wBACA,2BACA,EACA,CACA,KACA,wCACA,wBACA,sBACA,CAAS,EAGT,CACA,WACA,OACA,KACA,CACA,CAAG,2BACH,OAAS,SAAa,OACtB,KACA,SACA,OACA,WACA,gBACA,GAAG,YACH,CAQA,SAIA,EACA,aACA,UACA,MACA,IACA,UACA,UACA,CAAQ,mCACR,GAVA,IAAa,qBAUb,EAVa,WAWb,gBACiB,QAAO,EACxB,kBACA,SACA,CAAW,QAEX,GAEA,EACe,QAAO,EACtB,UACA,SACA,CAAS,QAET,EACA,CACA,GAUA,WACA,GAAK,QAAQ,IACb,cACA,CAAC,CA0BD,WACA,GAAK,QAAM,IACX,cACA,CAAC,CAkDD,WACA,QACA,cACA,CAAC,kDCrVM,SAASoC,EAASxC,CAAI,CAAEI,CAAO,EACpC,MAAOH,CAAAA,EAAAA,EAAAA,CAAAA,CAAMA,CAACD,QAAMI,EAAAA,KAAAA,EAAAA,EAASE,EAAE,EAAEkC,KAAbpC,GAAqB,EAC3C,uDC1BA,IAAMqjB,EAAmB,OACnBC,EAAkB,OAElBC,EAAc,CAAC,IAAK,KAAM,KAAM,OAAO,CAEtC,SAAStZ,EAA0BpI,CAAK,EAC7C,OAAOwhB,EAAiBnW,IAAI,CAACrL,EAC/B,CAEO,SAASkI,EAAyBlI,CAAK,EAC5C,OAAOyhB,EAAgBpW,IAAI,CAACrL,EAC9B,CAEO,SAASqI,EAA0BrI,CAAK,CAAEqG,CAAM,CAAEuB,CAAK,EAC5D,IAAM+Z,EAAWC,SAKVA,CAAa,CAAEvb,CAAM,CAAEuB,CAAK,EACnC,IAAMia,EAAuB,MAAb7hB,CAAK,CAAC,EAAE,CAAW,QAAU,oBAC7C,MAAO,QAA+CA,MAAAA,CAAtCA,EAAM0E,WAAW,GAAG,kBAAmC2B,MAAAA,CAAjBrG,EAAM,WAAuC6hB,MAAAA,CAA5Bxb,EAAO,sBAA+CuB,MAAAA,CAA1Bia,EAAQ,mBAAwB,OAANja,EAAM,iFACrI,EAR2B5H,EAAOqG,EAAQuB,GAExC,GADAka,QAAQC,IAAI,CAACJ,GACTD,EAAYvJ,QAAQ,CAACnY,GAAQ,MAAM,WAAe2hB,EACxD,8DCiBO,SAASvY,EAAYrL,CAAI,CAAEI,CAAO,MAIrCA,EAAAA,EAEAoI,EAAAA,EAHApI,EAAAA,EAAAA,EAAAA,EAFF,IAAMoI,EAAiBC,CAAAA,EAAAA,EAAAA,CAAAA,CAAiBA,GAClCnC,EACJlG,OAAAA,EAAAA,OAAAA,EAAAA,OAAAA,EAAAA,MAAAA,SAAAA,EAAAA,KAAAA,EAAAA,EAASkG,SAATlG,GAASkG,EAATlG,QACAA,GAAAA,CADAA,MACAA,EAAAA,EAASsI,MAATtI,GAAAA,OAAAA,EAAAA,EAAAA,OAAiBA,EAAjBA,KAAAA,EAAAA,EAA0BkG,GAA1BlG,SAA0BkG,EAD1BlG,EAEAoI,EAAelC,QAFflG,IAEekG,EAFflG,SAGAoI,EAAAA,CAHApI,CAGesI,MAAAA,GAAfF,OAAAA,EAAAA,EAAAA,OAAuBpI,EAAvBoI,KAAAA,EAAAA,EAAgClC,GAAhCkC,SAA4C,EAH5CpI,EAIA,EAEIwF,EAAQ3F,CAAAA,EAAAA,EAAAA,CANZG,CAMkBH,CAACD,QAAMI,EAAAA,KAAAA,EAAAA,EAASE,EAAE,EAChC6F,EAAMP,EAAMM,CADS9F,KACH,GAKxB,OAFAwF,EAAM2G,OAAO,CAAC3G,EAAM3E,OAAO,IAFQ,CAEH4E,EAFlBM,CAAyB,CAAnBG,CAAAA,EAAwBH,EAAMG,CAAAA,GAGlDV,EAAMmF,QAAQ,CAAC,EAAG,EAAG,EAAG,GACjBnF,CACT,8DChBO,SAASkD,EAAQ9I,CAAI,EAC1B,MAAO,CAAE,EAAE4iB,CAAAA,EAAAA,EAAAA,CAAAA,CAAMA,CAAC5iB,IAAyB,UAAhB,OAAOA,GAAsBmM,MAAM,CAAClM,CAAAA,EAAAA,EAAAA,CAAAA,CAAMA,CAACD,GAAAA,CAAK,kFCyCtE,IAAMyL,EAAqB,OAOrBwX,EAAoB,CAPW,KAc/BrX,EAAuB,CAPM,GAc7BvK,EAP6B,KAc7B6T,EAAuB,CAPM,GAsH7BjK,CA/G4B,CA+GNgZ,OAAOC,GAAG,CAAC,qBAAqB", "sources": ["webpack://_N_E/./node_modules/date-fns/getTime.js", "webpack://_N_E/./node_modules/date-fns/isWithinInterval.js", "webpack://_N_E/./node_modules/date-fns/isBefore.js", "webpack://_N_E/./node_modules/react-datepicker/node_modules/clsx/dist/clsx.mjs", "webpack://_N_E/./node_modules/date-fns/getSeconds.js", "webpack://_N_E/./node_modules/date-fns/getDate.js", "webpack://_N_E/./node_modules/date-fns/addHours.js", "webpack://_N_E/./node_modules/date-fns/getDayOfYear.js", "webpack://_N_E/./node_modules/date-fns/_lib/addLeadingZeros.js", "webpack://_N_E/./node_modules/date-fns/_lib/format/lightFormatters.js", "webpack://_N_E/./node_modules/date-fns/_lib/format/formatters.js", "webpack://_N_E/./node_modules/date-fns/format.js", "webpack://_N_E/./node_modules/date-fns/isSameMonth.js", "webpack://_N_E/./node_modules/date-fns/startOfDay.js", "webpack://_N_E/./node_modules/date-fns/constructFrom.js", "webpack://_N_E/./node_modules/date-fns/startOfWeekYear.js", "webpack://_N_E/./node_modules/date-fns/getWeek.js", "webpack://_N_E/./node_modules/date-fns/addMinutes.js", "webpack://_N_E/./node_modules/date-fns/getDay.js", "webpack://_N_E/./node_modules/date-fns/max.js", "webpack://_N_E/./node_modules/date-fns/setMinutes.js", "webpack://_N_E/./node_modules/date-fns/startOfMonth.js", "webpack://_N_E/./node_modules/date-fns/subQuarters.js", "webpack://_N_E/./node_modules/date-fns/parseISO.js", "webpack://_N_E/./node_modules/date-fns/setQuarter.js", "webpack://_N_E/./node_modules/date-fns/endOfMonth.js", "webpack://_N_E/./node_modules/date-fns/min.js", "webpack://_N_E/./node_modules/date-fns/endOfDay.js", "webpack://_N_E/./node_modules/date-fns/setSeconds.js", "webpack://_N_E/./node_modules/date-fns/endOfYear.js", "webpack://_N_E/./node_modules/date-fns/addDays.js", "webpack://_N_E/./node_modules/date-fns/toDate.js", "webpack://_N_E/./node_modules/date-fns/addMilliseconds.js", "webpack://_N_E/./node_modules/date-fns/getDaysInMonth.js", "webpack://_N_E/./node_modules/date-fns/setMonth.js", "webpack://_N_E/./node_modules/date-fns/startOfISOWeekYear.js", "webpack://_N_E/./node_modules/date-fns/getISOWeek.js", "webpack://_N_E/./node_modules/date-fns/differenceInDays.js", "webpack://_N_E/./node_modules/date-fns/subMonths.js", "webpack://_N_E/./node_modules/date-fns/getDefaultOptions.js", "webpack://_N_E/./node_modules/date-fns/transpose.js", "webpack://_N_E/./node_modules/date-fns/parse/_lib/Setter.js", "webpack://_N_E/./node_modules/date-fns/parse/_lib/Parser.js", "webpack://_N_E/./node_modules/date-fns/parse/_lib/parsers/EraParser.js", "webpack://_N_E/./node_modules/date-fns/parse/_lib/constants.js", "webpack://_N_E/./node_modules/date-fns/parse/_lib/utils.js", "webpack://_N_E/./node_modules/date-fns/parse/_lib/parsers/YearParser.js", "webpack://_N_E/./node_modules/date-fns/parse/_lib/parsers/LocalWeekYearParser.js", "webpack://_N_E/./node_modules/date-fns/parse/_lib/parsers/ISOWeekYearParser.js", "webpack://_N_E/./node_modules/date-fns/parse/_lib/parsers/ExtendedYearParser.js", "webpack://_N_E/./node_modules/date-fns/parse/_lib/parsers/QuarterParser.js", "webpack://_N_E/./node_modules/date-fns/parse/_lib/parsers/StandAloneQuarterParser.js", "webpack://_N_E/./node_modules/date-fns/parse/_lib/parsers/MonthParser.js", "webpack://_N_E/./node_modules/date-fns/parse/_lib/parsers/StandAloneMonthParser.js", "webpack://_N_E/./node_modules/date-fns/setWeek.js", "webpack://_N_E/./node_modules/date-fns/parse/_lib/parsers/LocalWeekParser.js", "webpack://_N_E/./node_modules/date-fns/setISOWeek.js", "webpack://_N_E/./node_modules/date-fns/parse/_lib/parsers/ISOWeekParser.js", "webpack://_N_E/./node_modules/date-fns/parse/_lib/parsers/DateParser.js", "webpack://_N_E/./node_modules/date-fns/parse/_lib/parsers/DayOfYearParser.js", "webpack://_N_E/./node_modules/date-fns/setDay.js", "webpack://_N_E/./node_modules/date-fns/parse/_lib/parsers/DayParser.js", "webpack://_N_E/./node_modules/date-fns/parse/_lib/parsers/LocalDayParser.js", "webpack://_N_E/./node_modules/date-fns/parse/_lib/parsers/StandAloneLocalDayParser.js", "webpack://_N_E/./node_modules/date-fns/getISODay.js", "webpack://_N_E/./node_modules/date-fns/setISODay.js", "webpack://_N_E/./node_modules/date-fns/parse/_lib/parsers/ISODayParser.js", "webpack://_N_E/./node_modules/date-fns/parse/_lib/parsers/AMPMParser.js", "webpack://_N_E/./node_modules/date-fns/parse/_lib/parsers/AMPMMidnightParser.js", "webpack://_N_E/./node_modules/date-fns/parse/_lib/parsers/DayPeriodParser.js", "webpack://_N_E/./node_modules/date-fns/parse/_lib/parsers/Hour1to12Parser.js", "webpack://_N_E/./node_modules/date-fns/parse/_lib/parsers/Hour0to23Parser.js", "webpack://_N_E/./node_modules/date-fns/parse/_lib/parsers/Hour0To11Parser.js", "webpack://_N_E/./node_modules/date-fns/parse/_lib/parsers/Hour1To24Parser.js", "webpack://_N_E/./node_modules/date-fns/parse/_lib/parsers/MinuteParser.js", "webpack://_N_E/./node_modules/date-fns/parse/_lib/parsers/SecondParser.js", "webpack://_N_E/./node_modules/date-fns/parse/_lib/parsers/FractionOfSecondParser.js", "webpack://_N_E/./node_modules/date-fns/parse/_lib/parsers/ISOTimezoneWithZParser.js", "webpack://_N_E/./node_modules/date-fns/parse/_lib/parsers/ISOTimezoneParser.js", "webpack://_N_E/./node_modules/date-fns/parse/_lib/parsers/TimestampSecondsParser.js", "webpack://_N_E/./node_modules/date-fns/parse/_lib/parsers/TimestampMillisecondsParser.js", "webpack://_N_E/./node_modules/date-fns/parse/_lib/parsers.js", "webpack://_N_E/./node_modules/date-fns/parse.js", "webpack://_N_E/./node_modules/date-fns/getQuarter.js", "webpack://_N_E/./node_modules/date-fns/getWeekYear.js", "webpack://_N_E/./node_modules/date-fns/getMinutes.js", "webpack://_N_E/./node_modules/date-fns/differenceInCalendarQuarters.js", "webpack://_N_E/./node_modules/date-fns/getHours.js", "webpack://_N_E/./node_modules/date-fns/isSameYear.js", "webpack://_N_E/./node_modules/date-fns/startOfYear.js", "webpack://_N_E/./node_modules/date-fns/locale/en-US/_lib/formatDistance.js", "webpack://_N_E/./node_modules/date-fns/locale/_lib/buildFormatLongFn.js", "webpack://_N_E/./node_modules/date-fns/locale/en-US/_lib/formatLong.js", "webpack://_N_E/./node_modules/date-fns/locale/en-US/_lib/formatRelative.js", "webpack://_N_E/./node_modules/date-fns/locale/_lib/buildLocalizeFn.js", "webpack://_N_E/./node_modules/date-fns/locale/en-US/_lib/localize.js", "webpack://_N_E/./node_modules/date-fns/locale/_lib/buildMatchFn.js", "webpack://_N_E/./node_modules/date-fns/locale/_lib/buildMatchPatternFn.js", "webpack://_N_E/./node_modules/date-fns/locale/en-US/_lib/match.js", "webpack://_N_E/./node_modules/date-fns/locale/en-US.js", "webpack://_N_E/./node_modules/date-fns/subYears.js", "webpack://_N_E/./node_modules/@floating-ui/react/dist/floating-ui.react.utils.mjs", "webpack://_N_E/./node_modules/date-fns/isSameDay.js", "webpack://_N_E/./node_modules/date-fns/differenceInCalendarMonths.js", "webpack://_N_E/./node_modules/date-fns/_lib/normalizeDates.js", "webpack://_N_E/./node_modules/date-fns/setYear.js", "webpack://_N_E/./node_modules/date-fns/_lib/getTimezoneOffsetInMilliseconds.js", "webpack://_N_E/./node_modules/date-fns/isAfter.js", "webpack://_N_E/./node_modules/date-fns/subDays.js", "webpack://_N_E/./node_modules/date-fns/addMonths.js", "webpack://_N_E/./node_modules/date-fns/startOfQuarter.js", "webpack://_N_E/./node_modules/date-fns/setHours.js", "webpack://_N_E/./node_modules/date-fns/addQuarters.js", "webpack://_N_E/./node_modules/date-fns/subWeeks.js", "webpack://_N_E/./node_modules/date-fns/addSeconds.js", "webpack://_N_E/./node_modules/date-fns/startOfISOWeek.js", "webpack://_N_E/./node_modules/date-fns/getISOWeekYear.js", "webpack://_N_E/./node_modules/date-fns/getYear.js", "webpack://_N_E/./node_modules/date-fns/isEqual.js", "webpack://_N_E/./node_modules/date-fns/isSameQuarter.js", "webpack://_N_E/./node_modules/date-fns/endOfWeek.js", "webpack://_N_E/./node_modules/date-fns/differenceInCalendarYears.js", "webpack://_N_E/./node_modules/date-fns/isDate.js", "webpack://_N_E/./node_modules/date-fns/differenceInCalendarDays.js", "webpack://_N_E/./node_modules/date-fns/_lib/format/longFormatters.js", "webpack://_N_E/./node_modules/date-fns/addWeeks.js", "webpack://_N_E/./node_modules/date-fns/_lib/defaultOptions.js", "webpack://_N_E/./node_modules/date-fns/addYears.js", "webpack://_N_E/./node_modules/@floating-ui/react-dom/dist/floating-ui.react-dom.mjs", "webpack://_N_E/./node_modules/date-fns/getMonth.js", "webpack://_N_E/./node_modules/date-fns/_lib/protectedTokens.js", "webpack://_N_E/./node_modules/date-fns/startOfWeek.js", "webpack://_N_E/./node_modules/date-fns/isValid.js", "webpack://_N_E/./node_modules/date-fns/constants.js"], "sourcesContent": ["import { toDate } from \"./toDate.js\";\n\n/**\n * @name getTime\n * @category Timestamp Helpers\n * @summary Get the milliseconds timestamp of the given date.\n *\n * @description\n * Get the milliseconds timestamp of the given date.\n *\n * @param date - The given date\n *\n * @returns The timestamp\n *\n * @example\n * // Get the timestamp of 29 February 2012 11:45:05.123:\n * const result = getTime(new Date(2012, 1, 29, 11, 45, 5, 123))\n * //=> 1330515905123\n */\nexport function getTime(date) {\n  return +toDate(date);\n}\n\n// Fallback for modularized imports:\nexport default getTime;\n", "import { toDate } from \"./toDate.js\";\n\n/**\n * The {@link isWithinInterval} function options.\n */\n\n/**\n * @name isWithinInterval\n * @category Interval Helpers\n * @summary Is the given date within the interval?\n *\n * @description\n * Is the given date within the interval? (Including start and end.)\n *\n * @param date - The date to check\n * @param interval - The interval to check\n * @param options - An object with options\n *\n * @returns The date is within the interval\n *\n * @example\n * // For the date within the interval:\n * isWithinInterval(new Date(2014, 0, 3), {\n *   start: new Date(2014, 0, 1),\n *   end: new Date(2014, 0, 7)\n * })\n * // => true\n *\n * @example\n * // For the date outside of the interval:\n * isWithinInterval(new Date(2014, 0, 10), {\n *   start: new Date(2014, 0, 1),\n *   end: new Date(2014, 0, 7)\n * })\n * // => false\n *\n * @example\n * // For date equal to the interval start:\n * isWithinInterval(date, { start, end: date })\n * // => true\n *\n * @example\n * // For date equal to the interval end:\n * isWithinInterval(date, { start: date, end })\n * // => true\n */\nexport function isWithinInterval(date, interval, options) {\n  const time = +toDate(date, options?.in);\n  const [startTime, endTime] = [\n    +toDate(interval.start, options?.in),\n    +toDate(interval.end, options?.in),\n  ].sort((a, b) => a - b);\n\n  return time >= startTime && time <= endTime;\n}\n\n// Fallback for modularized imports:\nexport default isWithinInterval;\n", "import { toDate } from \"./toDate.js\";\n\n/**\n * @name isBefore\n * @category Common Helpers\n * @summary Is the first date before the second one?\n *\n * @description\n * Is the first date before the second one?\n *\n * @param date - The date that should be before the other one to return true\n * @param dateToCompare - The date to compare with\n *\n * @returns The first date is before the second date\n *\n * @example\n * // Is 10 July 1989 before 11 February 1987?\n * const result = isBefore(new Date(1989, 6, 10), new Date(1987, 1, 11))\n * //=> false\n */\nexport function isBefore(date, dateToCompare) {\n  return +toDate(date) < +toDate(dateToCompare);\n}\n\n// Fallback for modularized imports:\nexport default isBefore;\n", "function r(e){var t,f,n=\"\";if(\"string\"==typeof e||\"number\"==typeof e)n+=e;else if(\"object\"==typeof e)if(Array.isArray(e)){var o=e.length;for(t=0;t<o;t++)e[t]&&(f=r(e[t]))&&(n&&(n+=\" \"),n+=f)}else for(f in e)e[f]&&(n&&(n+=\" \"),n+=f);return n}export function clsx(){for(var e,t,f=0,n=\"\",o=arguments.length;f<o;f++)(e=arguments[f])&&(t=r(e))&&(n&&(n+=\" \"),n+=t);return n}export default clsx;", "import { toDate } from \"./toDate.js\";\n\n/**\n * @name getSeconds\n * @category Second Helpers\n * @summary Get the seconds of the given date.\n *\n * @description\n * Get the seconds of the given date.\n *\n * @param date - The given date\n *\n * @returns The seconds\n *\n * @example\n * // Get the seconds of 29 February 2012 11:45:05.123:\n * const result = getSeconds(new Date(2012, 1, 29, 11, 45, 5, 123))\n * //=> 5\n */\nexport function getSeconds(date) {\n  return toDate(date).getSeconds();\n}\n\n// Fallback for modularized imports:\nexport default getSeconds;\n", "import { toDate } from \"./toDate.js\";\n\n/**\n * The {@link getDate} function options.\n */\n\n/**\n * @name getDate\n * @category Day Helpers\n * @summary Get the day of the month of the given date.\n *\n * @description\n * Get the day of the month of the given date.\n *\n * @param date - The given date\n * @param options - An object with options.\n *\n * @returns The day of month\n *\n * @example\n * // Which day of the month is 29 February 2012?\n * const result = getDate(new Date(2012, 1, 29))\n * //=> 29\n */\nexport function getDate(date, options) {\n  return toDate(date, options?.in).getDate();\n}\n\n// Fallback for modularized imports:\nexport default getDate;\n", "import { addMilliseconds } from \"./addMilliseconds.js\";\nimport { millisecondsInHour } from \"./constants.js\";\n\n/**\n * The {@link addHours} function options.\n */\n\n/**\n * @name addHours\n * @category Hour Helpers\n * @summary Add the specified number of hours to the given date.\n *\n * @description\n * Add the specified number of hours to the given date.\n *\n * @typeParam DateType - The `Date` type, the function operates on. Gets inferred from passed arguments. Allows to use extensions like [`UTCDate`](https://github.com/date-fns/utc).\n * @typeParam ResultDate - The result `Date` type, it is the type returned from the context function if it is passed, or inferred from the arguments.\n *\n * @param date - The date to be changed\n * @param amount - The amount of hours to be added\n * @param options - An object with options\n *\n * @returns The new date with the hours added\n *\n * @example\n * // Add 2 hours to 10 July 2014 23:00:00:\n * const result = addHours(new Date(2014, 6, 10, 23, 0), 2)\n * //=> Fri Jul 11 2014 01:00:00\n */\nexport function addHours(date, amount, options) {\n  return addMilliseconds(date, amount * millisecondsInHour, options);\n}\n\n// Fallback for modularized imports:\nexport default addHours;\n", "import { differenceInCalendarDays } from \"./differenceInCalendarDays.js\";\nimport { startOfYear } from \"./startOfYear.js\";\nimport { toDate } from \"./toDate.js\";\n\n/**\n * The {@link getDayOfYear} function options.\n */\n\n/**\n * @name getDayOfYear\n * @category Day Helpers\n * @summary Get the day of the year of the given date.\n *\n * @description\n * Get the day of the year of the given date.\n *\n * @param date - The given date\n * @param options - The options\n *\n * @returns The day of year\n *\n * @example\n * // Which day of the year is 2 July 2014?\n * const result = getDayOfYear(new Date(2014, 6, 2))\n * //=> 183\n */\nexport function getDayOfYear(date, options) {\n  const _date = toDate(date, options?.in);\n  const diff = differenceInCalendarDays(_date, startOfYear(_date));\n  const dayOfYear = diff + 1;\n  return dayOfYear;\n}\n\n// Fallback for modularized imports:\nexport default getDayOfYear;\n", "export function addLeadingZeros(number, targetLength) {\n  const sign = number < 0 ? \"-\" : \"\";\n  const output = Math.abs(number).toString().padStart(targetLength, \"0\");\n  return sign + output;\n}\n", "import { addLeadingZeros } from \"../addLeadingZeros.js\";\n\n/*\n * |     | Unit                           |     | Unit                           |\n * |-----|--------------------------------|-----|--------------------------------|\n * |  a  | AM, PM                         |  A* |                                |\n * |  d  | Day of month                   |  D  |                                |\n * |  h  | Hour [1-12]                    |  H  | Hour [0-23]                    |\n * |  m  | Minute                         |  M  | Month                          |\n * |  s  | Second                         |  S  | Fraction of second             |\n * |  y  | Year (abs)                     |  Y  |                                |\n *\n * Letters marked by * are not implemented but reserved by Unicode standard.\n */\n\nexport const lightFormatters = {\n  // Year\n  y(date, token) {\n    // From http://www.unicode.org/reports/tr35/tr35-31/tr35-dates.html#Date_Format_tokens\n    // | Year     |     y | yy |   yyy |  yyyy | yyyyy |\n    // |----------|-------|----|-------|-------|-------|\n    // | AD 1     |     1 | 01 |   001 |  0001 | 00001 |\n    // | AD 12    |    12 | 12 |   012 |  0012 | 00012 |\n    // | AD 123   |   123 | 23 |   123 |  0123 | 00123 |\n    // | AD 1234  |  1234 | 34 |  1234 |  1234 | 01234 |\n    // | AD 12345 | 12345 | 45 | 12345 | 12345 | 12345 |\n\n    const signedYear = date.getFullYear();\n    // Returns 1 for 1 BC (which is year 0 in JavaScript)\n    const year = signedYear > 0 ? signedYear : 1 - signedYear;\n    return addLeadingZeros(token === \"yy\" ? year % 100 : year, token.length);\n  },\n\n  // Month\n  M(date, token) {\n    const month = date.getMonth();\n    return token === \"M\" ? String(month + 1) : addLeadingZeros(month + 1, 2);\n  },\n\n  // Day of the month\n  d(date, token) {\n    return addLeadingZeros(date.getDate(), token.length);\n  },\n\n  // AM or PM\n  a(date, token) {\n    const dayPeriodEnumValue = date.getHours() / 12 >= 1 ? \"pm\" : \"am\";\n\n    switch (token) {\n      case \"a\":\n      case \"aa\":\n        return dayPeriodEnumValue.toUpperCase();\n      case \"aaa\":\n        return dayPeriodEnumValue;\n      case \"aaaaa\":\n        return dayPeriodEnumValue[0];\n      case \"aaaa\":\n      default:\n        return dayPeriodEnumValue === \"am\" ? \"a.m.\" : \"p.m.\";\n    }\n  },\n\n  // Hour [1-12]\n  h(date, token) {\n    return addLeadingZeros(date.getHours() % 12 || 12, token.length);\n  },\n\n  // Hour [0-23]\n  H(date, token) {\n    return addLeadingZeros(date.getHours(), token.length);\n  },\n\n  // Minute\n  m(date, token) {\n    return addLeadingZeros(date.getMinutes(), token.length);\n  },\n\n  // Second\n  s(date, token) {\n    return addLeadingZeros(date.getSeconds(), token.length);\n  },\n\n  // Fraction of second\n  S(date, token) {\n    const numberOfDigits = token.length;\n    const milliseconds = date.getMilliseconds();\n    const fractionalSeconds = Math.trunc(\n      milliseconds * Math.pow(10, numberOfDigits - 3),\n    );\n    return addLeadingZeros(fractionalSeconds, token.length);\n  },\n};\n", "import { getDayOfYear } from \"../../getDayOfYear.js\";\nimport { getISOWeek } from \"../../getISOWeek.js\";\nimport { getISOWeekYear } from \"../../getISOWeekYear.js\";\nimport { getWeek } from \"../../getWeek.js\";\nimport { getWeekYear } from \"../../getWeekYear.js\";\n\nimport { addLeadingZeros } from \"../addLeadingZeros.js\";\nimport { lightFormatters } from \"./lightFormatters.js\";\n\nconst dayPeriodEnum = {\n  am: \"am\",\n  pm: \"pm\",\n  midnight: \"midnight\",\n  noon: \"noon\",\n  morning: \"morning\",\n  afternoon: \"afternoon\",\n  evening: \"evening\",\n  night: \"night\",\n};\n\n/*\n * |     | Unit                           |     | Unit                           |\n * |-----|--------------------------------|-----|--------------------------------|\n * |  a  | AM, PM                         |  A* | Milliseconds in day            |\n * |  b  | AM, PM, noon, midnight         |  B  | Flexible day period            |\n * |  c  | Stand-alone local day of week  |  C* | Localized hour w/ day period   |\n * |  d  | Day of month                   |  D  | Day of year                    |\n * |  e  | Local day of week              |  E  | Day of week                    |\n * |  f  |                                |  F* | Day of week in month           |\n * |  g* | Modified Julian day            |  G  | Era                            |\n * |  h  | Hour [1-12]                    |  H  | Hour [0-23]                    |\n * |  i! | ISO day of week                |  I! | ISO week of year               |\n * |  j* | Localized hour w/ day period   |  J* | Localized hour w/o day period  |\n * |  k  | Hour [1-24]                    |  K  | Hour [0-11]                    |\n * |  l* | (deprecated)                   |  L  | Stand-alone month              |\n * |  m  | Minute                         |  M  | Month                          |\n * |  n  |                                |  N  |                                |\n * |  o! | Ordinal number modifier        |  O  | Timezone (GMT)                 |\n * |  p! | Long localized time            |  P! | Long localized date            |\n * |  q  | Stand-alone quarter            |  Q  | Quarter                        |\n * |  r* | Related Gregorian year         |  R! | ISO week-numbering year        |\n * |  s  | Second                         |  S  | Fraction of second             |\n * |  t! | Seconds timestamp              |  T! | Milliseconds timestamp         |\n * |  u  | Extended year                  |  U* | Cyclic year                    |\n * |  v* | Timezone (generic non-locat.)  |  V* | Timezone (location)            |\n * |  w  | Local week of year             |  W* | Week of month                  |\n * |  x  | Timezone (ISO-8601 w/o Z)      |  X  | Timezone (ISO-8601)            |\n * |  y  | Year (abs)                     |  Y  | Local week-numbering year      |\n * |  z  | Timezone (specific non-locat.) |  Z* | Timezone (aliases)             |\n *\n * Letters marked by * are not implemented but reserved by Unicode standard.\n *\n * Letters marked by ! are non-standard, but implemented by date-fns:\n * - `o` modifies the previous token to turn it into an ordinal (see `format` docs)\n * - `i` is ISO day of week. For `i` and `ii` is returns numeric ISO week days,\n *   i.e. 7 for Sunday, 1 for Monday, etc.\n * - `I` is ISO week of year, as opposed to `w` which is local week of year.\n * - `R` is ISO week-numbering year, as opposed to `Y` which is local week-numbering year.\n *   `R` is supposed to be used in conjunction with `I` and `i`\n *   for universal ISO week-numbering date, whereas\n *   `Y` is supposed to be used in conjunction with `w` and `e`\n *   for week-numbering date specific to the locale.\n * - `P` is long localized date format\n * - `p` is long localized time format\n */\n\nexport const formatters = {\n  // Era\n  G: function (date, token, localize) {\n    const era = date.getFullYear() > 0 ? 1 : 0;\n    switch (token) {\n      // AD, BC\n      case \"G\":\n      case \"GG\":\n      case \"GGG\":\n        return localize.era(era, { width: \"abbreviated\" });\n      // A, B\n      case \"GGGGG\":\n        return localize.era(era, { width: \"narrow\" });\n      // Anno Domini, Before Christ\n      case \"GGGG\":\n      default:\n        return localize.era(era, { width: \"wide\" });\n    }\n  },\n\n  // Year\n  y: function (date, token, localize) {\n    // Ordinal number\n    if (token === \"yo\") {\n      const signedYear = date.getFullYear();\n      // Returns 1 for 1 BC (which is year 0 in JavaScript)\n      const year = signedYear > 0 ? signedYear : 1 - signedYear;\n      return localize.ordinalNumber(year, { unit: \"year\" });\n    }\n\n    return lightFormatters.y(date, token);\n  },\n\n  // Local week-numbering year\n  Y: function (date, token, localize, options) {\n    const signedWeekYear = getWeekYear(date, options);\n    // Returns 1 for 1 BC (which is year 0 in JavaScript)\n    const weekYear = signedWeekYear > 0 ? signedWeekYear : 1 - signedWeekYear;\n\n    // Two digit year\n    if (token === \"YY\") {\n      const twoDigitYear = weekYear % 100;\n      return addLeadingZeros(twoDigitYear, 2);\n    }\n\n    // Ordinal number\n    if (token === \"Yo\") {\n      return localize.ordinalNumber(weekYear, { unit: \"year\" });\n    }\n\n    // Padding\n    return addLeadingZeros(weekYear, token.length);\n  },\n\n  // ISO week-numbering year\n  R: function (date, token) {\n    const isoWeekYear = getISOWeekYear(date);\n\n    // Padding\n    return addLeadingZeros(isoWeekYear, token.length);\n  },\n\n  // Extended year. This is a single number designating the year of this calendar system.\n  // The main difference between `y` and `u` localizers are B.C. years:\n  // | Year | `y` | `u` |\n  // |------|-----|-----|\n  // | AC 1 |   1 |   1 |\n  // | BC 1 |   1 |   0 |\n  // | BC 2 |   2 |  -1 |\n  // Also `yy` always returns the last two digits of a year,\n  // while `uu` pads single digit years to 2 characters and returns other years unchanged.\n  u: function (date, token) {\n    const year = date.getFullYear();\n    return addLeadingZeros(year, token.length);\n  },\n\n  // Quarter\n  Q: function (date, token, localize) {\n    const quarter = Math.ceil((date.getMonth() + 1) / 3);\n    switch (token) {\n      // 1, 2, 3, 4\n      case \"Q\":\n        return String(quarter);\n      // 01, 02, 03, 04\n      case \"QQ\":\n        return addLeadingZeros(quarter, 2);\n      // 1st, 2nd, 3rd, 4th\n      case \"Qo\":\n        return localize.ordinalNumber(quarter, { unit: \"quarter\" });\n      // Q1, Q2, Q3, Q4\n      case \"QQQ\":\n        return localize.quarter(quarter, {\n          width: \"abbreviated\",\n          context: \"formatting\",\n        });\n      // 1, 2, 3, 4 (narrow quarter; could be not numerical)\n      case \"QQQQQ\":\n        return localize.quarter(quarter, {\n          width: \"narrow\",\n          context: \"formatting\",\n        });\n      // 1st quarter, 2nd quarter, ...\n      case \"QQQQ\":\n      default:\n        return localize.quarter(quarter, {\n          width: \"wide\",\n          context: \"formatting\",\n        });\n    }\n  },\n\n  // Stand-alone quarter\n  q: function (date, token, localize) {\n    const quarter = Math.ceil((date.getMonth() + 1) / 3);\n    switch (token) {\n      // 1, 2, 3, 4\n      case \"q\":\n        return String(quarter);\n      // 01, 02, 03, 04\n      case \"qq\":\n        return addLeadingZeros(quarter, 2);\n      // 1st, 2nd, 3rd, 4th\n      case \"qo\":\n        return localize.ordinalNumber(quarter, { unit: \"quarter\" });\n      // Q1, Q2, Q3, Q4\n      case \"qqq\":\n        return localize.quarter(quarter, {\n          width: \"abbreviated\",\n          context: \"standalone\",\n        });\n      // 1, 2, 3, 4 (narrow quarter; could be not numerical)\n      case \"qqqqq\":\n        return localize.quarter(quarter, {\n          width: \"narrow\",\n          context: \"standalone\",\n        });\n      // 1st quarter, 2nd quarter, ...\n      case \"qqqq\":\n      default:\n        return localize.quarter(quarter, {\n          width: \"wide\",\n          context: \"standalone\",\n        });\n    }\n  },\n\n  // Month\n  M: function (date, token, localize) {\n    const month = date.getMonth();\n    switch (token) {\n      case \"M\":\n      case \"MM\":\n        return lightFormatters.M(date, token);\n      // 1st, 2nd, ..., 12th\n      case \"Mo\":\n        return localize.ordinalNumber(month + 1, { unit: \"month\" });\n      // Jan, Feb, ..., Dec\n      case \"MMM\":\n        return localize.month(month, {\n          width: \"abbreviated\",\n          context: \"formatting\",\n        });\n      // J, F, ..., D\n      case \"MMMMM\":\n        return localize.month(month, {\n          width: \"narrow\",\n          context: \"formatting\",\n        });\n      // January, February, ..., December\n      case \"MMMM\":\n      default:\n        return localize.month(month, { width: \"wide\", context: \"formatting\" });\n    }\n  },\n\n  // Stand-alone month\n  L: function (date, token, localize) {\n    const month = date.getMonth();\n    switch (token) {\n      // 1, 2, ..., 12\n      case \"L\":\n        return String(month + 1);\n      // 01, 02, ..., 12\n      case \"LL\":\n        return addLeadingZeros(month + 1, 2);\n      // 1st, 2nd, ..., 12th\n      case \"Lo\":\n        return localize.ordinalNumber(month + 1, { unit: \"month\" });\n      // Jan, Feb, ..., Dec\n      case \"LLL\":\n        return localize.month(month, {\n          width: \"abbreviated\",\n          context: \"standalone\",\n        });\n      // J, F, ..., D\n      case \"LLLLL\":\n        return localize.month(month, {\n          width: \"narrow\",\n          context: \"standalone\",\n        });\n      // January, February, ..., December\n      case \"LLLL\":\n      default:\n        return localize.month(month, { width: \"wide\", context: \"standalone\" });\n    }\n  },\n\n  // Local week of year\n  w: function (date, token, localize, options) {\n    const week = getWeek(date, options);\n\n    if (token === \"wo\") {\n      return localize.ordinalNumber(week, { unit: \"week\" });\n    }\n\n    return addLeadingZeros(week, token.length);\n  },\n\n  // ISO week of year\n  I: function (date, token, localize) {\n    const isoWeek = getISOWeek(date);\n\n    if (token === \"Io\") {\n      return localize.ordinalNumber(isoWeek, { unit: \"week\" });\n    }\n\n    return addLeadingZeros(isoWeek, token.length);\n  },\n\n  // Day of the month\n  d: function (date, token, localize) {\n    if (token === \"do\") {\n      return localize.ordinalNumber(date.getDate(), { unit: \"date\" });\n    }\n\n    return lightFormatters.d(date, token);\n  },\n\n  // Day of year\n  D: function (date, token, localize) {\n    const dayOfYear = getDayOfYear(date);\n\n    if (token === \"Do\") {\n      return localize.ordinalNumber(dayOfYear, { unit: \"dayOfYear\" });\n    }\n\n    return addLeadingZeros(dayOfYear, token.length);\n  },\n\n  // Day of week\n  E: function (date, token, localize) {\n    const dayOfWeek = date.getDay();\n    switch (token) {\n      // Tue\n      case \"E\":\n      case \"EE\":\n      case \"EEE\":\n        return localize.day(dayOfWeek, {\n          width: \"abbreviated\",\n          context: \"formatting\",\n        });\n      // T\n      case \"EEEEE\":\n        return localize.day(dayOfWeek, {\n          width: \"narrow\",\n          context: \"formatting\",\n        });\n      // Tu\n      case \"EEEEEE\":\n        return localize.day(dayOfWeek, {\n          width: \"short\",\n          context: \"formatting\",\n        });\n      // Tuesday\n      case \"EEEE\":\n      default:\n        return localize.day(dayOfWeek, {\n          width: \"wide\",\n          context: \"formatting\",\n        });\n    }\n  },\n\n  // Local day of week\n  e: function (date, token, localize, options) {\n    const dayOfWeek = date.getDay();\n    const localDayOfWeek = (dayOfWeek - options.weekStartsOn + 8) % 7 || 7;\n    switch (token) {\n      // Numerical value (Nth day of week with current locale or weekStartsOn)\n      case \"e\":\n        return String(localDayOfWeek);\n      // Padded numerical value\n      case \"ee\":\n        return addLeadingZeros(localDayOfWeek, 2);\n      // 1st, 2nd, ..., 7th\n      case \"eo\":\n        return localize.ordinalNumber(localDayOfWeek, { unit: \"day\" });\n      case \"eee\":\n        return localize.day(dayOfWeek, {\n          width: \"abbreviated\",\n          context: \"formatting\",\n        });\n      // T\n      case \"eeeee\":\n        return localize.day(dayOfWeek, {\n          width: \"narrow\",\n          context: \"formatting\",\n        });\n      // Tu\n      case \"eeeeee\":\n        return localize.day(dayOfWeek, {\n          width: \"short\",\n          context: \"formatting\",\n        });\n      // Tuesday\n      case \"eeee\":\n      default:\n        return localize.day(dayOfWeek, {\n          width: \"wide\",\n          context: \"formatting\",\n        });\n    }\n  },\n\n  // Stand-alone local day of week\n  c: function (date, token, localize, options) {\n    const dayOfWeek = date.getDay();\n    const localDayOfWeek = (dayOfWeek - options.weekStartsOn + 8) % 7 || 7;\n    switch (token) {\n      // Numerical value (same as in `e`)\n      case \"c\":\n        return String(localDayOfWeek);\n      // Padded numerical value\n      case \"cc\":\n        return addLeadingZeros(localDayOfWeek, token.length);\n      // 1st, 2nd, ..., 7th\n      case \"co\":\n        return localize.ordinalNumber(localDayOfWeek, { unit: \"day\" });\n      case \"ccc\":\n        return localize.day(dayOfWeek, {\n          width: \"abbreviated\",\n          context: \"standalone\",\n        });\n      // T\n      case \"ccccc\":\n        return localize.day(dayOfWeek, {\n          width: \"narrow\",\n          context: \"standalone\",\n        });\n      // Tu\n      case \"cccccc\":\n        return localize.day(dayOfWeek, {\n          width: \"short\",\n          context: \"standalone\",\n        });\n      // Tuesday\n      case \"cccc\":\n      default:\n        return localize.day(dayOfWeek, {\n          width: \"wide\",\n          context: \"standalone\",\n        });\n    }\n  },\n\n  // ISO day of week\n  i: function (date, token, localize) {\n    const dayOfWeek = date.getDay();\n    const isoDayOfWeek = dayOfWeek === 0 ? 7 : dayOfWeek;\n    switch (token) {\n      // 2\n      case \"i\":\n        return String(isoDayOfWeek);\n      // 02\n      case \"ii\":\n        return addLeadingZeros(isoDayOfWeek, token.length);\n      // 2nd\n      case \"io\":\n        return localize.ordinalNumber(isoDayOfWeek, { unit: \"day\" });\n      // Tue\n      case \"iii\":\n        return localize.day(dayOfWeek, {\n          width: \"abbreviated\",\n          context: \"formatting\",\n        });\n      // T\n      case \"iiiii\":\n        return localize.day(dayOfWeek, {\n          width: \"narrow\",\n          context: \"formatting\",\n        });\n      // Tu\n      case \"iiiiii\":\n        return localize.day(dayOfWeek, {\n          width: \"short\",\n          context: \"formatting\",\n        });\n      // Tuesday\n      case \"iiii\":\n      default:\n        return localize.day(dayOfWeek, {\n          width: \"wide\",\n          context: \"formatting\",\n        });\n    }\n  },\n\n  // AM or PM\n  a: function (date, token, localize) {\n    const hours = date.getHours();\n    const dayPeriodEnumValue = hours / 12 >= 1 ? \"pm\" : \"am\";\n\n    switch (token) {\n      case \"a\":\n      case \"aa\":\n        return localize.dayPeriod(dayPeriodEnumValue, {\n          width: \"abbreviated\",\n          context: \"formatting\",\n        });\n      case \"aaa\":\n        return localize\n          .dayPeriod(dayPeriodEnumValue, {\n            width: \"abbreviated\",\n            context: \"formatting\",\n          })\n          .toLowerCase();\n      case \"aaaaa\":\n        return localize.dayPeriod(dayPeriodEnumValue, {\n          width: \"narrow\",\n          context: \"formatting\",\n        });\n      case \"aaaa\":\n      default:\n        return localize.dayPeriod(dayPeriodEnumValue, {\n          width: \"wide\",\n          context: \"formatting\",\n        });\n    }\n  },\n\n  // AM, PM, midnight, noon\n  b: function (date, token, localize) {\n    const hours = date.getHours();\n    let dayPeriodEnumValue;\n    if (hours === 12) {\n      dayPeriodEnumValue = dayPeriodEnum.noon;\n    } else if (hours === 0) {\n      dayPeriodEnumValue = dayPeriodEnum.midnight;\n    } else {\n      dayPeriodEnumValue = hours / 12 >= 1 ? \"pm\" : \"am\";\n    }\n\n    switch (token) {\n      case \"b\":\n      case \"bb\":\n        return localize.dayPeriod(dayPeriodEnumValue, {\n          width: \"abbreviated\",\n          context: \"formatting\",\n        });\n      case \"bbb\":\n        return localize\n          .dayPeriod(dayPeriodEnumValue, {\n            width: \"abbreviated\",\n            context: \"formatting\",\n          })\n          .toLowerCase();\n      case \"bbbbb\":\n        return localize.dayPeriod(dayPeriodEnumValue, {\n          width: \"narrow\",\n          context: \"formatting\",\n        });\n      case \"bbbb\":\n      default:\n        return localize.dayPeriod(dayPeriodEnumValue, {\n          width: \"wide\",\n          context: \"formatting\",\n        });\n    }\n  },\n\n  // in the morning, in the afternoon, in the evening, at night\n  B: function (date, token, localize) {\n    const hours = date.getHours();\n    let dayPeriodEnumValue;\n    if (hours >= 17) {\n      dayPeriodEnumValue = dayPeriodEnum.evening;\n    } else if (hours >= 12) {\n      dayPeriodEnumValue = dayPeriodEnum.afternoon;\n    } else if (hours >= 4) {\n      dayPeriodEnumValue = dayPeriodEnum.morning;\n    } else {\n      dayPeriodEnumValue = dayPeriodEnum.night;\n    }\n\n    switch (token) {\n      case \"B\":\n      case \"BB\":\n      case \"BBB\":\n        return localize.dayPeriod(dayPeriodEnumValue, {\n          width: \"abbreviated\",\n          context: \"formatting\",\n        });\n      case \"BBBBB\":\n        return localize.dayPeriod(dayPeriodEnumValue, {\n          width: \"narrow\",\n          context: \"formatting\",\n        });\n      case \"BBBB\":\n      default:\n        return localize.dayPeriod(dayPeriodEnumValue, {\n          width: \"wide\",\n          context: \"formatting\",\n        });\n    }\n  },\n\n  // Hour [1-12]\n  h: function (date, token, localize) {\n    if (token === \"ho\") {\n      let hours = date.getHours() % 12;\n      if (hours === 0) hours = 12;\n      return localize.ordinalNumber(hours, { unit: \"hour\" });\n    }\n\n    return lightFormatters.h(date, token);\n  },\n\n  // Hour [0-23]\n  H: function (date, token, localize) {\n    if (token === \"Ho\") {\n      return localize.ordinalNumber(date.getHours(), { unit: \"hour\" });\n    }\n\n    return lightFormatters.H(date, token);\n  },\n\n  // Hour [0-11]\n  K: function (date, token, localize) {\n    const hours = date.getHours() % 12;\n\n    if (token === \"Ko\") {\n      return localize.ordinalNumber(hours, { unit: \"hour\" });\n    }\n\n    return addLeadingZeros(hours, token.length);\n  },\n\n  // Hour [1-24]\n  k: function (date, token, localize) {\n    let hours = date.getHours();\n    if (hours === 0) hours = 24;\n\n    if (token === \"ko\") {\n      return localize.ordinalNumber(hours, { unit: \"hour\" });\n    }\n\n    return addLeadingZeros(hours, token.length);\n  },\n\n  // Minute\n  m: function (date, token, localize) {\n    if (token === \"mo\") {\n      return localize.ordinalNumber(date.getMinutes(), { unit: \"minute\" });\n    }\n\n    return lightFormatters.m(date, token);\n  },\n\n  // Second\n  s: function (date, token, localize) {\n    if (token === \"so\") {\n      return localize.ordinalNumber(date.getSeconds(), { unit: \"second\" });\n    }\n\n    return lightFormatters.s(date, token);\n  },\n\n  // Fraction of second\n  S: function (date, token) {\n    return lightFormatters.S(date, token);\n  },\n\n  // Timezone (ISO-8601. If offset is 0, output is always `'Z'`)\n  X: function (date, token, _localize) {\n    const timezoneOffset = date.getTimezoneOffset();\n\n    if (timezoneOffset === 0) {\n      return \"Z\";\n    }\n\n    switch (token) {\n      // Hours and optional minutes\n      case \"X\":\n        return formatTimezoneWithOptionalMinutes(timezoneOffset);\n\n      // Hours, minutes and optional seconds without `:` delimiter\n      // Note: neither ISO-8601 nor JavaScript supports seconds in timezone offsets\n      // so this token always has the same output as `XX`\n      case \"XXXX\":\n      case \"XX\": // Hours and minutes without `:` delimiter\n        return formatTimezone(timezoneOffset);\n\n      // Hours, minutes and optional seconds with `:` delimiter\n      // Note: neither ISO-8601 nor JavaScript supports seconds in timezone offsets\n      // so this token always has the same output as `XXX`\n      case \"XXXXX\":\n      case \"XXX\": // Hours and minutes with `:` delimiter\n      default:\n        return formatTimezone(timezoneOffset, \":\");\n    }\n  },\n\n  // Timezone (ISO-8601. If offset is 0, output is `'+00:00'` or equivalent)\n  x: function (date, token, _localize) {\n    const timezoneOffset = date.getTimezoneOffset();\n\n    switch (token) {\n      // Hours and optional minutes\n      case \"x\":\n        return formatTimezoneWithOptionalMinutes(timezoneOffset);\n\n      // Hours, minutes and optional seconds without `:` delimiter\n      // Note: neither ISO-8601 nor JavaScript supports seconds in timezone offsets\n      // so this token always has the same output as `xx`\n      case \"xxxx\":\n      case \"xx\": // Hours and minutes without `:` delimiter\n        return formatTimezone(timezoneOffset);\n\n      // Hours, minutes and optional seconds with `:` delimiter\n      // Note: neither ISO-8601 nor JavaScript supports seconds in timezone offsets\n      // so this token always has the same output as `xxx`\n      case \"xxxxx\":\n      case \"xxx\": // Hours and minutes with `:` delimiter\n      default:\n        return formatTimezone(timezoneOffset, \":\");\n    }\n  },\n\n  // Timezone (GMT)\n  O: function (date, token, _localize) {\n    const timezoneOffset = date.getTimezoneOffset();\n\n    switch (token) {\n      // Short\n      case \"O\":\n      case \"OO\":\n      case \"OOO\":\n        return \"GMT\" + formatTimezoneShort(timezoneOffset, \":\");\n      // Long\n      case \"OOOO\":\n      default:\n        return \"GMT\" + formatTimezone(timezoneOffset, \":\");\n    }\n  },\n\n  // Timezone (specific non-location)\n  z: function (date, token, _localize) {\n    const timezoneOffset = date.getTimezoneOffset();\n\n    switch (token) {\n      // Short\n      case \"z\":\n      case \"zz\":\n      case \"zzz\":\n        return \"GMT\" + formatTimezoneShort(timezoneOffset, \":\");\n      // Long\n      case \"zzzz\":\n      default:\n        return \"GMT\" + formatTimezone(timezoneOffset, \":\");\n    }\n  },\n\n  // Seconds timestamp\n  t: function (date, token, _localize) {\n    const timestamp = Math.trunc(+date / 1000);\n    return addLeadingZeros(timestamp, token.length);\n  },\n\n  // Milliseconds timestamp\n  T: function (date, token, _localize) {\n    return addLeadingZeros(+date, token.length);\n  },\n};\n\nfunction formatTimezoneShort(offset, delimiter = \"\") {\n  const sign = offset > 0 ? \"-\" : \"+\";\n  const absOffset = Math.abs(offset);\n  const hours = Math.trunc(absOffset / 60);\n  const minutes = absOffset % 60;\n  if (minutes === 0) {\n    return sign + String(hours);\n  }\n  return sign + String(hours) + delimiter + addLeadingZeros(minutes, 2);\n}\n\nfunction formatTimezoneWithOptionalMinutes(offset, delimiter) {\n  if (offset % 60 === 0) {\n    const sign = offset > 0 ? \"-\" : \"+\";\n    return sign + addLeadingZeros(Math.abs(offset) / 60, 2);\n  }\n  return formatTimezone(offset, delimiter);\n}\n\nfunction formatTimezone(offset, delimiter = \"\") {\n  const sign = offset > 0 ? \"-\" : \"+\";\n  const absOffset = Math.abs(offset);\n  const hours = addLeadingZeros(Math.trunc(absOffset / 60), 2);\n  const minutes = addLeadingZeros(absOffset % 60, 2);\n  return sign + hours + delimiter + minutes;\n}\n", "import { defaultLocale } from \"./_lib/defaultLocale.js\";\nimport { getDefaultOptions } from \"./_lib/defaultOptions.js\";\nimport { formatters } from \"./_lib/format/formatters.js\";\nimport { longFormatters } from \"./_lib/format/longFormatters.js\";\nimport {\n  isProtectedDayOfYearToken,\n  isProtectedWeekYearToken,\n  warnOrThrowProtectedError,\n} from \"./_lib/protectedTokens.js\";\nimport { isValid } from \"./isValid.js\";\nimport { toDate } from \"./toDate.js\";\n\n// Rexports of internal for libraries to use.\n// See: https://github.com/date-fns/date-fns/issues/3638#issuecomment-1877082874\nexport { formatters, longFormatters };\n\n// This RegExp consists of three parts separated by `|`:\n// - [yYQqMLwIdDecihHKkms]o matches any available ordinal number token\n//   (one of the certain letters followed by `o`)\n// - (\\w)\\1* matches any sequences of the same letter\n// - '' matches two quote characters in a row\n// - '(''|[^'])+('|$) matches anything surrounded by two quote characters ('),\n//   except a single quote symbol, which ends the sequence.\n//   Two quote characters do not end the sequence.\n//   If there is no matching single quote\n//   then the sequence will continue until the end of the string.\n// - . matches any single character unmatched by previous parts of the RegExps\nconst formattingTokensRegExp =\n  /[yYQqMLwIdDecihHKkms]o|(\\w)\\1*|''|'(''|[^'])+('|$)|./g;\n\n// This RegExp catches symbols escaped by quotes, and also\n// sequences of symbols P, p, and the combinations like `PPPPPPPppppp`\nconst longFormattingTokensRegExp = /P+p+|P+|p+|''|'(''|[^'])+('|$)|./g;\n\nconst escapedStringRegExp = /^'([^]*?)'?$/;\nconst doubleQuoteRegExp = /''/g;\nconst unescapedLatinCharacterRegExp = /[a-zA-Z]/;\n\nexport { format as formatDate };\n\n/**\n * The {@link format} function options.\n */\n\n/**\n * @name format\n * @alias formatDate\n * @category Common Helpers\n * @summary Format the date.\n *\n * @description\n * Return the formatted date string in the given format. The result may vary by locale.\n *\n * > ⚠️ Please note that the `format` tokens differ from Moment.js and other libraries.\n * > See: https://github.com/date-fns/date-fns/blob/master/docs/unicodeTokens.md\n *\n * The characters wrapped between two single quotes characters (') are escaped.\n * Two single quotes in a row, whether inside or outside a quoted sequence, represent a 'real' single quote.\n * (see the last example)\n *\n * Format of the string is based on Unicode Technical Standard #35:\n * https://www.unicode.org/reports/tr35/tr35-dates.html#Date_Field_Symbol_Table\n * with a few additions (see note 7 below the table).\n *\n * Accepted patterns:\n * | Unit                            | Pattern | Result examples                   | Notes |\n * |---------------------------------|---------|-----------------------------------|-------|\n * | Era                             | G..GGG  | AD, BC                            |       |\n * |                                 | GGGG    | Anno Domini, Before Christ        | 2     |\n * |                                 | GGGGG   | A, B                              |       |\n * | Calendar year                   | y       | 44, 1, 1900, 2017                 | 5     |\n * |                                 | yo      | 44th, 1st, 0th, 17th              | 5,7   |\n * |                                 | yy      | 44, 01, 00, 17                    | 5     |\n * |                                 | yyy     | 044, 001, 1900, 2017              | 5     |\n * |                                 | yyyy    | 0044, 0001, 1900, 2017            | 5     |\n * |                                 | yyyyy   | ...                               | 3,5   |\n * | Local week-numbering year       | Y       | 44, 1, 1900, 2017                 | 5     |\n * |                                 | Yo      | 44th, 1st, 1900th, 2017th         | 5,7   |\n * |                                 | YY      | 44, 01, 00, 17                    | 5,8   |\n * |                                 | YYY     | 044, 001, 1900, 2017              | 5     |\n * |                                 | YYYY    | 0044, 0001, 1900, 2017            | 5,8   |\n * |                                 | YYYYY   | ...                               | 3,5   |\n * | ISO week-numbering year         | R       | -43, 0, 1, 1900, 2017             | 5,7   |\n * |                                 | RR      | -43, 00, 01, 1900, 2017           | 5,7   |\n * |                                 | RRR     | -043, 000, 001, 1900, 2017        | 5,7   |\n * |                                 | RRRR    | -0043, 0000, 0001, 1900, 2017     | 5,7   |\n * |                                 | RRRRR   | ...                               | 3,5,7 |\n * | Extended year                   | u       | -43, 0, 1, 1900, 2017             | 5     |\n * |                                 | uu      | -43, 01, 1900, 2017               | 5     |\n * |                                 | uuu     | -043, 001, 1900, 2017             | 5     |\n * |                                 | uuuu    | -0043, 0001, 1900, 2017           | 5     |\n * |                                 | uuuuu   | ...                               | 3,5   |\n * | Quarter (formatting)            | Q       | 1, 2, 3, 4                        |       |\n * |                                 | Qo      | 1st, 2nd, 3rd, 4th                | 7     |\n * |                                 | QQ      | 01, 02, 03, 04                    |       |\n * |                                 | QQQ     | Q1, Q2, Q3, Q4                    |       |\n * |                                 | QQQQ    | 1st quarter, 2nd quarter, ...     | 2     |\n * |                                 | QQQQQ   | 1, 2, 3, 4                        | 4     |\n * | Quarter (stand-alone)           | q       | 1, 2, 3, 4                        |       |\n * |                                 | qo      | 1st, 2nd, 3rd, 4th                | 7     |\n * |                                 | qq      | 01, 02, 03, 04                    |       |\n * |                                 | qqq     | Q1, Q2, Q3, Q4                    |       |\n * |                                 | qqqq    | 1st quarter, 2nd quarter, ...     | 2     |\n * |                                 | qqqqq   | 1, 2, 3, 4                        | 4     |\n * | Month (formatting)              | M       | 1, 2, ..., 12                     |       |\n * |                                 | Mo      | 1st, 2nd, ..., 12th               | 7     |\n * |                                 | MM      | 01, 02, ..., 12                   |       |\n * |                                 | MMM     | Jan, Feb, ..., Dec                |       |\n * |                                 | MMMM    | January, February, ..., December  | 2     |\n * |                                 | MMMMM   | J, F, ..., D                      |       |\n * | Month (stand-alone)             | L       | 1, 2, ..., 12                     |       |\n * |                                 | Lo      | 1st, 2nd, ..., 12th               | 7     |\n * |                                 | LL      | 01, 02, ..., 12                   |       |\n * |                                 | LLL     | Jan, Feb, ..., Dec                |       |\n * |                                 | LLLL    | January, February, ..., December  | 2     |\n * |                                 | LLLLL   | J, F, ..., D                      |       |\n * | Local week of year              | w       | 1, 2, ..., 53                     |       |\n * |                                 | wo      | 1st, 2nd, ..., 53th               | 7     |\n * |                                 | ww      | 01, 02, ..., 53                   |       |\n * | ISO week of year                | I       | 1, 2, ..., 53                     | 7     |\n * |                                 | Io      | 1st, 2nd, ..., 53th               | 7     |\n * |                                 | II      | 01, 02, ..., 53                   | 7     |\n * | Day of month                    | d       | 1, 2, ..., 31                     |       |\n * |                                 | do      | 1st, 2nd, ..., 31st               | 7     |\n * |                                 | dd      | 01, 02, ..., 31                   |       |\n * | Day of year                     | D       | 1, 2, ..., 365, 366               | 9     |\n * |                                 | Do      | 1st, 2nd, ..., 365th, 366th       | 7     |\n * |                                 | DD      | 01, 02, ..., 365, 366             | 9     |\n * |                                 | DDD     | 001, 002, ..., 365, 366           |       |\n * |                                 | DDDD    | ...                               | 3     |\n * | Day of week (formatting)        | E..EEE  | Mon, Tue, Wed, ..., Sun           |       |\n * |                                 | EEEE    | Monday, Tuesday, ..., Sunday      | 2     |\n * |                                 | EEEEE   | M, T, W, T, F, S, S               |       |\n * |                                 | EEEEEE  | Mo, Tu, We, Th, Fr, Sa, Su        |       |\n * | ISO day of week (formatting)    | i       | 1, 2, 3, ..., 7                   | 7     |\n * |                                 | io      | 1st, 2nd, ..., 7th                | 7     |\n * |                                 | ii      | 01, 02, ..., 07                   | 7     |\n * |                                 | iii     | Mon, Tue, Wed, ..., Sun           | 7     |\n * |                                 | iiii    | Monday, Tuesday, ..., Sunday      | 2,7   |\n * |                                 | iiiii   | M, T, W, T, F, S, S               | 7     |\n * |                                 | iiiiii  | Mo, Tu, We, Th, Fr, Sa, Su        | 7     |\n * | Local day of week (formatting)  | e       | 2, 3, 4, ..., 1                   |       |\n * |                                 | eo      | 2nd, 3rd, ..., 1st                | 7     |\n * |                                 | ee      | 02, 03, ..., 01                   |       |\n * |                                 | eee     | Mon, Tue, Wed, ..., Sun           |       |\n * |                                 | eeee    | Monday, Tuesday, ..., Sunday      | 2     |\n * |                                 | eeeee   | M, T, W, T, F, S, S               |       |\n * |                                 | eeeeee  | Mo, Tu, We, Th, Fr, Sa, Su        |       |\n * | Local day of week (stand-alone) | c       | 2, 3, 4, ..., 1                   |       |\n * |                                 | co      | 2nd, 3rd, ..., 1st                | 7     |\n * |                                 | cc      | 02, 03, ..., 01                   |       |\n * |                                 | ccc     | Mon, Tue, Wed, ..., Sun           |       |\n * |                                 | cccc    | Monday, Tuesday, ..., Sunday      | 2     |\n * |                                 | ccccc   | M, T, W, T, F, S, S               |       |\n * |                                 | cccccc  | Mo, Tu, We, Th, Fr, Sa, Su        |       |\n * | AM, PM                          | a..aa   | AM, PM                            |       |\n * |                                 | aaa     | am, pm                            |       |\n * |                                 | aaaa    | a.m., p.m.                        | 2     |\n * |                                 | aaaaa   | a, p                              |       |\n * | AM, PM, noon, midnight          | b..bb   | AM, PM, noon, midnight            |       |\n * |                                 | bbb     | am, pm, noon, midnight            |       |\n * |                                 | bbbb    | a.m., p.m., noon, midnight        | 2     |\n * |                                 | bbbbb   | a, p, n, mi                       |       |\n * | Flexible day period             | B..BBB  | at night, in the morning, ...     |       |\n * |                                 | BBBB    | at night, in the morning, ...     | 2     |\n * |                                 | BBBBB   | at night, in the morning, ...     |       |\n * | Hour [1-12]                     | h       | 1, 2, ..., 11, 12                 |       |\n * |                                 | ho      | 1st, 2nd, ..., 11th, 12th         | 7     |\n * |                                 | hh      | 01, 02, ..., 11, 12               |       |\n * | Hour [0-23]                     | H       | 0, 1, 2, ..., 23                  |       |\n * |                                 | Ho      | 0th, 1st, 2nd, ..., 23rd          | 7     |\n * |                                 | HH      | 00, 01, 02, ..., 23               |       |\n * | Hour [0-11]                     | K       | 1, 2, ..., 11, 0                  |       |\n * |                                 | Ko      | 1st, 2nd, ..., 11th, 0th          | 7     |\n * |                                 | KK      | 01, 02, ..., 11, 00               |       |\n * | Hour [1-24]                     | k       | 24, 1, 2, ..., 23                 |       |\n * |                                 | ko      | 24th, 1st, 2nd, ..., 23rd         | 7     |\n * |                                 | kk      | 24, 01, 02, ..., 23               |       |\n * | Minute                          | m       | 0, 1, ..., 59                     |       |\n * |                                 | mo      | 0th, 1st, ..., 59th               | 7     |\n * |                                 | mm      | 00, 01, ..., 59                   |       |\n * | Second                          | s       | 0, 1, ..., 59                     |       |\n * |                                 | so      | 0th, 1st, ..., 59th               | 7     |\n * |                                 | ss      | 00, 01, ..., 59                   |       |\n * | Fraction of second              | S       | 0, 1, ..., 9                      |       |\n * |                                 | SS      | 00, 01, ..., 99                   |       |\n * |                                 | SSS     | 000, 001, ..., 999                |       |\n * |                                 | SSSS    | ...                               | 3     |\n * | Timezone (ISO-8601 w/ Z)        | X       | -08, +0530, Z                     |       |\n * |                                 | XX      | -0800, +0530, Z                   |       |\n * |                                 | XXX     | -08:00, +05:30, Z                 |       |\n * |                                 | XXXX    | -0800, +0530, Z, +123456          | 2     |\n * |                                 | XXXXX   | -08:00, +05:30, Z, +12:34:56      |       |\n * | Timezone (ISO-8601 w/o Z)       | x       | -08, +0530, +00                   |       |\n * |                                 | xx      | -0800, +0530, +0000               |       |\n * |                                 | xxx     | -08:00, +05:30, +00:00            | 2     |\n * |                                 | xxxx    | -0800, +0530, +0000, +123456      |       |\n * |                                 | xxxxx   | -08:00, +05:30, +00:00, +12:34:56 |       |\n * | Timezone (GMT)                  | O...OOO | GMT-8, GMT+5:30, GMT+0            |       |\n * |                                 | OOOO    | GMT-08:00, GMT+05:30, GMT+00:00   | 2     |\n * | Timezone (specific non-locat.)  | z...zzz | GMT-8, GMT+5:30, GMT+0            | 6     |\n * |                                 | zzzz    | GMT-08:00, GMT+05:30, GMT+00:00   | 2,6   |\n * | Seconds timestamp               | t       | 512969520                         | 7     |\n * |                                 | tt      | ...                               | 3,7   |\n * | Milliseconds timestamp          | T       | 512969520900                      | 7     |\n * |                                 | TT      | ...                               | 3,7   |\n * | Long localized date             | P       | 04/29/1453                        | 7     |\n * |                                 | PP      | Apr 29, 1453                      | 7     |\n * |                                 | PPP     | April 29th, 1453                  | 7     |\n * |                                 | PPPP    | Friday, April 29th, 1453          | 2,7   |\n * | Long localized time             | p       | 12:00 AM                          | 7     |\n * |                                 | pp      | 12:00:00 AM                       | 7     |\n * |                                 | ppp     | 12:00:00 AM GMT+2                 | 7     |\n * |                                 | pppp    | 12:00:00 AM GMT+02:00             | 2,7   |\n * | Combination of date and time    | Pp      | 04/29/1453, 12:00 AM              | 7     |\n * |                                 | PPpp    | Apr 29, 1453, 12:00:00 AM         | 7     |\n * |                                 | PPPppp  | April 29th, 1453 at ...           | 7     |\n * |                                 | PPPPpppp| Friday, April 29th, 1453 at ...   | 2,7   |\n * Notes:\n * 1. \"Formatting\" units (e.g. formatting quarter) in the default en-US locale\n *    are the same as \"stand-alone\" units, but are different in some languages.\n *    \"Formatting\" units are declined according to the rules of the language\n *    in the context of a date. \"Stand-alone\" units are always nominative singular:\n *\n *    `format(new Date(2017, 10, 6), 'do LLLL', {locale: cs}) //=> '6. listopad'`\n *\n *    `format(new Date(2017, 10, 6), 'do MMMM', {locale: cs}) //=> '6. listopadu'`\n *\n * 2. Any sequence of the identical letters is a pattern, unless it is escaped by\n *    the single quote characters (see below).\n *    If the sequence is longer than listed in table (e.g. `EEEEEEEEEEE`)\n *    the output will be the same as default pattern for this unit, usually\n *    the longest one (in case of ISO weekdays, `EEEE`). Default patterns for units\n *    are marked with \"2\" in the last column of the table.\n *\n *    `format(new Date(2017, 10, 6), 'MMM') //=> 'Nov'`\n *\n *    `format(new Date(2017, 10, 6), 'MMMM') //=> 'November'`\n *\n *    `format(new Date(2017, 10, 6), 'MMMMM') //=> 'N'`\n *\n *    `format(new Date(2017, 10, 6), 'MMMMMM') //=> 'November'`\n *\n *    `format(new Date(2017, 10, 6), 'MMMMMMM') //=> 'November'`\n *\n * 3. Some patterns could be unlimited length (such as `yyyyyyyy`).\n *    The output will be padded with zeros to match the length of the pattern.\n *\n *    `format(new Date(2017, 10, 6), 'yyyyyyyy') //=> '00002017'`\n *\n * 4. `QQQQQ` and `qqqqq` could be not strictly numerical in some locales.\n *    These tokens represent the shortest form of the quarter.\n *\n * 5. The main difference between `y` and `u` patterns are B.C. years:\n *\n *    | Year | `y` | `u` |\n *    |------|-----|-----|\n *    | AC 1 |   1 |   1 |\n *    | BC 1 |   1 |   0 |\n *    | BC 2 |   2 |  -1 |\n *\n *    Also `yy` always returns the last two digits of a year,\n *    while `uu` pads single digit years to 2 characters and returns other years unchanged:\n *\n *    | Year | `yy` | `uu` |\n *    |------|------|------|\n *    | 1    |   01 |   01 |\n *    | 14   |   14 |   14 |\n *    | 376  |   76 |  376 |\n *    | 1453 |   53 | 1453 |\n *\n *    The same difference is true for local and ISO week-numbering years (`Y` and `R`),\n *    except local week-numbering years are dependent on `options.weekStartsOn`\n *    and `options.firstWeekContainsDate` (compare [getISOWeekYear](https://date-fns.org/docs/getISOWeekYear)\n *    and [getWeekYear](https://date-fns.org/docs/getWeekYear)).\n *\n * 6. Specific non-location timezones are currently unavailable in `date-fns`,\n *    so right now these tokens fall back to GMT timezones.\n *\n * 7. These patterns are not in the Unicode Technical Standard #35:\n *    - `i`: ISO day of week\n *    - `I`: ISO week of year\n *    - `R`: ISO week-numbering year\n *    - `t`: seconds timestamp\n *    - `T`: milliseconds timestamp\n *    - `o`: ordinal number modifier\n *    - `P`: long localized date\n *    - `p`: long localized time\n *\n * 8. `YY` and `YYYY` tokens represent week-numbering years but they are often confused with years.\n *    You should enable `options.useAdditionalWeekYearTokens` to use them. See: https://github.com/date-fns/date-fns/blob/master/docs/unicodeTokens.md\n *\n * 9. `D` and `DD` tokens represent days of the year but they are often confused with days of the month.\n *    You should enable `options.useAdditionalDayOfYearTokens` to use them. See: https://github.com/date-fns/date-fns/blob/master/docs/unicodeTokens.md\n *\n * @param date - The original date\n * @param format - The string of tokens\n * @param options - An object with options\n *\n * @returns The formatted date string\n *\n * @throws `date` must not be Invalid Date\n * @throws `options.locale` must contain `localize` property\n * @throws `options.locale` must contain `formatLong` property\n * @throws use `yyyy` instead of `YYYY` for formatting years using [format provided] to the input [input provided]; see: https://github.com/date-fns/date-fns/blob/master/docs/unicodeTokens.md\n * @throws use `yy` instead of `YY` for formatting years using [format provided] to the input [input provided]; see: https://github.com/date-fns/date-fns/blob/master/docs/unicodeTokens.md\n * @throws use `d` instead of `D` for formatting days of the month using [format provided] to the input [input provided]; see: https://github.com/date-fns/date-fns/blob/master/docs/unicodeTokens.md\n * @throws use `dd` instead of `DD` for formatting days of the month using [format provided] to the input [input provided]; see: https://github.com/date-fns/date-fns/blob/master/docs/unicodeTokens.md\n * @throws format string contains an unescaped latin alphabet character\n *\n * @example\n * // Represent 11 February 2014 in middle-endian format:\n * const result = format(new Date(2014, 1, 11), 'MM/dd/yyyy')\n * //=> '02/11/2014'\n *\n * @example\n * // Represent 2 July 2014 in Esperanto:\n * import { eoLocale } from 'date-fns/locale/eo'\n * const result = format(new Date(2014, 6, 2), \"do 'de' MMMM yyyy\", {\n *   locale: eoLocale\n * })\n * //=> '2-a de julio 2014'\n *\n * @example\n * // Escape string by single quote characters:\n * const result = format(new Date(2014, 6, 2, 15), \"h 'o''clock'\")\n * //=> \"3 o'clock\"\n */\nexport function format(date, formatStr, options) {\n  const defaultOptions = getDefaultOptions();\n  const locale = options?.locale ?? defaultOptions.locale ?? defaultLocale;\n\n  const firstWeekContainsDate =\n    options?.firstWeekContainsDate ??\n    options?.locale?.options?.firstWeekContainsDate ??\n    defaultOptions.firstWeekContainsDate ??\n    defaultOptions.locale?.options?.firstWeekContainsDate ??\n    1;\n\n  const weekStartsOn =\n    options?.weekStartsOn ??\n    options?.locale?.options?.weekStartsOn ??\n    defaultOptions.weekStartsOn ??\n    defaultOptions.locale?.options?.weekStartsOn ??\n    0;\n\n  const originalDate = toDate(date, options?.in);\n\n  if (!isValid(originalDate)) {\n    throw new RangeError(\"Invalid time value\");\n  }\n\n  let parts = formatStr\n    .match(longFormattingTokensRegExp)\n    .map((substring) => {\n      const firstCharacter = substring[0];\n      if (firstCharacter === \"p\" || firstCharacter === \"P\") {\n        const longFormatter = longFormatters[firstCharacter];\n        return longFormatter(substring, locale.formatLong);\n      }\n      return substring;\n    })\n    .join(\"\")\n    .match(formattingTokensRegExp)\n    .map((substring) => {\n      // Replace two single quote characters with one single quote character\n      if (substring === \"''\") {\n        return { isToken: false, value: \"'\" };\n      }\n\n      const firstCharacter = substring[0];\n      if (firstCharacter === \"'\") {\n        return { isToken: false, value: cleanEscapedString(substring) };\n      }\n\n      if (formatters[firstCharacter]) {\n        return { isToken: true, value: substring };\n      }\n\n      if (firstCharacter.match(unescapedLatinCharacterRegExp)) {\n        throw new RangeError(\n          \"Format string contains an unescaped latin alphabet character `\" +\n            firstCharacter +\n            \"`\",\n        );\n      }\n\n      return { isToken: false, value: substring };\n    });\n\n  // invoke localize preprocessor (only for french locales at the moment)\n  if (locale.localize.preprocessor) {\n    parts = locale.localize.preprocessor(originalDate, parts);\n  }\n\n  const formatterOptions = {\n    firstWeekContainsDate,\n    weekStartsOn,\n    locale,\n  };\n\n  return parts\n    .map((part) => {\n      if (!part.isToken) return part.value;\n\n      const token = part.value;\n\n      if (\n        (!options?.useAdditionalWeekYearTokens &&\n          isProtectedWeekYearToken(token)) ||\n        (!options?.useAdditionalDayOfYearTokens &&\n          isProtectedDayOfYearToken(token))\n      ) {\n        warnOrThrowProtectedError(token, formatStr, String(date));\n      }\n\n      const formatter = formatters[token[0]];\n      return formatter(originalDate, token, locale.localize, formatterOptions);\n    })\n    .join(\"\");\n}\n\nfunction cleanEscapedString(input) {\n  const matched = input.match(escapedStringRegExp);\n\n  if (!matched) {\n    return input;\n  }\n\n  return matched[1].replace(doubleQuoteRegExp, \"'\");\n}\n\n// Fallback for modularized imports:\nexport default format;\n", "import { normalizeDates } from \"./_lib/normalizeDates.js\";\n\n/**\n * The {@link isSameMonth} function options.\n */\n\n/**\n * @name isSameMonth\n * @category Month Helpers\n * @summary Are the given dates in the same month (and year)?\n *\n * @description\n * Are the given dates in the same month (and year)?\n *\n * @param laterDate - The first date to check\n * @param earlierDate - The second date to check\n * @param options - An object with options\n *\n * @returns The dates are in the same month (and year)\n *\n * @example\n * // Are 2 September 2014 and 25 September 2014 in the same month?\n * const result = isSameMonth(new Date(2014, 8, 2), new Date(2014, 8, 25))\n * //=> true\n *\n * @example\n * // Are 2 September 2014 and 25 September 2015 in the same month?\n * const result = isSameMonth(new Date(2014, 8, 2), new Date(2015, 8, 25))\n * //=> false\n */\nexport function isSameMonth(laterDate, earlierDate, options) {\n  const [laterDate_, earlierDate_] = normalizeDates(\n    options?.in,\n    laterDate,\n    earlierDate,\n  );\n  return (\n    laterDate_.getFullYear() === earlierDate_.getFullYear() &&\n    laterDate_.getMonth() === earlierDate_.getMonth()\n  );\n}\n\n// Fallback for modularized imports:\nexport default isSameMonth;\n", "import { toDate } from \"./toDate.js\";\n\n/**\n * The {@link startOfDay} function options.\n */\n\n/**\n * @name startOfDay\n * @category Day Helpers\n * @summary Return the start of a day for the given date.\n *\n * @description\n * Return the start of a day for the given date.\n * The result will be in the local timezone.\n *\n * @typeParam DateType - The `Date` type, the function operates on. Gets inferred from passed arguments. Allows to use extensions like [`UTCDate`](https://github.com/date-fns/utc).\n * @typeParam ResultDate - The result `Date` type, it is the type returned from the context function if it is passed, or inferred from the arguments.\n *\n * @param date - The original date\n * @param options - The options\n *\n * @returns The start of a day\n *\n * @example\n * // The start of a day for 2 September 2014 11:55:00:\n * const result = startOfDay(new Date(2014, 8, 2, 11, 55, 0))\n * //=> Tue Sep 02 2014 00:00:00\n */\nexport function startOfDay(date, options) {\n  const _date = toDate(date, options?.in);\n  _date.setHours(0, 0, 0, 0);\n  return _date;\n}\n\n// Fallback for modularized imports:\nexport default startOfDay;\n", "import { constructFromSymbol } from \"./constants.js\";\n\n/**\n * @name constructFrom\n * @category Generic Helpers\n * @summary Constructs a date using the reference date and the value\n *\n * @description\n * The function constructs a new date using the constructor from the reference\n * date and the given value. It helps to build generic functions that accept\n * date extensions.\n *\n * It defaults to `Date` if the passed reference date is a number or a string.\n *\n * Starting from v3.7.0, it allows to construct a date using `[Symbol.for(\"constructDateFrom\")]`\n * enabling to transfer extra properties from the reference date to the new date.\n * It's useful for extensions like [`TZDate`](https://github.com/date-fns/tz)\n * that accept a time zone as a constructor argument.\n *\n * @typeParam DateType - The `Date` type, the function operates on. Gets inferred from passed arguments. Allows to use extensions like [`UTCDate`](https://github.com/date-fns/utc).\n *\n * @param date - The reference date to take constructor from\n * @param value - The value to create the date\n *\n * @returns Date initialized using the given date and value\n *\n * @example\n * import { constructFrom } from \"./constructFrom/date-fns\";\n *\n * // A function that clones a date preserving the original type\n * function cloneDate<DateType extends Date>(date: DateType): DateType {\n *   return constructFrom(\n *     date, // Use constructor from the given date\n *     date.getTime() // Use the date value to create a new date\n *   );\n * }\n */\nexport function constructFrom(date, value) {\n  if (typeof date === \"function\") return date(value);\n\n  if (date && typeof date === \"object\" && constructFromSymbol in date)\n    return date[constructFromSymbol](value);\n\n  if (date instanceof Date) return new date.constructor(value);\n\n  return new Date(value);\n}\n\n// Fallback for modularized imports:\nexport default constructFrom;\n", "import { getDefaultOptions } from \"./_lib/defaultOptions.js\";\nimport { constructFrom } from \"./constructFrom.js\";\nimport { getWeekYear } from \"./getWeekYear.js\";\nimport { startOfWeek } from \"./startOfWeek.js\";\n\n/**\n * The {@link startOfWeekYear} function options.\n */\n\n/**\n * @name startOfWeekYear\n * @category Week-Numbering Year Helpers\n * @summary Return the start of a local week-numbering year for the given date.\n *\n * @description\n * Return the start of a local week-numbering year.\n * The exact calculation depends on the values of\n * `options.weekStartsOn` (which is the index of the first day of the week)\n * and `options.firstWeekContainsDate` (which is the day of January, which is always in\n * the first week of the week-numbering year)\n *\n * Week numbering: https://en.wikipedia.org/wiki/Week#The_ISO_week_date_system\n *\n * @typeParam DateType - The `Date` type, the function operates on. Gets inferred from passed arguments. Allows to use extensions like [`UTCDate`](https://github.com/date-fns/utc).\n * @typeParam ResultDate - The result `Date` type.\n *\n * @param date - The original date\n * @param options - An object with options\n *\n * @returns The start of a week-numbering year\n *\n * @example\n * // The start of an a week-numbering year for 2 July 2005 with default settings:\n * const result = startOfWeekYear(new Date(2005, 6, 2))\n * //=> Sun Dec 26 2004 00:00:00\n *\n * @example\n * // The start of a week-numbering year for 2 July 2005\n * // if Monday is the first day of week\n * // and 4 January is always in the first week of the year:\n * const result = startOfWeekYear(new Date(2005, 6, 2), {\n *   weekStartsOn: 1,\n *   firstWeekContainsDate: 4\n * })\n * //=> Mon Jan 03 2005 00:00:00\n */\nexport function startOfWeekYear(date, options) {\n  const defaultOptions = getDefaultOptions();\n  const firstWeekContainsDate =\n    options?.firstWeekContainsDate ??\n    options?.locale?.options?.firstWeekContainsDate ??\n    defaultOptions.firstWeekContainsDate ??\n    defaultOptions.locale?.options?.firstWeekContainsDate ??\n    1;\n\n  const year = getWeekYear(date, options);\n  const firstWeek = constructFrom(options?.in || date, 0);\n  firstWeek.setFullYear(year, 0, firstWeekContainsDate);\n  firstWeek.setHours(0, 0, 0, 0);\n  const _date = startOfWeek(firstWeek, options);\n  return _date;\n}\n\n// Fallback for modularized imports:\nexport default startOfWeekYear;\n", "import { millisecondsInWeek } from \"./constants.js\";\nimport { startOfWeek } from \"./startOfWeek.js\";\nimport { startOfWeekYear } from \"./startOfWeekYear.js\";\nimport { toDate } from \"./toDate.js\";\n\n/**\n * The {@link getWeek} function options.\n */\n\n/**\n * @name getWeek\n * @category Week Helpers\n * @summary Get the local week index of the given date.\n *\n * @description\n * Get the local week index of the given date.\n * The exact calculation depends on the values of\n * `options.weekStartsOn` (which is the index of the first day of the week)\n * and `options.firstWeekContainsDate` (which is the day of January, which is always in\n * the first week of the week-numbering year)\n *\n * Week numbering: https://en.wikipedia.org/wiki/Week#The_ISO_week_date_system\n *\n * @param date - The given date\n * @param options - An object with options\n *\n * @returns The week\n *\n * @example\n * // Which week of the local week numbering year is 2 January 2005 with default options?\n * const result = getWeek(new Date(2005, 0, 2))\n * //=> 2\n *\n * @example\n * // Which week of the local week numbering year is 2 January 2005,\n * // if Monday is the first day of the week,\n * // and the first week of the year always contains 4 January?\n * const result = getWeek(new Date(2005, 0, 2), {\n *   weekStartsOn: 1,\n *   firstWeekContainsDate: 4\n * })\n * //=> 53\n */\nexport function getWeek(date, options) {\n  const _date = toDate(date, options?.in);\n  const diff = +startOfWeek(_date, options) - +startOfWeekYear(_date, options);\n\n  // Round the number of weeks to the nearest integer because the number of\n  // milliseconds in a week is not constant (e.g. it's different in the week of\n  // the daylight saving time clock shift).\n  return Math.round(diff / millisecondsInWeek) + 1;\n}\n\n// Fallback for modularized imports:\nexport default getWeek;\n", "import { millisecondsInMinute } from \"./constants.js\";\nimport { toDate } from \"./toDate.js\";\n\n/**\n * The {@link addMinutes} function options.\n */\n\n/**\n * @name addMinutes\n * @category Minute Helpers\n * @summary Add the specified number of minutes to the given date.\n *\n * @description\n * Add the specified number of minutes to the given date.\n *\n * @typeParam DateType - The `Date` type, the function operates on. Gets inferred from passed arguments. Allows to use extensions like [`UTCDate`](https://github.com/date-fns/utc).\n * @typeParam ResultDate - The result `Date` type, it is the type returned from the context function if it is passed, or inferred from the arguments.\n *\n * @param date - The date to be changed\n * @param amount - The amount of minutes to be added.\n * @param options - An object with options\n *\n * @returns The new date with the minutes added\n *\n * @example\n * // Add 30 minutes to 10 July 2014 12:00:00:\n * const result = addMinutes(new Date(2014, 6, 10, 12, 0), 30)\n * //=> Thu Jul 10 2014 12:30:00\n */\nexport function addMinutes(date, amount, options) {\n  const _date = toDate(date, options?.in);\n  _date.setTime(_date.getTime() + amount * millisecondsInMinute);\n  return _date;\n}\n\n// Fallback for modularized imports:\nexport default addMinutes;\n", "import { toDate } from \"./toDate.js\";\n\n/**\n * The {@link getDay} function options.\n */\n\n/**\n * @name getDay\n * @category Weekday Helpers\n * @summary Get the day of the week of the given date.\n *\n * @description\n * Get the day of the week of the given date.\n *\n * @param date - The given date\n * @param options - The options\n *\n * @returns The day of week, 0 represents Sunday\n *\n * @example\n * // Which day of the week is 29 February 2012?\n * const result = getDay(new Date(2012, 1, 29))\n * //=> 3\n */\nexport function getDay(date, options) {\n  return toDate(date, options?.in).getDay();\n}\n\n// Fallback for modularized imports:\nexport default getDay;\n", "import { constructFrom } from \"./constructFrom.js\";\nimport { toDate } from \"./toDate.js\";\n\n/**\n * The {@link max} function options.\n */\n\n/**\n * @name max\n * @category Common Helpers\n * @summary Return the latest of the given dates.\n *\n * @description\n * Return the latest of the given dates.\n *\n * @typeParam DateType - The `Date` type, the function operates on. Gets inferred from passed arguments. Allows to use extensions like [`UTCDate`](https://github.com/date-fns/utc).\n * @typeParam ResultDate - The result `Date` type, it is the type returned from the context function if it is passed, or inferred from the arguments.\n *\n * @param dates - The dates to compare\n *\n * @returns The latest of the dates\n *\n * @example\n * // Which of these dates is the latest?\n * const result = max([\n *   new Date(1989, 6, 10),\n *   new Date(1987, 1, 11),\n *   new Date(1995, 6, 2),\n *   new Date(1990, 0, 1)\n * ])\n * //=> Sun Jul 02 1995 00:00:00\n */\nexport function max(dates, options) {\n  let result;\n  let context = options?.in;\n\n  dates.forEach((date) => {\n    // Use the first date object as the context function\n    if (!context && typeof date === \"object\")\n      context = constructFrom.bind(null, date);\n\n    const date_ = toDate(date, context);\n    if (!result || result < date_ || isNaN(+date_)) result = date_;\n  });\n\n  return constructFrom(context, result || NaN);\n}\n\n// Fallback for modularized imports:\nexport default max;\n", "import { toDate } from \"./toDate.js\";\n\n/**\n * The {@link setMinutes} function options.\n */\n\n/**\n * @name setMinutes\n * @category Minute Helpers\n * @summary Set the minutes to the given date.\n *\n * @description\n * Set the minutes to the given date.\n *\n * @typeParam DateType - The `Date` type, the function operates on. Gets inferred from passed arguments. Allows using extensions like [`UTCDate`](https://github.com/date-fns/utc).\n * @typeParam ResultDate - The result `Date` type, returned from the context function, or inferred from the arguments.\n *\n * @param date - The date to be changed\n * @param minutes - The minutes of the new date\n * @param options - An object with options\n *\n * @returns The new date with the minutes set\n *\n * @example\n * // Set 45 minutes to 1 September 2014 11:30:40:\n * const result = setMinutes(new Date(2014, 8, 1, 11, 30, 40), 45)\n * //=> Mon Sep 01 2014 11:45:40\n */\nexport function setMinutes(date, minutes, options) {\n  const date_ = toDate(date, options?.in);\n  date_.setMinutes(minutes);\n  return date_;\n}\n\n// Fallback for modularized imports:\nexport default setMinutes;\n", "import { toDate } from \"./toDate.js\";\n\n/**\n * The {@link startOfMonth} function options.\n */\n\n/**\n * @name startOfMonth\n * @category Month Helpers\n * @summary Return the start of a month for the given date.\n *\n * @description\n * Return the start of a month for the given date. The result will be in the local timezone.\n *\n * @typeParam DateType - The `Date` type, the function operates on. Gets inferred from passed arguments.\n * Allows to use extensions like [`UTCDate`](https://github.com/date-fns/utc).\n * @typeParam ResultDate - The result `Date` type, it is the type returned from the context function if it is passed,\n * or inferred from the arguments.\n *\n * @param date - The original date\n * @param options - An object with options\n *\n * @returns The start of a month\n *\n * @example\n * // The start of a month for 2 September 2014 11:55:00:\n * const result = startOfMonth(new Date(2014, 8, 2, 11, 55, 0))\n * //=> Mon Sep 01 2014 00:00:00\n */\nexport function startOfMonth(date, options) {\n  const _date = toDate(date, options?.in);\n  _date.setDate(1);\n  _date.setHours(0, 0, 0, 0);\n  return _date;\n}\n\n// Fallback for modularized imports:\nexport default startOfMonth;\n", "import { addQuarters } from \"./addQuarters.js\";\n\n/**\n * The {@link subQuarters} function options.\n */\n\n/**\n * @name subQuarters\n * @category Quarter Helpers\n * @summary Subtract the specified number of year quarters from the given date.\n *\n * @description\n * Subtract the specified number of year quarters from the given date.\n *\n * @typeParam DateType - The `Date` type, the function operates on. Gets inferred from passed arguments. Allows to use extensions like [`UTCDate`](https://github.com/date-fns/utc).\n * @typeParam ResultDate - The result `Date` type, it is the type returned from the context function if it is passed, or inferred from the arguments.\n *\n * @param date - The date to be changed\n * @param amount - The amount of quarters to be subtracted.\n * @param options - An object with options\n *\n * @returns The new date with the quarters subtracted\n *\n * @example\n * // Subtract 3 quarters from 1 September 2014:\n * const result = subQuarters(new Date(2014, 8, 1), 3)\n * //=> Sun Dec 01 2013 00:00:00\n */\nexport function subQuarters(date, amount, options) {\n  return addQuarters(date, -amount, options);\n}\n\n// Fallback for modularized imports:\nexport default subQuarters;\n", "import {\n  millisecondsInHour,\n  millisecondsInMinute,\n} from \"./constants.js\";\nimport { constructFrom } from \"./constructFrom.js\";\nimport { toDate } from \"./toDate.js\";\n\n/**\n * The {@link parseISO} function options.\n */\n\n/**\n * @name parseISO\n * @category Common Helpers\n * @summary Parse ISO string\n *\n * @description\n * Parse the given string in ISO 8601 format and return an instance of Date.\n *\n * Function accepts complete ISO 8601 formats as well as partial implementations.\n * ISO 8601: http://en.wikipedia.org/wiki/ISO_8601\n *\n * If the argument isn't a string, the function cannot parse the string or\n * the values are invalid, it returns Invalid Date.\n *\n * @typeParam DateType - The `Date` type, the function operates on. Gets inferred from passed arguments. Allows to use extensions like [`UTCDate`](https://github.com/date-fns/utc).\n * @typeParam ResultDate - The result `Date` type, it is the type returned from the context function if it is passed, or inferred from the arguments.\n *\n * @param argument - The value to convert\n * @param options - An object with options\n *\n * @returns The parsed date in the local time zone\n *\n * @example\n * // Convert string '2014-02-11T11:30:30' to date:\n * const result = parseISO('2014-02-11T11:30:30')\n * //=> Tue Feb 11 2014 11:30:30\n *\n * @example\n * // Convert string '+02014101' to date,\n * // if the additional number of digits in the extended year format is 1:\n * const result = parseISO('+02014101', { additionalDigits: 1 })\n * //=> Fri Apr 11 2014 00:00:00\n */\nexport function parseISO(argument, options) {\n  const invalidDate = () => constructFrom(options?.in, NaN);\n\n  const additionalDigits = options?.additionalDigits ?? 2;\n  const dateStrings = splitDateString(argument);\n\n  let date;\n  if (dateStrings.date) {\n    const parseYearResult = parseYear(dateStrings.date, additionalDigits);\n    date = parseDate(parseYearResult.restDateString, parseYearResult.year);\n  }\n\n  if (!date || isNaN(+date)) return invalidDate();\n\n  const timestamp = +date;\n  let time = 0;\n  let offset;\n\n  if (dateStrings.time) {\n    time = parseTime(dateStrings.time);\n    if (isNaN(time)) return invalidDate();\n  }\n\n  if (dateStrings.timezone) {\n    offset = parseTimezone(dateStrings.timezone);\n    if (isNaN(offset)) return invalidDate();\n  } else {\n    const tmpDate = new Date(timestamp + time);\n    const result = toDate(0, options?.in);\n    result.setFullYear(\n      tmpDate.getUTCFullYear(),\n      tmpDate.getUTCMonth(),\n      tmpDate.getUTCDate(),\n    );\n    result.setHours(\n      tmpDate.getUTCHours(),\n      tmpDate.getUTCMinutes(),\n      tmpDate.getUTCSeconds(),\n      tmpDate.getUTCMilliseconds(),\n    );\n    return result;\n  }\n\n  return toDate(timestamp + time + offset, options?.in);\n}\n\nconst patterns = {\n  dateTimeDelimiter: /[T ]/,\n  timeZoneDelimiter: /[Z ]/i,\n  timezone: /([Z+-].*)$/,\n};\n\nconst dateRegex =\n  /^-?(?:(\\d{3})|(\\d{2})(?:-?(\\d{2}))?|W(\\d{2})(?:-?(\\d{1}))?|)$/;\nconst timeRegex =\n  /^(\\d{2}(?:[.,]\\d*)?)(?::?(\\d{2}(?:[.,]\\d*)?))?(?::?(\\d{2}(?:[.,]\\d*)?))?$/;\nconst timezoneRegex = /^([+-])(\\d{2})(?::?(\\d{2}))?$/;\n\nfunction splitDateString(dateString) {\n  const dateStrings = {};\n  const array = dateString.split(patterns.dateTimeDelimiter);\n  let timeString;\n\n  // The regex match should only return at maximum two array elements.\n  // [date], [time], or [date, time].\n  if (array.length > 2) {\n    return dateStrings;\n  }\n\n  if (/:/.test(array[0])) {\n    timeString = array[0];\n  } else {\n    dateStrings.date = array[0];\n    timeString = array[1];\n    if (patterns.timeZoneDelimiter.test(dateStrings.date)) {\n      dateStrings.date = dateString.split(patterns.timeZoneDelimiter)[0];\n      timeString = dateString.substr(\n        dateStrings.date.length,\n        dateString.length,\n      );\n    }\n  }\n\n  if (timeString) {\n    const token = patterns.timezone.exec(timeString);\n    if (token) {\n      dateStrings.time = timeString.replace(token[1], \"\");\n      dateStrings.timezone = token[1];\n    } else {\n      dateStrings.time = timeString;\n    }\n  }\n\n  return dateStrings;\n}\n\nfunction parseYear(dateString, additionalDigits) {\n  const regex = new RegExp(\n    \"^(?:(\\\\d{4}|[+-]\\\\d{\" +\n      (4 + additionalDigits) +\n      \"})|(\\\\d{2}|[+-]\\\\d{\" +\n      (2 + additionalDigits) +\n      \"})$)\",\n  );\n\n  const captures = dateString.match(regex);\n  // Invalid ISO-formatted year\n  if (!captures) return { year: NaN, restDateString: \"\" };\n\n  const year = captures[1] ? parseInt(captures[1]) : null;\n  const century = captures[2] ? parseInt(captures[2]) : null;\n\n  // either year or century is null, not both\n  return {\n    year: century === null ? year : century * 100,\n    restDateString: dateString.slice((captures[1] || captures[2]).length),\n  };\n}\n\nfunction parseDate(dateString, year) {\n  // Invalid ISO-formatted year\n  if (year === null) return new Date(NaN);\n\n  const captures = dateString.match(dateRegex);\n  // Invalid ISO-formatted string\n  if (!captures) return new Date(NaN);\n\n  const isWeekDate = !!captures[4];\n  const dayOfYear = parseDateUnit(captures[1]);\n  const month = parseDateUnit(captures[2]) - 1;\n  const day = parseDateUnit(captures[3]);\n  const week = parseDateUnit(captures[4]);\n  const dayOfWeek = parseDateUnit(captures[5]) - 1;\n\n  if (isWeekDate) {\n    if (!validateWeekDate(year, week, dayOfWeek)) {\n      return new Date(NaN);\n    }\n    return dayOfISOWeekYear(year, week, dayOfWeek);\n  } else {\n    const date = new Date(0);\n    if (\n      !validateDate(year, month, day) ||\n      !validateDayOfYearDate(year, dayOfYear)\n    ) {\n      return new Date(NaN);\n    }\n    date.setUTCFullYear(year, month, Math.max(dayOfYear, day));\n    return date;\n  }\n}\n\nfunction parseDateUnit(value) {\n  return value ? parseInt(value) : 1;\n}\n\nfunction parseTime(timeString) {\n  const captures = timeString.match(timeRegex);\n  if (!captures) return NaN; // Invalid ISO-formatted time\n\n  const hours = parseTimeUnit(captures[1]);\n  const minutes = parseTimeUnit(captures[2]);\n  const seconds = parseTimeUnit(captures[3]);\n\n  if (!validateTime(hours, minutes, seconds)) {\n    return NaN;\n  }\n\n  return (\n    hours * millisecondsInHour + minutes * millisecondsInMinute + seconds * 1000\n  );\n}\n\nfunction parseTimeUnit(value) {\n  return (value && parseFloat(value.replace(\",\", \".\"))) || 0;\n}\n\nfunction parseTimezone(timezoneString) {\n  if (timezoneString === \"Z\") return 0;\n\n  const captures = timezoneString.match(timezoneRegex);\n  if (!captures) return 0;\n\n  const sign = captures[1] === \"+\" ? -1 : 1;\n  const hours = parseInt(captures[2]);\n  const minutes = (captures[3] && parseInt(captures[3])) || 0;\n\n  if (!validateTimezone(hours, minutes)) {\n    return NaN;\n  }\n\n  return sign * (hours * millisecondsInHour + minutes * millisecondsInMinute);\n}\n\nfunction dayOfISOWeekYear(isoWeekYear, week, day) {\n  const date = new Date(0);\n  date.setUTCFullYear(isoWeekYear, 0, 4);\n  const fourthOfJanuaryDay = date.getUTCDay() || 7;\n  const diff = (week - 1) * 7 + day + 1 - fourthOfJanuaryDay;\n  date.setUTCDate(date.getUTCDate() + diff);\n  return date;\n}\n\n// Validation functions\n\n// February is null to handle the leap year (using ||)\nconst daysInMonths = [31, null, 31, 30, 31, 30, 31, 31, 30, 31, 30, 31];\n\nfunction isLeapYearIndex(year) {\n  return year % 400 === 0 || (year % 4 === 0 && year % 100 !== 0);\n}\n\nfunction validateDate(year, month, date) {\n  return (\n    month >= 0 &&\n    month <= 11 &&\n    date >= 1 &&\n    date <= (daysInMonths[month] || (isLeapYearIndex(year) ? 29 : 28))\n  );\n}\n\nfunction validateDayOfYearDate(year, dayOfYear) {\n  return dayOfYear >= 1 && dayOfYear <= (isLeapYearIndex(year) ? 366 : 365);\n}\n\nfunction validateWeekDate(_year, week, day) {\n  return week >= 1 && week <= 53 && day >= 0 && day <= 6;\n}\n\nfunction validateTime(hours, minutes, seconds) {\n  if (hours === 24) {\n    return minutes === 0 && seconds === 0;\n  }\n\n  return (\n    seconds >= 0 &&\n    seconds < 60 &&\n    minutes >= 0 &&\n    minutes < 60 &&\n    hours >= 0 &&\n    hours < 25\n  );\n}\n\nfunction validateTimezone(_hours, minutes) {\n  return minutes >= 0 && minutes <= 59;\n}\n\n// Fallback for modularized imports:\nexport default parseISO;\n", "import { setMonth } from \"./setMonth.js\";\nimport { toDate } from \"./toDate.js\";\n\n/**\n * The {@link setQuarter} function options.\n */\n\n/**\n * @name setQuarter\n * @category Quarter Helpers\n * @summary Set the year quarter to the given date.\n *\n * @description\n * Set the year quarter to the given date.\n *\n * @typeParam DateType - The `Date` type, the function operates on. Gets inferred from passed arguments. Allows to use extensions like [`UTCDate`](https://github.com/date-fns/utc).\n * @typeParam ResultDate - The result `Date` type, it is the type returned from the context function if it is passed, or inferred from the arguments.\n *\n * @param date - The date to be changed\n * @param quarter - The quarter of the new date\n * @param options - The options\n *\n * @returns The new date with the quarter set\n *\n * @example\n * // Set the 2nd quarter to 2 July 2014:\n * const result = setQuarter(new Date(2014, 6, 2), 2)\n * //=> Wed Apr 02 2014 00:00:00\n */\nexport function setQuarter(date, quarter, options) {\n  const date_ = toDate(date, options?.in);\n  const oldQuarter = Math.trunc(date_.getMonth() / 3) + 1;\n  const diff = quarter - oldQuarter;\n  return setMonth(date_, date_.getMonth() + diff * 3);\n}\n\n// Fallback for modularized imports:\nexport default setQuarter;\n", "import { toDate } from \"./toDate.js\";\n\n/**\n * The {@link endOfMonth} function options.\n */\n\n/**\n * @name endOfMonth\n * @category Month Helpers\n * @summary Return the end of a month for the given date.\n *\n * @description\n * Return the end of a month for the given date.\n * The result will be in the local timezone.\n *\n * @typeParam DateType - The `Date` type, the function operates on. Gets inferred from passed arguments. Allows to use extensions like [`UTCDate`](https://github.com/date-fns/utc).\n * @typeParam ResultDate - The result `Date` type, it is the type returned from the context function if it is passed, or inferred from the arguments.\n *\n * @param date - The original date\n * @param options - An object with options\n *\n * @returns The end of a month\n *\n * @example\n * // The end of a month for 2 September 2014 11:55:00:\n * const result = endOfMonth(new Date(2014, 8, 2, 11, 55, 0))\n * //=> Tue Sep 30 2014 23:59:59.999\n */\nexport function endOfMonth(date, options) {\n  const _date = toDate(date, options?.in);\n  const month = _date.getMonth();\n  _date.setFullYear(_date.getFullYear(), month + 1, 0);\n  _date.setHours(23, 59, 59, 999);\n  return _date;\n}\n\n// Fallback for modularized imports:\nexport default endOfMonth;\n", "import { constructFrom } from \"./constructFrom.js\";\nimport { toDate } from \"./toDate.js\";\n\n/**\n * The {@link min} function options.\n */\n\n/**\n * @name min\n * @category Common Helpers\n * @summary Returns the earliest of the given dates.\n *\n * @description\n * Returns the earliest of the given dates.\n *\n * @typeParam DateType - The `Date` type, the function operates on. Gets inferred from passed arguments. Allows to use extensions like [`UTCDate`](https://github.com/date-fns/utc).\n * @typeParam ResultDate - The result `Date` type, it is the type returned from the context function if it is passed, or inferred from the arguments.\n *\n * @param dates - The dates to compare\n *\n * @returns The earliest of the dates\n *\n * @example\n * // Which of these dates is the earliest?\n * const result = min([\n *   new Date(1989, 6, 10),\n *   new Date(1987, 1, 11),\n *   new Date(1995, 6, 2),\n *   new Date(1990, 0, 1)\n * ])\n * //=> Wed Feb 11 1987 00:00:00\n */\nexport function min(dates, options) {\n  let result;\n  let context = options?.in;\n\n  dates.forEach((date) => {\n    // Use the first date object as the context function\n    if (!context && typeof date === \"object\")\n      context = constructFrom.bind(null, date);\n\n    const date_ = toDate(date, context);\n    if (!result || result > date_ || isNaN(+date_)) result = date_;\n  });\n\n  return constructFrom(context, result || NaN);\n}\n\n// Fallback for modularized imports:\nexport default min;\n", "import { toDate } from \"./toDate.js\";\n\n/**\n * The {@link endOfDay} function options.\n */\n\n/**\n * @name endOfDay\n * @category Day Helpers\n * @summary Return the end of a day for the given date.\n *\n * @description\n * Return the end of a day for the given date.\n * The result will be in the local timezone.\n *\n * @typeParam DateType - The `Date` type, the function operates on. Gets inferred from passed arguments. Allows to use extensions like [`UTCDate`](https://github.com/date-fns/utc).\n * @typeParam ResultDate - The result `Date` type, it is the type returned from the context function if it is passed, or inferred from the arguments.\n *\n * @param date - The original date\n * @param options - An object with options\n *\n * @returns The end of a day\n *\n * @example\n * // The end of a day for 2 September 2014 11:55:00:\n * const result = endOfDay(new Date(2014, 8, 2, 11, 55, 0))\n * //=> Tue Sep 02 2014 23:59:59.999\n */\nexport function endOfDay(date, options) {\n  const _date = toDate(date, options?.in);\n  _date.setHours(23, 59, 59, 999);\n  return _date;\n}\n\n// Fallback for modularized imports:\nexport default endOfDay;\n", "import { toDate } from \"./toDate.js\";\n\n/**\n * The {@link setSeconds} function options.\n */\n\n/**\n * @name setSeconds\n * @category Second Helpers\n * @summary Set the seconds to the given date, with context support.\n *\n * @description\n * Set the seconds to the given date, with an optional context for time zone specification.\n *\n * @typeParam DateType - The `Date` type, the function operates on. Gets inferred from passed arguments. Allows to use extensions like [`UTCDate`](https://github.com/date-fns/utc).\n * @typeParam ResultDate - The result `Date` type, it is the type returned from the context function if it is passed, or inferred from the arguments.\n *\n * @param date - The date to be changed\n * @param seconds - The seconds of the new date\n * @param options - An object with options\n *\n * @returns The new date with the seconds set\n *\n * @example\n * // Set 45 seconds to 1 September 2014 11:30:40:\n * const result = setSeconds(new Date(2014, 8, 1, 11, 30, 40), 45)\n * //=> Mon Sep 01 2014 11:30:45\n */\nexport function setSeconds(date, seconds, options) {\n  const _date = toDate(date, options?.in);\n  _date.setSeconds(seconds);\n  return _date;\n}\n\n// Fallback for modularized imports:\nexport default setSeconds;\n", "import { toDate } from \"./toDate.js\";\n\n/**\n * The {@link endOfYear} function options.\n */\n\n/**\n * @name endOfYear\n * @category Year Helpers\n * @summary Return the end of a year for the given date.\n *\n * @description\n * Return the end of a year for the given date.\n * The result will be in the local timezone.\n *\n * @typeParam DateType - The `Date` type, the function operates on. Gets inferred from passed arguments. Allows to use extensions like [`UTCDate`](https://github.com/date-fns/utc).\n * @typeParam ResultDate - The result `Date` type, it is the type returned from the context function if it is passed, or inferred from the arguments.\n *\n * @param date - The original date\n * @param options - The options\n *\n * @returns The end of a year\n *\n * @example\n * // The end of a year for 2 September 2014 11:55:00:\n * const result = endOfYear(new Date(2014, 8, 2, 11, 55, 0))\n * //=> Wed Dec 31 2014 23:59:59.999\n */\nexport function endOfYear(date, options) {\n  const _date = toDate(date, options?.in);\n  const year = _date.getFullYear();\n  _date.setFullYear(year + 1, 0, 0);\n  _date.setHours(23, 59, 59, 999);\n  return _date;\n}\n\n// Fallback for modularized imports:\nexport default endOfYear;\n", "import { constructFrom } from \"./constructFrom.js\";\nimport { toDate } from \"./toDate.js\";\n\n/**\n * The {@link addDays} function options.\n */\n\n/**\n * @name addDays\n * @category Day Helpers\n * @summary Add the specified number of days to the given date.\n *\n * @description\n * Add the specified number of days to the given date.\n *\n * @typeParam DateType - The `Date` type, the function operates on. Gets inferred from passed arguments. Allows to use extensions like [`UTCDate`](https://github.com/date-fns/utc).\n * @typeParam ResultDate - The result `Date` type, it is the type returned from the context function if it is passed, or inferred from the arguments.\n *\n * @param date - The date to be changed\n * @param amount - The amount of days to be added.\n * @param options - An object with options\n *\n * @returns The new date with the days added\n *\n * @example\n * // Add 10 days to 1 September 2014:\n * const result = addDays(new Date(2014, 8, 1), 10)\n * //=> Thu Sep 11 2014 00:00:00\n */\nexport function addDays(date, amount, options) {\n  const _date = toDate(date, options?.in);\n  if (isNaN(amount)) return constructFrom(options?.in || date, NaN);\n\n  // If 0 days, no-op to avoid changing times in the hour before end of DST\n  if (!amount) return _date;\n\n  _date.setDate(_date.getDate() + amount);\n  return _date;\n}\n\n// Fallback for modularized imports:\nexport default addDays;\n", "import { constructFrom } from \"./constructFrom.js\";\n\n/**\n * @name toDate\n * @category Common Helpers\n * @summary Convert the given argument to an instance of Date.\n *\n * @description\n * Convert the given argument to an instance of Date.\n *\n * If the argument is an instance of Date, the function returns its clone.\n *\n * If the argument is a number, it is treated as a timestamp.\n *\n * If the argument is none of the above, the function returns Invalid Date.\n *\n * Starting from v3.7.0, it clones a date using `[Symbol.for(\"constructDateFrom\")]`\n * enabling to transfer extra properties from the reference date to the new date.\n * It's useful for extensions like [`TZDate`](https://github.com/date-fns/tz)\n * that accept a time zone as a constructor argument.\n *\n * **Note**: *all* Date arguments passed to any *date-fns* function is processed by `toDate`.\n *\n * @typeParam DateType - The `Date` type, the function operates on. Gets inferred from passed arguments. Allows to use extensions like [`UTCDate`](https://github.com/date-fns/utc).\n * @typeParam ResultDate - The result `Date` type, it is the type returned from the context function if it is passed, or inferred from the arguments.\n *\n * @param argument - The value to convert\n *\n * @returns The parsed date in the local time zone\n *\n * @example\n * // Clone the date:\n * const result = toDate(new Date(2014, 1, 11, 11, 30, 30))\n * //=> Tue Feb 11 2014 11:30:30\n *\n * @example\n * // Convert the timestamp to date:\n * const result = toDate(1392098430000)\n * //=> Tue Feb 11 2014 11:30:30\n */\nexport function toDate(argument, context) {\n  // [TODO] Get rid of `toDate` or `constructFrom`?\n  return constructFrom(context || argument, argument);\n}\n\n// Fallback for modularized imports:\nexport default toDate;\n", "import { constructFrom } from \"./constructFrom.js\";\nimport { toDate } from \"./toDate.js\";\n\n/**\n * The {@link addMilliseconds} function options.\n */\n\n/**\n * @name addMilliseconds\n * @category Millisecond Helpers\n * @summary Add the specified number of milliseconds to the given date.\n *\n * @description\n * Add the specified number of milliseconds to the given date.\n *\n * @typeParam DateType - The `Date` type, the function operates on. Gets inferred from passed arguments. Allows to use extensions like [`UTCDate`](https://github.com/date-fns/utc).\n * @typeParam ResultDate - The result `Date` type, it is the type returned from the context function if it is passed, or inferred from the arguments.\n *\n * @param date - The date to be changed\n * @param amount - The amount of milliseconds to be added.\n * @param options - The options object\n *\n * @returns The new date with the milliseconds added\n *\n * @example\n * // Add 750 milliseconds to 10 July 2014 12:45:30.000:\n * const result = addMilliseconds(new Date(2014, 6, 10, 12, 45, 30, 0), 750)\n * //=> Thu Jul 10 2014 12:45:30.750\n */\nexport function addMilliseconds(date, amount, options) {\n  return constructFrom(options?.in || date, +toDate(date) + amount);\n}\n\n// Fallback for modularized imports:\nexport default addMilliseconds;\n", "import { constructFrom } from \"./constructFrom.js\";\nimport { toDate } from \"./toDate.js\";\n\n/**\n * The {@link getDaysInMonth} function options.\n */\n\n/**\n * @name getDaysInMonth\n * @category Month Helpers\n * @summary Get the number of days in a month of the given date.\n *\n * @description\n * Get the number of days in a month of the given date, considering the context if provided.\n *\n * @param date - The given date\n * @param options - An object with options\n *\n * @returns The number of days in a month\n *\n * @example\n * // How many days are in February 2000?\n * const result = getDaysInMonth(new Date(2000, 1))\n * //=> 29\n */\nexport function getDaysInMonth(date, options) {\n  const _date = toDate(date, options?.in);\n  const year = _date.getFullYear();\n  const monthIndex = _date.getMonth();\n  const lastDayOfMonth = constructFrom(_date, 0);\n  lastDayOfMonth.setFullYear(year, monthIndex + 1, 0);\n  lastDayOfMonth.setHours(0, 0, 0, 0);\n  return lastDayOfMonth.getDate();\n}\n\n// Fallback for modularized imports:\nexport default getDaysInMonth;\n", "import { constructFrom } from \"./constructFrom.js\";\nimport { getDaysInMonth } from \"./getDaysInMonth.js\";\nimport { toDate } from \"./toDate.js\";\n\n/**\n * The {@link setMonth} function options.\n */\n\n/**\n * @name setMonth\n * @category Month Helpers\n * @summary Set the month to the given date.\n *\n * @description\n * Set the month to the given date.\n *\n * @typeParam DateType - The `Date` type, the function operates on. Gets inferred from passed arguments. Allows to use extensions like [`UTCDate`](https://github.com/date-fns/utc).\n * @typeParam ResultDate - The result `Date` type, it is the type returned from the context function if it is passed, or inferred from the arguments.\n *\n * @param date - The date to be changed\n * @param month - The month index to set (0-11)\n * @param options - The options\n *\n * @returns The new date with the month set\n *\n * @example\n * // Set February to 1 September 2014:\n * const result = setMonth(new Date(2014, 8, 1), 1)\n * //=> Sat Feb 01 2014 00:00:00\n */\nexport function setMonth(date, month, options) {\n  const _date = toDate(date, options?.in);\n  const year = _date.getFullYear();\n  const day = _date.getDate();\n\n  const midMonth = constructFrom(options?.in || date, 0);\n  midMonth.setFullYear(year, month, 15);\n  midMonth.setHours(0, 0, 0, 0);\n  const daysInMonth = getDaysInMonth(midMonth);\n\n  // Set the earlier date, allows to wrap Jan 31 to Feb 28\n  _date.setMonth(month, Math.min(day, daysInMonth));\n  return _date;\n}\n\n// Fallback for modularized imports:\nexport default setMonth;\n", "import { constructFrom } from \"./constructFrom.js\";\nimport { getISOWeekYear } from \"./getISOWeekYear.js\";\nimport { startOfISOWeek } from \"./startOfISOWeek.js\";\n\n/**\n * The {@link startOfISOWeekYear} function options.\n */\n\n/**\n * @name startOfISOWeekYear\n * @category ISO Week-Numbering Year Helpers\n * @summary Return the start of an ISO week-numbering year for the given date.\n *\n * @description\n * Return the start of an ISO week-numbering year,\n * which always starts 3 days before the year's first Thursday.\n * The result will be in the local timezone.\n *\n * ISO week-numbering year: http://en.wikipedia.org/wiki/ISO_week_date\n *\n * @typeParam DateType - The `Date` type, the function operates on. Gets inferred from passed arguments. Allows to use extensions like [`UTCDate`](https://github.com/date-fns/utc).\n * @typeParam ResultDate - The result `Date` type, it is the type returned from the context function if it is passed, or inferred from the arguments.\n *\n * @param date - The original date\n * @param options - An object with options\n *\n * @returns The start of an ISO week-numbering year\n *\n * @example\n * // The start of an ISO week-numbering year for 2 July 2005:\n * const result = startOfISOWeekYear(new Date(2005, 6, 2))\n * //=> Mon Jan 03 2005 00:00:00\n */\nexport function startOfISOWeekYear(date, options) {\n  const year = getISOWeekYear(date, options);\n  const fourthOfJanuary = constructFrom(options?.in || date, 0);\n  fourthOfJanuary.setFullYear(year, 0, 4);\n  fourthOfJanuary.setHours(0, 0, 0, 0);\n  return startOfISOWeek(fourthOfJanuary);\n}\n\n// Fallback for modularized imports:\nexport default startOfISOWeekYear;\n", "import { millisecondsInWeek } from \"./constants.js\";\nimport { startOfISOWeek } from \"./startOfISOWeek.js\";\nimport { startOfISOWeekYear } from \"./startOfISOWeekYear.js\";\nimport { toDate } from \"./toDate.js\";\n\n/**\n * The {@link getISOWeek} function options.\n */\n\n/**\n * @name getISOWeek\n * @category ISO Week Helpers\n * @summary Get the ISO week of the given date.\n *\n * @description\n * Get the ISO week of the given date.\n *\n * ISO week-numbering year: http://en.wikipedia.org/wiki/ISO_week_date\n *\n * @param date - The given date\n * @param options - The options\n *\n * @returns The ISO week\n *\n * @example\n * // Which week of the ISO-week numbering year is 2 January 2005?\n * const result = getISOWeek(new Date(2005, 0, 2))\n * //=> 53\n */\nexport function getISOWeek(date, options) {\n  const _date = toDate(date, options?.in);\n  const diff = +startOfISOWeek(_date) - +startOfISOWeekYear(_date);\n\n  // Round the number of weeks to the nearest integer because the number of\n  // milliseconds in a week is not constant (e.g. it's different in the week of\n  // the daylight saving time clock shift).\n  return Math.round(diff / millisecondsInWeek) + 1;\n}\n\n// Fallback for modularized imports:\nexport default getISOWeek;\n", "import { normalizeDates } from \"./_lib/normalizeDates.js\";\nimport { differenceInCalendarDays } from \"./differenceInCalendarDays.js\";\n\n/**\n * The {@link differenceInDays} function options.\n */\n\n/**\n * @name differenceInDays\n * @category Day Helpers\n * @summary Get the number of full days between the given dates.\n *\n * @description\n * Get the number of full day periods between two dates. Fractional days are\n * truncated towards zero.\n *\n * One \"full day\" is the distance between a local time in one day to the same\n * local time on the next or previous day. A full day can sometimes be less than\n * or more than 24 hours if a daylight savings change happens between two dates.\n *\n * To ignore DST and only measure exact 24-hour periods, use this instead:\n * `Math.trunc(differenceInHours(dateLeft, dateRight)/24)|0`.\n *\n * @param laterDate - The later date\n * @param earlierDate - The earlier date\n * @param options - An object with options\n *\n * @returns The number of full days according to the local timezone\n *\n * @example\n * // How many full days are between\n * // 2 July 2011 23:00:00 and 2 July 2012 00:00:00?\n * const result = differenceInDays(\n *   new Date(2012, 6, 2, 0, 0),\n *   new Date(2011, 6, 2, 23, 0)\n * )\n * //=> 365\n *\n * @example\n * // How many full days are between\n * // 2 July 2011 23:59:00 and 3 July 2011 00:01:00?\n * const result = differenceInDays(\n *   new Date(2011, 6, 3, 0, 1),\n *   new Date(2011, 6, 2, 23, 59)\n * )\n * //=> 0\n *\n * @example\n * // How many full days are between\n * // 1 March 2020 0:00 and 1 June 2020 0:00 ?\n * // Note: because local time is used, the\n * // result will always be 92 days, even in\n * // time zones where DST starts and the\n * // period has only 92*24-1 hours.\n * const result = differenceInDays(\n *   new Date(2020, 5, 1),\n *   new Date(2020, 2, 1)\n * )\n * //=> 92\n */\nexport function differenceInDays(laterDate, earlierDate, options) {\n  const [laterDate_, earlierDate_] = normalizeDates(\n    options?.in,\n    laterDate,\n    earlierDate,\n  );\n\n  const sign = compareLocalAsc(laterDate_, earlierDate_);\n  const difference = Math.abs(\n    differenceInCalendarDays(laterDate_, earlierDate_),\n  );\n\n  laterDate_.setDate(laterDate_.getDate() - sign * difference);\n\n  // Math.abs(diff in full days - diff in calendar days) === 1 if last calendar day is not full\n  // If so, result must be decreased by 1 in absolute value\n  const isLastDayNotFull = Number(\n    compareLocalAsc(laterDate_, earlierDate_) === -sign,\n  );\n\n  const result = sign * (difference - isLastDayNotFull);\n  // Prevent negative zero\n  return result === 0 ? 0 : result;\n}\n\n// Like `compareAsc` but uses local time not UTC, which is needed\n// for accurate equality comparisons of UTC timestamps that end up\n// having the same representation in local time, e.g. one hour before\n// DST ends vs. the instant that DST ends.\nfunction compareLocalAsc(laterDate, earlierDate) {\n  const diff =\n    laterDate.getFullYear() - earlierDate.getFullYear() ||\n    laterDate.getMonth() - earlierDate.getMonth() ||\n    laterDate.getDate() - earlierDate.getDate() ||\n    laterDate.getHours() - earlierDate.getHours() ||\n    laterDate.getMinutes() - earlierDate.getMinutes() ||\n    laterDate.getSeconds() - earlierDate.getSeconds() ||\n    laterDate.getMilliseconds() - earlierDate.getMilliseconds();\n\n  if (diff < 0) return -1;\n  if (diff > 0) return 1;\n\n  // Return 0 if diff is 0; return NaN if diff is NaN\n  return diff;\n}\n\n// Fallback for modularized imports:\nexport default differenceInDays;\n", "import { addMonths } from \"./addMonths.js\";\n\n/**\n * The subMonths function options.\n */\n\n/**\n * @name subMonths\n * @category Month Helpers\n * @summary Subtract the specified number of months from the given date.\n *\n * @description\n * Subtract the specified number of months from the given date.\n *\n * @typeParam DateType - The `Date` type, the function operates on. Gets inferred from passed arguments. Allows to use extensions like [`UTCDate`](https://github.com/date-fns/utc).\n * @typeParam ResultDate - The result `Date` type, it is the type returned from the context function if it is passed, or inferred from the arguments.\n *\n * @param date - The date to be changed\n * @param amount - The amount of months to be subtracted.\n * @param options - An object with options\n *\n * @returns The new date with the months subtracted\n *\n * @example\n * // Subtract 5 months from 1 February 2015:\n * const result = subMonths(new Date(2015, 1, 1), 5)\n * //=> Mon Sep 01 2014 00:00:00\n */\nexport function subMonths(date, amount, options) {\n  return addMonths(date, -amount, options);\n}\n\n// Fallback for modularized imports:\nexport default subMonths;\n", "import { getDefaultOptions as getInternalDefaultOptions } from \"./_lib/defaultOptions.js\";\n\n/**\n * @name getDefaultOptions\n * @category Common Helpers\n * @summary Get default options.\n * @pure false\n *\n * @description\n * Returns an object that contains defaults for\n * `options.locale`, `options.weekStartsOn` and `options.firstWeekContainsDate`\n * arguments for all functions.\n *\n * You can change these with [setDefaultOptions](https://date-fns.org/docs/setDefaultOptions).\n *\n * @returns The default options\n *\n * @example\n * const result = getDefaultOptions()\n * //=> {}\n *\n * @example\n * setDefaultOptions({ weekStarsOn: 1, firstWeekContainsDate: 4 })\n * const result = getDefaultOptions()\n * //=> { weekStarsOn: 1, firstWeekContainsDate: 4 }\n */\nexport function getDefaultOptions() {\n  return Object.assign({}, getInternalDefaultOptions());\n}\n\n// Fallback for modularized imports:\nexport default getDefaultOptions;\n", "import { constructFrom } from \"./constructFrom.js\";\n\n/**\n * @name transpose\n * @category Generic Helpers\n * @summary Transpose the date to the given constructor.\n *\n * @description\n * The function transposes the date to the given constructor. It helps you\n * to transpose the date in the system time zone to say `UTCDate` or any other\n * date extension.\n *\n * @typeParam InputDate - The input `Date` type derived from the passed argument.\n * @typeParam ResultDate - The result `Date` type derived from the passed constructor.\n *\n * @param date - The date to use values from\n * @param constructor - The date constructor to use\n *\n * @returns Date transposed to the given constructor\n *\n * @example\n * // Create July 10, 2022 00:00 in locale time zone\n * const date = new Date(2022, 6, 10)\n * //=> 'Sun Jul 10 2022 00:00:00 GMT+0800 (Singapore Standard Time)'\n *\n * @example\n * // Transpose the date to July 10, 2022 00:00 in UTC\n * transpose(date, UTCDate)\n * //=> 'Sun Jul 10 2022 00:00:00 GMT+0000 (Coordinated Universal Time)'\n */\nexport function transpose(date, constructor) {\n  const date_ = isConstructor(constructor)\n    ? new constructor(0)\n    : constructFrom(constructor, 0);\n  date_.setFullYear(date.getFullYear(), date.getMonth(), date.getDate());\n  date_.setHours(\n    date.getHours(),\n    date.getMinutes(),\n    date.getSeconds(),\n    date.getMilliseconds(),\n  );\n  return date_;\n}\n\nfunction isConstructor(constructor) {\n  return (\n    typeof constructor === \"function\" &&\n    constructor.prototype?.constructor === constructor\n  );\n}\n\n// Fallback for modularized imports:\nexport default transpose;\n", "import { constructFrom } from \"../../constructFrom.js\";\nimport { transpose } from \"../../transpose.js\";\n\nconst TIMEZONE_UNIT_PRIORITY = 10;\n\nexport class Setter {\n  subPriority = 0;\n\n  validate(_utcDate, _options) {\n    return true;\n  }\n}\n\nexport class ValueSetter extends Setter {\n  constructor(\n    value,\n\n    validateValue,\n\n    setValue,\n\n    priority,\n    subPriority,\n  ) {\n    super();\n    this.value = value;\n    this.validateValue = validateValue;\n    this.setValue = setValue;\n    this.priority = priority;\n    if (subPriority) {\n      this.subPriority = subPriority;\n    }\n  }\n\n  validate(date, options) {\n    return this.validateValue(date, this.value, options);\n  }\n\n  set(date, flags, options) {\n    return this.setValue(date, flags, this.value, options);\n  }\n}\n\nexport class DateTimezoneSetter extends Setter {\n  priority = TIMEZONE_UNIT_PRIORITY;\n  subPriority = -1;\n\n  constructor(context, reference) {\n    super();\n    this.context = context || ((date) => constructFrom(reference, date));\n  }\n\n  set(date, flags) {\n    if (flags.timestampIsSet) return date;\n    return constructFrom(date, transpose(date, this.context));\n  }\n}\n", "import { ValueSetter } from \"./Setter.js\";\n\nexport class Parser {\n  run(dateString, token, match, options) {\n    const result = this.parse(dateString, token, match, options);\n    if (!result) {\n      return null;\n    }\n\n    return {\n      setter: new ValueSetter(\n        result.value,\n        this.validate,\n        this.set,\n        this.priority,\n        this.subPriority,\n      ),\n      rest: result.rest,\n    };\n  }\n\n  validate(_utcDate, _value, _options) {\n    return true;\n  }\n}\n", "import { Parser } from \"../Parser.js\";\n\nexport class <PERSON><PERSON>ars<PERSON> extends Parser {\n  priority = 140;\n\n  parse(dateString, token, match) {\n    switch (token) {\n      // AD, BC\n      case \"G\":\n      case \"GG\":\n      case \"GGG\":\n        return (\n          match.era(dateString, { width: \"abbreviated\" }) ||\n          match.era(dateString, { width: \"narrow\" })\n        );\n\n      // A, B\n      case \"GGGGG\":\n        return match.era(dateString, { width: \"narrow\" });\n      // <PERSON><PERSON>, Before Christ\n      case \"GGGG\":\n      default:\n        return (\n          match.era(dateString, { width: \"wide\" }) ||\n          match.era(dateString, { width: \"abbreviated\" }) ||\n          match.era(dateString, { width: \"narrow\" })\n        );\n    }\n  }\n\n  set(date, flags, value) {\n    flags.era = value;\n    date.setFullYear(value, 0, 1);\n    date.setHours(0, 0, 0, 0);\n    return date;\n  }\n\n  incompatibleTokens = [\"R\", \"u\", \"t\", \"T\"];\n}\n", "export const numericPatterns = {\n  month: /^(1[0-2]|0?\\d)/, // 0 to 12\n  date: /^(3[0-1]|[0-2]?\\d)/, // 0 to 31\n  dayOfYear: /^(36[0-6]|3[0-5]\\d|[0-2]?\\d?\\d)/, // 0 to 366\n  week: /^(5[0-3]|[0-4]?\\d)/, // 0 to 53\n  hour23h: /^(2[0-3]|[0-1]?\\d)/, // 0 to 23\n  hour24h: /^(2[0-4]|[0-1]?\\d)/, // 0 to 24\n  hour11h: /^(1[0-1]|0?\\d)/, // 0 to 11\n  hour12h: /^(1[0-2]|0?\\d)/, // 0 to 12\n  minute: /^[0-5]?\\d/, // 0 to 59\n  second: /^[0-5]?\\d/, // 0 to 59\n\n  singleDigit: /^\\d/, // 0 to 9\n  twoDigits: /^\\d{1,2}/, // 0 to 99\n  threeDigits: /^\\d{1,3}/, // 0 to 999\n  fourDigits: /^\\d{1,4}/, // 0 to 9999\n\n  anyDigitsSigned: /^-?\\d+/,\n  singleDigitSigned: /^-?\\d/, // 0 to 9, -0 to -9\n  twoDigitsSigned: /^-?\\d{1,2}/, // 0 to 99, -0 to -99\n  threeDigitsSigned: /^-?\\d{1,3}/, // 0 to 999, -0 to -999\n  fourDigitsSigned: /^-?\\d{1,4}/, // 0 to 9999, -0 to -9999\n};\n\nexport const timezonePatterns = {\n  basicOptionalMinutes: /^([+-])(\\d{2})(\\d{2})?|Z/,\n  basic: /^([+-])(\\d{2})(\\d{2})|Z/,\n  basicOptionalSeconds: /^([+-])(\\d{2})(\\d{2})((\\d{2}))?|Z/,\n  extended: /^([+-])(\\d{2}):(\\d{2})|Z/,\n  extendedOptionalSeconds: /^([+-])(\\d{2}):(\\d{2})(:(\\d{2}))?|Z/,\n};\n", "import {\n  millisecondsInHour,\n  millisecondsInMinute,\n  millisecondsInSecond,\n} from \"../../constants.js\";\n\nimport { numericPatterns } from \"./constants.js\";\n\nexport function mapValue(parseFnResult, mapFn) {\n  if (!parseFnResult) {\n    return parseFnResult;\n  }\n\n  return {\n    value: mapFn(parseFnResult.value),\n    rest: parseFnResult.rest,\n  };\n}\n\nexport function parseNumericPattern(pattern, dateString) {\n  const matchResult = dateString.match(pattern);\n\n  if (!matchResult) {\n    return null;\n  }\n\n  return {\n    value: parseInt(matchResult[0], 10),\n    rest: dateString.slice(matchResult[0].length),\n  };\n}\n\nexport function parseTimezonePattern(pattern, dateString) {\n  const matchResult = dateString.match(pattern);\n\n  if (!matchResult) {\n    return null;\n  }\n\n  // Input is 'Z'\n  if (matchResult[0] === \"Z\") {\n    return {\n      value: 0,\n      rest: dateString.slice(1),\n    };\n  }\n\n  const sign = matchResult[1] === \"+\" ? 1 : -1;\n  const hours = matchResult[2] ? parseInt(matchResult[2], 10) : 0;\n  const minutes = matchResult[3] ? parseInt(matchResult[3], 10) : 0;\n  const seconds = matchResult[5] ? parseInt(matchResult[5], 10) : 0;\n\n  return {\n    value:\n      sign *\n      (hours * millisecondsInHour +\n        minutes * millisecondsInMinute +\n        seconds * millisecondsInSecond),\n    rest: dateString.slice(matchResult[0].length),\n  };\n}\n\nexport function parseAnyDigitsSigned(dateString) {\n  return parseNumericPattern(numericPatterns.anyDigitsSigned, dateString);\n}\n\nexport function parseNDigits(n, dateString) {\n  switch (n) {\n    case 1:\n      return parseNumericPattern(numericPatterns.singleDigit, dateString);\n    case 2:\n      return parseNumericPattern(numericPatterns.twoDigits, dateString);\n    case 3:\n      return parseNumericPattern(numericPatterns.threeDigits, dateString);\n    case 4:\n      return parseNumericPattern(numericPatterns.fourDigits, dateString);\n    default:\n      return parseNumericPattern(new RegExp(\"^\\\\d{1,\" + n + \"}\"), dateString);\n  }\n}\n\nexport function parseNDigitsSigned(n, dateString) {\n  switch (n) {\n    case 1:\n      return parseNumericPattern(numericPatterns.singleDigitSigned, dateString);\n    case 2:\n      return parseNumericPattern(numericPatterns.twoDigitsSigned, dateString);\n    case 3:\n      return parseNumericPattern(numericPatterns.threeDigitsSigned, dateString);\n    case 4:\n      return parseNumericPattern(numericPatterns.fourDigitsSigned, dateString);\n    default:\n      return parseNumericPattern(new RegExp(\"^-?\\\\d{1,\" + n + \"}\"), dateString);\n  }\n}\n\nexport function dayPeriodEnumToHours(dayPeriod) {\n  switch (dayPeriod) {\n    case \"morning\":\n      return 4;\n    case \"evening\":\n      return 17;\n    case \"pm\":\n    case \"noon\":\n    case \"afternoon\":\n      return 12;\n    case \"am\":\n    case \"midnight\":\n    case \"night\":\n    default:\n      return 0;\n  }\n}\n\nexport function normalizeTwoDigitYear(twoDigitYear, currentYear) {\n  const isCommonEra = currentYear > 0;\n  // Absolute number of the current year:\n  // 1 -> 1 AC\n  // 0 -> 1 BC\n  // -1 -> 2 BC\n  const absCurrentYear = isCommonEra ? currentYear : 1 - currentYear;\n\n  let result;\n  if (absCurrentYear <= 50) {\n    result = twoDigitYear || 100;\n  } else {\n    const rangeEnd = absCurrentYear + 50;\n    const rangeEndCentury = Math.trunc(rangeEnd / 100) * 100;\n    const isPreviousCentury = twoDigitYear >= rangeEnd % 100;\n    result = twoDigitYear + rangeEndCentury - (isPreviousCentury ? 100 : 0);\n  }\n\n  return isCommonEra ? result : 1 - result;\n}\n\nexport function isLeapYearIndex(year) {\n  return year % 400 === 0 || (year % 4 === 0 && year % 100 !== 0);\n}\n", "import { Parser } from \"../Parser.js\";\n\nimport { mapValue, normalizeTwoDigitYear, parseNDigits } from \"../utils.js\";\n\n// From http://www.unicode.org/reports/tr35/tr35-31/tr35-dates.html#Date_Format_Patterns\n// | Year     |     y | yy |   yyy |  yyyy | yyyyy |\n// |----------|-------|----|-------|-------|-------|\n// | AD 1     |     1 | 01 |   001 |  0001 | 00001 |\n// | AD 12    |    12 | 12 |   012 |  0012 | 00012 |\n// | AD 123   |   123 | 23 |   123 |  0123 | 00123 |\n// | AD 1234  |  1234 | 34 |  1234 |  1234 | 01234 |\n// | AD 12345 | 12345 | 45 | 12345 | 12345 | 12345 |\nexport class YearParser extends Parser {\n  priority = 130;\n  incompatibleTokens = [\"Y\", \"R\", \"u\", \"w\", \"I\", \"i\", \"e\", \"c\", \"t\", \"T\"];\n\n  parse(dateString, token, match) {\n    const valueCallback = (year) => ({\n      year,\n      isTwoDigitYear: token === \"yy\",\n    });\n\n    switch (token) {\n      case \"y\":\n        return mapValue(parseNDigits(4, dateString), valueCallback);\n      case \"yo\":\n        return mapValue(\n          match.ordinalNumber(dateString, {\n            unit: \"year\",\n          }),\n          valueCallback,\n        );\n      default:\n        return mapValue(parseNDigits(token.length, dateString), valueCallback);\n    }\n  }\n\n  validate(_date, value) {\n    return value.isTwoDigitYear || value.year > 0;\n  }\n\n  set(date, flags, value) {\n    const currentYear = date.getFullYear();\n\n    if (value.isTwoDigitYear) {\n      const normalizedTwoDigitYear = normalizeTwoDigitYear(\n        value.year,\n        currentYear,\n      );\n      date.setFullYear(normalizedTwoDigitYear, 0, 1);\n      date.setHours(0, 0, 0, 0);\n      return date;\n    }\n\n    const year =\n      !(\"era\" in flags) || flags.era === 1 ? value.year : 1 - value.year;\n    date.setFullYear(year, 0, 1);\n    date.setHours(0, 0, 0, 0);\n    return date;\n  }\n}\n", "import { getWeekYear } from \"../../../getWeekYear.js\";\n\nimport { startOfWeek } from \"../../../startOfWeek.js\";\nimport { Parser } from \"../Parser.js\";\n\nimport { mapValue, normalizeTwoDigitYear, parseNDigits } from \"../utils.js\";\n\n// Local week-numbering year\nexport class LocalWeekYearParser extends Parser {\n  priority = 130;\n\n  parse(dateString, token, match) {\n    const valueCallback = (year) => ({\n      year,\n      isTwoDigitYear: token === \"YY\",\n    });\n\n    switch (token) {\n      case \"Y\":\n        return mapValue(parseNDigits(4, dateString), valueCallback);\n      case \"Yo\":\n        return mapValue(\n          match.ordinalNumber(dateString, {\n            unit: \"year\",\n          }),\n          valueCallback,\n        );\n      default:\n        return mapValue(parseNDigits(token.length, dateString), valueCallback);\n    }\n  }\n\n  validate(_date, value) {\n    return value.isTwoDigitYear || value.year > 0;\n  }\n\n  set(date, flags, value, options) {\n    const currentYear = getWeekYear(date, options);\n\n    if (value.isTwoDigitYear) {\n      const normalizedTwoDigitYear = normalizeTwoDigitYear(\n        value.year,\n        currentYear,\n      );\n      date.setFullYear(\n        normalizedTwoDigitYear,\n        0,\n        options.firstWeekContainsDate,\n      );\n      date.setHours(0, 0, 0, 0);\n      return startOfWeek(date, options);\n    }\n\n    const year =\n      !(\"era\" in flags) || flags.era === 1 ? value.year : 1 - value.year;\n    date.setFullYear(year, 0, options.firstWeekContainsDate);\n    date.setHours(0, 0, 0, 0);\n    return startOfWeek(date, options);\n  }\n\n  incompatibleTokens = [\n    \"y\",\n    \"R\",\n    \"u\",\n    \"Q\",\n    \"q\",\n    \"M\",\n    \"L\",\n    \"I\",\n    \"d\",\n    \"D\",\n    \"i\",\n    \"t\",\n    \"T\",\n  ];\n}\n", "import { startOfISOWeek } from \"../../../startOfISOWeek.js\";\nimport { constructFrom } from \"../../../constructFrom.js\";\nimport { Parser } from \"../Parser.js\";\n\nimport { parseNDigitsSigned } from \"../utils.js\";\n\n// ISO week-numbering year\nexport class ISOWeekYearParser extends Parser {\n  priority = 130;\n\n  parse(dateString, token) {\n    if (token === \"R\") {\n      return parseNDigitsSigned(4, dateString);\n    }\n\n    return parseNDigitsSigned(token.length, dateString);\n  }\n\n  set(date, _flags, value) {\n    const firstWeekOfYear = constructFrom(date, 0);\n    firstWeekOfYear.setFullYear(value, 0, 4);\n    firstWeekOfYear.setHours(0, 0, 0, 0);\n    return startOfISOWeek(firstWeekOfYear);\n  }\n\n  incompatibleTokens = [\n    \"G\",\n    \"y\",\n    \"Y\",\n    \"u\",\n    \"Q\",\n    \"q\",\n    \"M\",\n    \"L\",\n    \"w\",\n    \"d\",\n    \"D\",\n    \"e\",\n    \"c\",\n    \"t\",\n    \"T\",\n  ];\n}\n", "import { Parser } from \"../Parser.js\";\n\nimport { parseNDigitsSigned } from \"../utils.js\";\n\nexport class ExtendedYearParser extends Parser {\n  priority = 130;\n\n  parse(dateString, token) {\n    if (token === \"u\") {\n      return parseNDigitsSigned(4, dateString);\n    }\n\n    return parseNDigitsSigned(token.length, dateString);\n  }\n\n  set(date, _flags, value) {\n    date.setFullYear(value, 0, 1);\n    date.setHours(0, 0, 0, 0);\n    return date;\n  }\n\n  incompatibleTokens = [\"G\", \"y\", \"Y\", \"R\", \"w\", \"I\", \"i\", \"e\", \"c\", \"t\", \"T\"];\n}\n", "import { Parser } from \"../Parser.js\";\n\nimport { parseNDigits } from \"../utils.js\";\n\nexport class QuarterParser extends Parser {\n  priority = 120;\n\n  parse(dateString, token, match) {\n    switch (token) {\n      // 1, 2, 3, 4\n      case \"Q\":\n      case \"QQ\": // 01, 02, 03, 04\n        return parseNDigits(token.length, dateString);\n      // 1st, 2nd, 3rd, 4th\n      case \"Qo\":\n        return match.ordinalNumber(dateString, { unit: \"quarter\" });\n      // Q1, Q2, Q3, Q4\n      case \"QQQ\":\n        return (\n          match.quarter(dateString, {\n            width: \"abbreviated\",\n            context: \"formatting\",\n          }) ||\n          match.quarter(dateString, {\n            width: \"narrow\",\n            context: \"formatting\",\n          })\n        );\n\n      // 1, 2, 3, 4 (narrow quarter; could be not numerical)\n      case \"QQQQQ\":\n        return match.quarter(dateString, {\n          width: \"narrow\",\n          context: \"formatting\",\n        });\n      // 1st quarter, 2nd quarter, ...\n      case \"QQQQ\":\n      default:\n        return (\n          match.quarter(dateString, {\n            width: \"wide\",\n            context: \"formatting\",\n          }) ||\n          match.quarter(dateString, {\n            width: \"abbreviated\",\n            context: \"formatting\",\n          }) ||\n          match.quarter(dateString, {\n            width: \"narrow\",\n            context: \"formatting\",\n          })\n        );\n    }\n  }\n\n  validate(_date, value) {\n    return value >= 1 && value <= 4;\n  }\n\n  set(date, _flags, value) {\n    date.setMonth((value - 1) * 3, 1);\n    date.setHours(0, 0, 0, 0);\n    return date;\n  }\n\n  incompatibleTokens = [\n    \"Y\",\n    \"R\",\n    \"q\",\n    \"M\",\n    \"L\",\n    \"w\",\n    \"I\",\n    \"d\",\n    \"D\",\n    \"i\",\n    \"e\",\n    \"c\",\n    \"t\",\n    \"T\",\n  ];\n}\n", "import { Parser } from \"../Parser.js\";\n\nimport { parseNDigits } from \"../utils.js\";\n\nexport class StandAloneQuarterParser extends Parser {\n  priority = 120;\n\n  parse(dateString, token, match) {\n    switch (token) {\n      // 1, 2, 3, 4\n      case \"q\":\n      case \"qq\": // 01, 02, 03, 04\n        return parseNDigits(token.length, dateString);\n      // 1st, 2nd, 3rd, 4th\n      case \"qo\":\n        return match.ordinalNumber(dateString, { unit: \"quarter\" });\n      // Q1, Q2, Q3, Q4\n      case \"qqq\":\n        return (\n          match.quarter(dateString, {\n            width: \"abbreviated\",\n            context: \"standalone\",\n          }) ||\n          match.quarter(dateString, {\n            width: \"narrow\",\n            context: \"standalone\",\n          })\n        );\n\n      // 1, 2, 3, 4 (narrow quarter; could be not numerical)\n      case \"qqqqq\":\n        return match.quarter(dateString, {\n          width: \"narrow\",\n          context: \"standalone\",\n        });\n      // 1st quarter, 2nd quarter, ...\n      case \"qqqq\":\n      default:\n        return (\n          match.quarter(dateString, {\n            width: \"wide\",\n            context: \"standalone\",\n          }) ||\n          match.quarter(dateString, {\n            width: \"abbreviated\",\n            context: \"standalone\",\n          }) ||\n          match.quarter(dateString, {\n            width: \"narrow\",\n            context: \"standalone\",\n          })\n        );\n    }\n  }\n\n  validate(_date, value) {\n    return value >= 1 && value <= 4;\n  }\n\n  set(date, _flags, value) {\n    date.setMonth((value - 1) * 3, 1);\n    date.setHours(0, 0, 0, 0);\n    return date;\n  }\n\n  incompatibleTokens = [\n    \"Y\",\n    \"R\",\n    \"Q\",\n    \"M\",\n    \"L\",\n    \"w\",\n    \"I\",\n    \"d\",\n    \"D\",\n    \"i\",\n    \"e\",\n    \"c\",\n    \"t\",\n    \"T\",\n  ];\n}\n", "import { numericPatterns } from \"../constants.js\";\nimport { Parser } from \"../Parser.js\";\n\nimport { mapValue, parseNDigits, parseNumericPattern } from \"../utils.js\";\n\nexport class MonthParser extends Parser {\n  incompatibleTokens = [\n    \"Y\",\n    \"R\",\n    \"q\",\n    \"Q\",\n    \"L\",\n    \"w\",\n    \"I\",\n    \"D\",\n    \"i\",\n    \"e\",\n    \"c\",\n    \"t\",\n    \"T\",\n  ];\n\n  priority = 110;\n\n  parse(dateString, token, match) {\n    const valueCallback = (value) => value - 1;\n\n    switch (token) {\n      // 1, 2, ..., 12\n      case \"M\":\n        return mapValue(\n          parseNumericPattern(numericPatterns.month, dateString),\n          valueCallback,\n        );\n      // 01, 02, ..., 12\n      case \"MM\":\n        return mapValue(parseNDigits(2, dateString), valueCallback);\n      // 1st, 2nd, ..., 12th\n      case \"Mo\":\n        return mapValue(\n          match.ordinalNumber(dateString, {\n            unit: \"month\",\n          }),\n          valueCallback,\n        );\n      // Jan, Feb, ..., Dec\n      case \"MMM\":\n        return (\n          match.month(dateString, {\n            width: \"abbreviated\",\n            context: \"formatting\",\n          }) ||\n          match.month(dateString, { width: \"narrow\", context: \"formatting\" })\n        );\n\n      // J, F, ..., D\n      case \"MMMMM\":\n        return match.month(dateString, {\n          width: \"narrow\",\n          context: \"formatting\",\n        });\n      // January, February, ..., December\n      case \"MMMM\":\n      default:\n        return (\n          match.month(dateString, { width: \"wide\", context: \"formatting\" }) ||\n          match.month(dateString, {\n            width: \"abbreviated\",\n            context: \"formatting\",\n          }) ||\n          match.month(dateString, { width: \"narrow\", context: \"formatting\" })\n        );\n    }\n  }\n\n  validate(_date, value) {\n    return value >= 0 && value <= 11;\n  }\n\n  set(date, _flags, value) {\n    date.setMonth(value, 1);\n    date.setHours(0, 0, 0, 0);\n    return date;\n  }\n}\n", "import { numericPatterns } from \"../constants.js\";\nimport { Parser } from \"../Parser.js\";\n\nimport { mapValue, parseNDigits, parseNumericPattern } from \"../utils.js\";\n\nexport class StandAloneMonthParser extends Parser {\n  priority = 110;\n\n  parse(dateString, token, match) {\n    const valueCallback = (value) => value - 1;\n\n    switch (token) {\n      // 1, 2, ..., 12\n      case \"L\":\n        return mapValue(\n          parseNumericPattern(numericPatterns.month, dateString),\n          valueCallback,\n        );\n      // 01, 02, ..., 12\n      case \"LL\":\n        return mapValue(parseNDigits(2, dateString), valueCallback);\n      // 1st, 2nd, ..., 12th\n      case \"Lo\":\n        return mapValue(\n          match.ordinalNumber(dateString, {\n            unit: \"month\",\n          }),\n          valueCallback,\n        );\n      // Jan, Feb, ..., Dec\n      case \"LLL\":\n        return (\n          match.month(dateString, {\n            width: \"abbreviated\",\n            context: \"standalone\",\n          }) ||\n          match.month(dateString, { width: \"narrow\", context: \"standalone\" })\n        );\n\n      // J, F, ..., D\n      case \"LLLLL\":\n        return match.month(dateString, {\n          width: \"narrow\",\n          context: \"standalone\",\n        });\n      // January, February, ..., December\n      case \"LLLL\":\n      default:\n        return (\n          match.month(dateString, { width: \"wide\", context: \"standalone\" }) ||\n          match.month(dateString, {\n            width: \"abbreviated\",\n            context: \"standalone\",\n          }) ||\n          match.month(dateString, { width: \"narrow\", context: \"standalone\" })\n        );\n    }\n  }\n\n  validate(_date, value) {\n    return value >= 0 && value <= 11;\n  }\n\n  set(date, _flags, value) {\n    date.setMonth(value, 1);\n    date.setHours(0, 0, 0, 0);\n    return date;\n  }\n\n  incompatibleTokens = [\n    \"Y\",\n    \"R\",\n    \"q\",\n    \"Q\",\n    \"M\",\n    \"w\",\n    \"I\",\n    \"D\",\n    \"i\",\n    \"e\",\n    \"c\",\n    \"t\",\n    \"T\",\n  ];\n}\n", "import { getWeek } from \"./getWeek.js\";\nimport { toDate } from \"./toDate.js\";\n\n/**\n * The {@link setWeek} function options.\n */\n\n/**\n * @name setWeek\n * @category Week Helpers\n * @summary Set the local week to the given date.\n *\n * @description\n * Set the local week to the given date, saving the weekday number.\n * The exact calculation depends on the values of\n * `options.weekStartsOn` (which is the index of the first day of the week)\n * and `options.firstWeekContainsDate` (which is the day of January, which is always in\n * the first week of the week-numbering year)\n *\n * Week numbering: https://en.wikipedia.org/wiki/Week#The_ISO_week_date_system\n *\n * @typeParam DateType - The `Date` type, the function operates on. Gets inferred from passed arguments. Allows to use extensions like [`UTCDate`](https://github.com/date-fns/utc).\n * @typeParam ResultDate - The result `Date` type, it is the type returned from the context function if it is passed, or inferred from the arguments.\n *\n * @param date - The date to be changed\n * @param week - The week of the new date\n * @param options - An object with options\n *\n * @returns The new date with the local week set\n *\n * @example\n * // Set the 1st week to 2 January 2005 with default options:\n * const result = setWeek(new Date(2005, 0, 2), 1)\n * //=> Sun Dec 26 2004 00:00:00\n *\n * @example\n * // Set the 1st week to 2 January 2005,\n * // if Monday is the first day of the week,\n * // and the first week of the year always contains 4 January:\n * const result = setWeek(new Date(2005, 0, 2), 1, {\n *   weekStartsOn: 1,\n *   firstWeekContainsDate: 4\n * })\n * //=> Sun Jan 4 2004 00:00:00\n */\nexport function setWeek(date, week, options) {\n  const date_ = toDate(date, options?.in);\n  const diff = getWeek(date_, options) - week;\n  date_.setDate(date_.getDate() - diff * 7);\n  return toDate(date_, options?.in);\n}\n\n// Fallback for modularized imports:\nexport default setWeek;\n", "import { setWeek } from \"../../../setWeek.js\";\nimport { startOfWeek } from \"../../../startOfWeek.js\";\nimport { numericPatterns } from \"../constants.js\";\nimport { Parser } from \"../Parser.js\";\n\nimport { parseNDigits, parseNumericPattern } from \"../utils.js\";\n\n// Local week of year\nexport class LocalWeekParser extends Parser {\n  priority = 100;\n\n  parse(dateString, token, match) {\n    switch (token) {\n      case \"w\":\n        return parseNumericPattern(numericPatterns.week, dateString);\n      case \"wo\":\n        return match.ordinalNumber(dateString, { unit: \"week\" });\n      default:\n        return parseNDigits(token.length, dateString);\n    }\n  }\n\n  validate(_date, value) {\n    return value >= 1 && value <= 53;\n  }\n\n  set(date, _flags, value, options) {\n    return startOfWeek(setWeek(date, value, options), options);\n  }\n\n  incompatibleTokens = [\n    \"y\",\n    \"R\",\n    \"u\",\n    \"q\",\n    \"Q\",\n    \"M\",\n    \"L\",\n    \"I\",\n    \"d\",\n    \"D\",\n    \"i\",\n    \"t\",\n    \"T\",\n  ];\n}\n", "import { getISOWeek } from \"./getISOWeek.js\";\nimport { toDate } from \"./toDate.js\";\n\n/**\n * The {@link setISOWeek} function options.\n */\n\n/**\n * @name setISOWeek\n * @category ISO Week Helpers\n * @summary Set the ISO week to the given date.\n *\n * @description\n * Set the ISO week to the given date, saving the weekday number.\n *\n * ISO week-numbering year: http://en.wikipedia.org/wiki/ISO_week_date\n *\n * @typeParam DateType - The `Date` type, the function operates on. Gets inferred from passed arguments. Allows to use extensions like [`UTCDate`](https://github.com/date-fns/utc).\n * @typeParam ResultDate - The `Date` type of the context function.\n *\n * @param date - The date to be changed\n * @param week - The ISO week of the new date\n * @param options - An object with options\n *\n * @returns The new date with the ISO week set\n *\n * @example\n * // Set the 53rd ISO week to 7 August 2004:\n * const result = setISOWeek(new Date(2004, 7, 7), 53)\n * //=> Sat Jan 01 2005 00:00:00\n */\nexport function setISOWeek(date, week, options) {\n  const _date = toDate(date, options?.in);\n  const diff = getISOWeek(_date, options) - week;\n  _date.setDate(_date.getDate() - diff * 7);\n  return _date;\n}\n\n// Fallback for modularized imports:\nexport default setISOWeek;\n", "import { setISOWeek } from \"../../../setISOWeek.js\";\nimport { startOfISOWeek } from \"../../../startOfISOWeek.js\";\nimport { numericPatterns } from \"../constants.js\";\nimport { Parser } from \"../Parser.js\";\n\nimport { parseNDigits, parseNumericPattern } from \"../utils.js\";\n\n// ISO week of year\nexport class ISOWeekParser extends Parser {\n  priority = 100;\n\n  parse(dateString, token, match) {\n    switch (token) {\n      case \"I\":\n        return parseNumericPattern(numericPatterns.week, dateString);\n      case \"Io\":\n        return match.ordinalNumber(dateString, { unit: \"week\" });\n      default:\n        return parseNDigits(token.length, dateString);\n    }\n  }\n\n  validate(_date, value) {\n    return value >= 1 && value <= 53;\n  }\n\n  set(date, _flags, value) {\n    return startOfISOWeek(setISOWeek(date, value));\n  }\n\n  incompatibleTokens = [\n    \"y\",\n    \"Y\",\n    \"u\",\n    \"q\",\n    \"Q\",\n    \"M\",\n    \"L\",\n    \"w\",\n    \"d\",\n    \"D\",\n    \"e\",\n    \"c\",\n    \"t\",\n    \"T\",\n  ];\n}\n", "import { numericPatterns } from \"../constants.js\";\nimport { Parser } from \"../Parser.js\";\n\nimport {\n  isLeapYearIndex,\n  parseNDigits,\n  parseNumericPattern,\n} from \"../utils.js\";\n\nconst DAYS_IN_MONTH = [31, 28, 31, 30, 31, 30, 31, 31, 30, 31, 30, 31];\nconst DAYS_IN_MONTH_LEAP_YEAR = [\n  31, 29, 31, 30, 31, 30, 31, 31, 30, 31, 30, 31,\n];\n\n// Day of the month\nexport class DateParser extends Parser {\n  priority = 90;\n  subPriority = 1;\n\n  parse(dateString, token, match) {\n    switch (token) {\n      case \"d\":\n        return parseNumericPattern(numericPatterns.date, dateString);\n      case \"do\":\n        return match.ordinalNumber(dateString, { unit: \"date\" });\n      default:\n        return parseNDigits(token.length, dateString);\n    }\n  }\n\n  validate(date, value) {\n    const year = date.getFullYear();\n    const isLeapYear = isLeapYearIndex(year);\n    const month = date.getMonth();\n    if (isLeapYear) {\n      return value >= 1 && value <= DAYS_IN_MONTH_LEAP_YEAR[month];\n    } else {\n      return value >= 1 && value <= DAYS_IN_MONTH[month];\n    }\n  }\n\n  set(date, _flags, value) {\n    date.setDate(value);\n    date.setHours(0, 0, 0, 0);\n    return date;\n  }\n\n  incompatibleTokens = [\n    \"Y\",\n    \"R\",\n    \"q\",\n    \"Q\",\n    \"w\",\n    \"I\",\n    \"D\",\n    \"i\",\n    \"e\",\n    \"c\",\n    \"t\",\n    \"T\",\n  ];\n}\n", "import { numericPatterns } from \"../constants.js\";\nimport { Parser } from \"../Parser.js\";\n\nimport {\n  isLeapYearIndex,\n  parseNDigits,\n  parseNumericPattern,\n} from \"../utils.js\";\n\nexport class DayOfYearParser extends Parser {\n  priority = 90;\n\n  subpriority = 1;\n\n  parse(dateString, token, match) {\n    switch (token) {\n      case \"D\":\n      case \"DD\":\n        return parseNumericPattern(numericPatterns.dayOfYear, dateString);\n      case \"Do\":\n        return match.ordinalNumber(dateString, { unit: \"date\" });\n      default:\n        return parseNDigits(token.length, dateString);\n    }\n  }\n\n  validate(date, value) {\n    const year = date.getFullYear();\n    const isLeapYear = isLeapYearIndex(year);\n    if (isLeapYear) {\n      return value >= 1 && value <= 366;\n    } else {\n      return value >= 1 && value <= 365;\n    }\n  }\n\n  set(date, _flags, value) {\n    date.setMonth(0, value);\n    date.setHours(0, 0, 0, 0);\n    return date;\n  }\n\n  incompatibleTokens = [\n    \"Y\",\n    \"R\",\n    \"q\",\n    \"Q\",\n    \"M\",\n    \"L\",\n    \"w\",\n    \"I\",\n    \"d\",\n    \"E\",\n    \"i\",\n    \"e\",\n    \"c\",\n    \"t\",\n    \"T\",\n  ];\n}\n", "import { getDefaultOptions } from \"./_lib/defaultOptions.js\";\nimport { addDays } from \"./addDays.js\";\nimport { toDate } from \"./toDate.js\";\n\n/**\n * The {@link setDay} function options.\n */\n\n/**\n * @name setDay\n * @category Weekday Helpers\n * @summary Set the day of the week to the given date.\n *\n * @description\n * Set the day of the week to the given date.\n *\n * @typeParam DateType - The `Date` type, the function operates on. Gets inferred from passed arguments. Allows to use extensions like [`UTCDate`](https://github.com/date-fns/utc).\n * @typeParam ResultDate - The result `Date` type, it is the type returned from the context function if it is passed, or inferred from the arguments.\n *\n * @param date - The date to be changed\n * @param day - The day of the week of the new date\n * @param options - An object with options.\n *\n * @returns The new date with the day of the week set\n *\n * @example\n * // Set week day to Sunday, with the default weekStartsOn of Sunday:\n * const result = setDay(new Date(2014, 8, 1), 0)\n * //=> Sun Aug 31 2014 00:00:00\n *\n * @example\n * // Set week day to Sunday, with a weekStartsOn of Monday:\n * const result = setDay(new Date(2014, 8, 1), 0, { weekStartsOn: 1 })\n * //=> Sun Sep 07 2014 00:00:00\n */\nexport function setDay(date, day, options) {\n  const defaultOptions = getDefaultOptions();\n  const weekStartsOn =\n    options?.weekStartsOn ??\n    options?.locale?.options?.weekStartsOn ??\n    defaultOptions.weekStartsOn ??\n    defaultOptions.locale?.options?.weekStartsOn ??\n    0;\n\n  const date_ = toDate(date, options?.in);\n  const currentDay = date_.getDay();\n\n  const remainder = day % 7;\n  const dayIndex = (remainder + 7) % 7;\n\n  const delta = 7 - weekStartsOn;\n  const diff =\n    day < 0 || day > 6\n      ? day - ((currentDay + delta) % 7)\n      : ((dayIndex + delta) % 7) - ((currentDay + delta) % 7);\n  return addDays(date_, diff, options);\n}\n\n// Fallback for modularized imports:\nexport default setDay;\n", "import { setDay } from \"../../../setDay.js\";\nimport { Parser } from \"../Parser.js\";\n\n// Day of week\nexport class Day<PERSON><PERSON><PERSON> extends Parser {\n  priority = 90;\n\n  parse(dateString, token, match) {\n    switch (token) {\n      // Tue\n      case \"E\":\n      case \"EE\":\n      case \"EEE\":\n        return (\n          match.day(dateString, {\n            width: \"abbreviated\",\n            context: \"formatting\",\n          }) ||\n          match.day(dateString, { width: \"short\", context: \"formatting\" }) ||\n          match.day(dateString, { width: \"narrow\", context: \"formatting\" })\n        );\n\n      // T\n      case \"EEEEE\":\n        return match.day(dateString, {\n          width: \"narrow\",\n          context: \"formatting\",\n        });\n      // Tu\n      case \"EEEEEE\":\n        return (\n          match.day(dateString, { width: \"short\", context: \"formatting\" }) ||\n          match.day(dateString, { width: \"narrow\", context: \"formatting\" })\n        );\n\n      // Tuesday\n      case \"EEEE\":\n      default:\n        return (\n          match.day(dateString, { width: \"wide\", context: \"formatting\" }) ||\n          match.day(dateString, {\n            width: \"abbreviated\",\n            context: \"formatting\",\n          }) ||\n          match.day(dateString, { width: \"short\", context: \"formatting\" }) ||\n          match.day(dateString, { width: \"narrow\", context: \"formatting\" })\n        );\n    }\n  }\n\n  validate(_date, value) {\n    return value >= 0 && value <= 6;\n  }\n\n  set(date, _flags, value, options) {\n    date = setDay(date, value, options);\n    date.setHours(0, 0, 0, 0);\n    return date;\n  }\n\n  incompatibleTokens = [\"D\", \"i\", \"e\", \"c\", \"t\", \"T\"];\n}\n", "import { setDay } from \"../../../setDay.js\";\nimport { Parser } from \"../Parser.js\";\n\nimport { mapValue, parseNDigits } from \"../utils.js\";\n\n// Local day of week\nexport class LocalDayParser extends Parser {\n  priority = 90;\n  parse(dateString, token, match, options) {\n    const valueCallback = (value) => {\n      // We want here floor instead of trunc, so we get -7 for value 0 instead of 0\n      const wholeWeekDays = Math.floor((value - 1) / 7) * 7;\n      return ((value + options.weekStartsOn + 6) % 7) + wholeWeekDays;\n    };\n\n    switch (token) {\n      // 3\n      case \"e\":\n      case \"ee\": // 03\n        return mapValue(parseNDigits(token.length, dateString), valueCallback);\n      // 3rd\n      case \"eo\":\n        return mapValue(\n          match.ordinalNumber(dateString, {\n            unit: \"day\",\n          }),\n          valueCallback,\n        );\n      // Tue\n      case \"eee\":\n        return (\n          match.day(dateString, {\n            width: \"abbreviated\",\n            context: \"formatting\",\n          }) ||\n          match.day(dateString, { width: \"short\", context: \"formatting\" }) ||\n          match.day(dateString, { width: \"narrow\", context: \"formatting\" })\n        );\n\n      // T\n      case \"eeeee\":\n        return match.day(dateString, {\n          width: \"narrow\",\n          context: \"formatting\",\n        });\n      // Tu\n      case \"eeeeee\":\n        return (\n          match.day(dateString, { width: \"short\", context: \"formatting\" }) ||\n          match.day(dateString, { width: \"narrow\", context: \"formatting\" })\n        );\n\n      // Tuesday\n      case \"eeee\":\n      default:\n        return (\n          match.day(dateString, { width: \"wide\", context: \"formatting\" }) ||\n          match.day(dateString, {\n            width: \"abbreviated\",\n            context: \"formatting\",\n          }) ||\n          match.day(dateString, { width: \"short\", context: \"formatting\" }) ||\n          match.day(dateString, { width: \"narrow\", context: \"formatting\" })\n        );\n    }\n  }\n\n  validate(_date, value) {\n    return value >= 0 && value <= 6;\n  }\n\n  set(date, _flags, value, options) {\n    date = setDay(date, value, options);\n    date.setHours(0, 0, 0, 0);\n    return date;\n  }\n\n  incompatibleTokens = [\n    \"y\",\n    \"R\",\n    \"u\",\n    \"q\",\n    \"Q\",\n    \"M\",\n    \"L\",\n    \"I\",\n    \"d\",\n    \"D\",\n    \"E\",\n    \"i\",\n    \"c\",\n    \"t\",\n    \"T\",\n  ];\n}\n", "import { setDay } from \"../../../setDay.js\";\nimport { Parser } from \"../Parser.js\";\n\nimport { mapValue, parseNDigits } from \"../utils.js\";\n\n// Stand-alone local day of week\nexport class StandAloneLocalDayParser extends Parser {\n  priority = 90;\n\n  parse(dateString, token, match, options) {\n    const valueCallback = (value) => {\n      // We want here floor instead of trunc, so we get -7 for value 0 instead of 0\n      const wholeWeekDays = Math.floor((value - 1) / 7) * 7;\n      return ((value + options.weekStartsOn + 6) % 7) + wholeWeekDays;\n    };\n\n    switch (token) {\n      // 3\n      case \"c\":\n      case \"cc\": // 03\n        return mapValue(parseNDigits(token.length, dateString), valueCallback);\n      // 3rd\n      case \"co\":\n        return mapValue(\n          match.ordinalNumber(dateString, {\n            unit: \"day\",\n          }),\n          valueCallback,\n        );\n      // Tue\n      case \"ccc\":\n        return (\n          match.day(dateString, {\n            width: \"abbreviated\",\n            context: \"standalone\",\n          }) ||\n          match.day(dateString, { width: \"short\", context: \"standalone\" }) ||\n          match.day(dateString, { width: \"narrow\", context: \"standalone\" })\n        );\n\n      // T\n      case \"ccccc\":\n        return match.day(dateString, {\n          width: \"narrow\",\n          context: \"standalone\",\n        });\n      // Tu\n      case \"cccccc\":\n        return (\n          match.day(dateString, { width: \"short\", context: \"standalone\" }) ||\n          match.day(dateString, { width: \"narrow\", context: \"standalone\" })\n        );\n\n      // Tuesday\n      case \"cccc\":\n      default:\n        return (\n          match.day(dateString, { width: \"wide\", context: \"standalone\" }) ||\n          match.day(dateString, {\n            width: \"abbreviated\",\n            context: \"standalone\",\n          }) ||\n          match.day(dateString, { width: \"short\", context: \"standalone\" }) ||\n          match.day(dateString, { width: \"narrow\", context: \"standalone\" })\n        );\n    }\n  }\n\n  validate(_date, value) {\n    return value >= 0 && value <= 6;\n  }\n\n  set(date, _flags, value, options) {\n    date = setDay(date, value, options);\n    date.setHours(0, 0, 0, 0);\n    return date;\n  }\n\n  incompatibleTokens = [\n    \"y\",\n    \"R\",\n    \"u\",\n    \"q\",\n    \"Q\",\n    \"M\",\n    \"L\",\n    \"I\",\n    \"d\",\n    \"D\",\n    \"E\",\n    \"i\",\n    \"e\",\n    \"t\",\n    \"T\",\n  ];\n}\n", "import { toDate } from \"./toDate.js\";\n\n/**\n * The {@link getISODay} function options.\n */\n\n/**\n * @name getISODay\n * @category Weekday Helpers\n * @summary Get the day of the ISO week of the given date.\n *\n * @description\n * Get the day of the ISO week of the given date,\n * which is 7 for Sunday, 1 for Monday etc.\n *\n * ISO week-numbering year: http://en.wikipedia.org/wiki/ISO_week_date\n *\n * @param date - The given date\n * @param options - An object with options\n *\n * @returns The day of ISO week\n *\n * @example\n * // Which day of the ISO week is 26 February 2012?\n * const result = getISODay(new Date(2012, 1, 26))\n * //=> 7\n */\nexport function getISODay(date, options) {\n  const day = toDate(date, options?.in).getDay();\n  return day === 0 ? 7 : day;\n}\n\n// Fallback for modularized imports:\nexport default getISODay;\n", "import { addDays } from \"./addDays.js\";\nimport { getISODay } from \"./getISODay.js\";\nimport { toDate } from \"./toDate.js\";\n\n/**\n * The {@link setISODay} function options.\n */\n\n/**\n * @name setISODay\n * @category Weekday Helpers\n * @summary Set the day of the ISO week to the given date.\n *\n * @description\n * Set the day of the ISO week to the given date.\n * ISO week starts with Monday.\n * 7 is the index of Sunday, 1 is the index of Monday, etc.\n *\n * @typeParam DateType - The `Date` type, the function operates on. Gets inferred from passed arguments. Allows to use extensions like [`UTCDate`](https://github.com/date-fns/utc).\n * @typeParam ResultDate - The result `Date` type, it is the type returned from the context function if it is passed, or inferred from the arguments.\n *\n * @param date - The date to be changed\n * @param day - The day of the ISO week of the new date\n * @param options - An object with options\n *\n * @returns The new date with the day of the ISO week set\n *\n * @example\n * // Set Sunday to 1 September 2014:\n * const result = setISODay(new Date(2014, 8, 1), 7)\n * //=> Sun Sep 07 2014 00:00:00\n */\nexport function setISODay(date, day, options) {\n  const date_ = toDate(date, options?.in);\n  const currentDay = getISODay(date_, options);\n  const diff = day - currentDay;\n  return addDays(date_, diff, options);\n}\n\n// Fallback for modularized imports:\nexport default setISODay;\n", "import { setISODay } from \"../../../setISODay.js\";\nimport { Parser } from \"../Parser.js\";\n\nimport { mapValue, parseNDigits } from \"../utils.js\";\n\n// ISO day of week\nexport class ISODayParser extends Parser {\n  priority = 90;\n\n  parse(dateString, token, match) {\n    const valueCallback = (value) => {\n      if (value === 0) {\n        return 7;\n      }\n      return value;\n    };\n\n    switch (token) {\n      // 2\n      case \"i\":\n      case \"ii\": // 02\n        return parseNDigits(token.length, dateString);\n      // 2nd\n      case \"io\":\n        return match.ordinalNumber(dateString, { unit: \"day\" });\n      // Tue\n      case \"iii\":\n        return mapValue(\n          match.day(dateString, {\n            width: \"abbreviated\",\n            context: \"formatting\",\n          }) ||\n            match.day(dateString, {\n              width: \"short\",\n              context: \"formatting\",\n            }) ||\n            match.day(dateString, {\n              width: \"narrow\",\n              context: \"formatting\",\n            }),\n          valueCallback,\n        );\n      // T\n      case \"iiiii\":\n        return mapValue(\n          match.day(dateString, {\n            width: \"narrow\",\n            context: \"formatting\",\n          }),\n          valueCallback,\n        );\n      // Tu\n      case \"iiiiii\":\n        return mapValue(\n          match.day(dateString, {\n            width: \"short\",\n            context: \"formatting\",\n          }) ||\n            match.day(dateString, {\n              width: \"narrow\",\n              context: \"formatting\",\n            }),\n          valueCallback,\n        );\n      // Tuesday\n      case \"iiii\":\n      default:\n        return mapValue(\n          match.day(dateString, {\n            width: \"wide\",\n            context: \"formatting\",\n          }) ||\n            match.day(dateString, {\n              width: \"abbreviated\",\n              context: \"formatting\",\n            }) ||\n            match.day(dateString, {\n              width: \"short\",\n              context: \"formatting\",\n            }) ||\n            match.day(dateString, {\n              width: \"narrow\",\n              context: \"formatting\",\n            }),\n          valueCallback,\n        );\n    }\n  }\n\n  validate(_date, value) {\n    return value >= 1 && value <= 7;\n  }\n\n  set(date, _flags, value) {\n    date = setISODay(date, value);\n    date.setHours(0, 0, 0, 0);\n    return date;\n  }\n\n  incompatibleTokens = [\n    \"y\",\n    \"Y\",\n    \"u\",\n    \"q\",\n    \"Q\",\n    \"M\",\n    \"L\",\n    \"w\",\n    \"d\",\n    \"D\",\n    \"E\",\n    \"e\",\n    \"c\",\n    \"t\",\n    \"T\",\n  ];\n}\n", "import { Parser } from \"../Parser.js\";\n\nimport { dayPeriodEnumToHours } from \"../utils.js\";\n\nexport class AMPMParser extends Parser {\n  priority = 80;\n\n  parse(dateString, token, match) {\n    switch (token) {\n      case \"a\":\n      case \"aa\":\n      case \"aaa\":\n        return (\n          match.dayPeriod(dateString, {\n            width: \"abbreviated\",\n            context: \"formatting\",\n          }) ||\n          match.dayPeriod(dateString, {\n            width: \"narrow\",\n            context: \"formatting\",\n          })\n        );\n\n      case \"aaaaa\":\n        return match.dayPeriod(dateString, {\n          width: \"narrow\",\n          context: \"formatting\",\n        });\n      case \"aaaa\":\n      default:\n        return (\n          match.dayPeriod(dateString, {\n            width: \"wide\",\n            context: \"formatting\",\n          }) ||\n          match.dayPeriod(dateString, {\n            width: \"abbreviated\",\n            context: \"formatting\",\n          }) ||\n          match.dayPeriod(dateString, {\n            width: \"narrow\",\n            context: \"formatting\",\n          })\n        );\n    }\n  }\n\n  set(date, _flags, value) {\n    date.setHours(dayPeriodEnumToHours(value), 0, 0, 0);\n    return date;\n  }\n\n  incompatibleTokens = [\"b\", \"B\", \"H\", \"k\", \"t\", \"T\"];\n}\n", "import { Parser } from \"../Parser.js\";\n\nimport { dayPeriodEnumToHours } from \"../utils.js\";\n\nexport class AMPMMidnightParser extends Parser {\n  priority = 80;\n\n  parse(dateString, token, match) {\n    switch (token) {\n      case \"b\":\n      case \"bb\":\n      case \"bbb\":\n        return (\n          match.dayPeriod(dateString, {\n            width: \"abbreviated\",\n            context: \"formatting\",\n          }) ||\n          match.dayPeriod(dateString, {\n            width: \"narrow\",\n            context: \"formatting\",\n          })\n        );\n\n      case \"bbbbb\":\n        return match.dayPeriod(dateString, {\n          width: \"narrow\",\n          context: \"formatting\",\n        });\n      case \"bbbb\":\n      default:\n        return (\n          match.dayPeriod(dateString, {\n            width: \"wide\",\n            context: \"formatting\",\n          }) ||\n          match.dayPeriod(dateString, {\n            width: \"abbreviated\",\n            context: \"formatting\",\n          }) ||\n          match.dayPeriod(dateString, {\n            width: \"narrow\",\n            context: \"formatting\",\n          })\n        );\n    }\n  }\n\n  set(date, _flags, value) {\n    date.setHours(dayPeriodEnumToHours(value), 0, 0, 0);\n    return date;\n  }\n\n  incompatibleTokens = [\"a\", \"B\", \"H\", \"k\", \"t\", \"T\"];\n}\n", "import { Parser } from \"../Parser.js\";\n\nimport { dayPeriodEnumToHours } from \"../utils.js\";\n\n// in the morning, in the afternoon, in the evening, at night\nexport class DayPeriodParser extends Parser {\n  priority = 80;\n\n  parse(dateString, token, match) {\n    switch (token) {\n      case \"B\":\n      case \"BB\":\n      case \"BBB\":\n        return (\n          match.dayPeriod(dateString, {\n            width: \"abbreviated\",\n            context: \"formatting\",\n          }) ||\n          match.dayPeriod(dateString, {\n            width: \"narrow\",\n            context: \"formatting\",\n          })\n        );\n\n      case \"BBBBB\":\n        return match.dayPeriod(dateString, {\n          width: \"narrow\",\n          context: \"formatting\",\n        });\n      case \"BBBB\":\n      default:\n        return (\n          match.dayPeriod(dateString, {\n            width: \"wide\",\n            context: \"formatting\",\n          }) ||\n          match.dayPeriod(dateString, {\n            width: \"abbreviated\",\n            context: \"formatting\",\n          }) ||\n          match.dayPeriod(dateString, {\n            width: \"narrow\",\n            context: \"formatting\",\n          })\n        );\n    }\n  }\n\n  set(date, _flags, value) {\n    date.setHours(dayPeriodEnumToHours(value), 0, 0, 0);\n    return date;\n  }\n\n  incompatibleTokens = [\"a\", \"b\", \"t\", \"T\"];\n}\n", "import { numericPatterns } from \"../constants.js\";\nimport { Parser } from \"../Parser.js\";\n\nimport { parseNDigits, parseNumericPattern } from \"../utils.js\";\n\nexport class Hour1to12Parser extends Parser {\n  priority = 70;\n\n  parse(dateString, token, match) {\n    switch (token) {\n      case \"h\":\n        return parseNumericPattern(numericPatterns.hour12h, dateString);\n      case \"ho\":\n        return match.ordinalNumber(dateString, { unit: \"hour\" });\n      default:\n        return parseNDigits(token.length, dateString);\n    }\n  }\n\n  validate(_date, value) {\n    return value >= 1 && value <= 12;\n  }\n\n  set(date, _flags, value) {\n    const isPM = date.getHours() >= 12;\n    if (isPM && value < 12) {\n      date.setHours(value + 12, 0, 0, 0);\n    } else if (!isPM && value === 12) {\n      date.setHours(0, 0, 0, 0);\n    } else {\n      date.setHours(value, 0, 0, 0);\n    }\n    return date;\n  }\n\n  incompatibleTokens = [\"H\", \"K\", \"k\", \"t\", \"T\"];\n}\n", "import { numericPatterns } from \"../constants.js\";\nimport { Parser } from \"../Parser.js\";\n\nimport { parseNDigits, parseNumericPattern } from \"../utils.js\";\n\nexport class Hour0to23Parser extends Parser {\n  priority = 70;\n\n  parse(dateString, token, match) {\n    switch (token) {\n      case \"H\":\n        return parseNumericPattern(numericPatterns.hour23h, dateString);\n      case \"Ho\":\n        return match.ordinalNumber(dateString, { unit: \"hour\" });\n      default:\n        return parseNDigits(token.length, dateString);\n    }\n  }\n\n  validate(_date, value) {\n    return value >= 0 && value <= 23;\n  }\n\n  set(date, _flags, value) {\n    date.setHours(value, 0, 0, 0);\n    return date;\n  }\n\n  incompatibleTokens = [\"a\", \"b\", \"h\", \"K\", \"k\", \"t\", \"T\"];\n}\n", "import { numericPatterns } from \"../constants.js\";\nimport { Parser } from \"../Parser.js\";\n\nimport { parseNDigits, parseNumericPattern } from \"../utils.js\";\n\nexport class Hour0To11Parser extends Parser {\n  priority = 70;\n\n  parse(dateString, token, match) {\n    switch (token) {\n      case \"K\":\n        return parseNumericPattern(numericPatterns.hour11h, dateString);\n      case \"Ko\":\n        return match.ordinalNumber(dateString, { unit: \"hour\" });\n      default:\n        return parseNDigits(token.length, dateString);\n    }\n  }\n\n  validate(_date, value) {\n    return value >= 0 && value <= 11;\n  }\n\n  set(date, _flags, value) {\n    const isPM = date.getHours() >= 12;\n    if (isPM && value < 12) {\n      date.setHours(value + 12, 0, 0, 0);\n    } else {\n      date.setHours(value, 0, 0, 0);\n    }\n    return date;\n  }\n\n  incompatibleTokens = [\"h\", \"H\", \"k\", \"t\", \"T\"];\n}\n", "import { numericPatterns } from \"../constants.js\";\nimport { Parser } from \"../Parser.js\";\n\nimport { parseNDigits, parseNumericPattern } from \"../utils.js\";\n\nexport class Hour1To24Parser extends Parser {\n  priority = 70;\n\n  parse(dateString, token, match) {\n    switch (token) {\n      case \"k\":\n        return parseNumericPattern(numericPatterns.hour24h, dateString);\n      case \"ko\":\n        return match.ordinalNumber(dateString, { unit: \"hour\" });\n      default:\n        return parseNDigits(token.length, dateString);\n    }\n  }\n\n  validate(_date, value) {\n    return value >= 1 && value <= 24;\n  }\n\n  set(date, _flags, value) {\n    const hours = value <= 24 ? value % 24 : value;\n    date.setHours(hours, 0, 0, 0);\n    return date;\n  }\n\n  incompatibleTokens = [\"a\", \"b\", \"h\", \"H\", \"K\", \"t\", \"T\"];\n}\n", "import { numericPatterns } from \"../constants.js\";\nimport { Parser } from \"../Parser.js\";\n\nimport { parseNDigits, parseNumericPattern } from \"../utils.js\";\n\nexport class MinuteParser extends Parser {\n  priority = 60;\n\n  parse(dateString, token, match) {\n    switch (token) {\n      case \"m\":\n        return parseNumericPattern(numericPatterns.minute, dateString);\n      case \"mo\":\n        return match.ordinalNumber(dateString, { unit: \"minute\" });\n      default:\n        return parseNDigits(token.length, dateString);\n    }\n  }\n\n  validate(_date, value) {\n    return value >= 0 && value <= 59;\n  }\n\n  set(date, _flags, value) {\n    date.setMinutes(value, 0, 0);\n    return date;\n  }\n\n  incompatibleTokens = [\"t\", \"T\"];\n}\n", "import { numericPatterns } from \"../constants.js\";\nimport { Parser } from \"../Parser.js\";\n\nimport { parseNDigits, parseNumericPattern } from \"../utils.js\";\n\nexport class SecondParser extends Parser {\n  priority = 50;\n\n  parse(dateString, token, match) {\n    switch (token) {\n      case \"s\":\n        return parseNumericPattern(numericPatterns.second, dateString);\n      case \"so\":\n        return match.ordinalNumber(dateString, { unit: \"second\" });\n      default:\n        return parseNDigits(token.length, dateString);\n    }\n  }\n\n  validate(_date, value) {\n    return value >= 0 && value <= 59;\n  }\n\n  set(date, _flags, value) {\n    date.setSeconds(value, 0);\n    return date;\n  }\n\n  incompatibleTokens = [\"t\", \"T\"];\n}\n", "import { Parser } from \"../Parser.js\";\n\nimport { mapValue, parseNDigits } from \"../utils.js\";\n\nexport class FractionOfSecondParser extends Parser {\n  priority = 30;\n\n  parse(dateString, token) {\n    const valueCallback = (value) =>\n      Math.trunc(value * Math.pow(10, -token.length + 3));\n    return mapValue(parseNDigits(token.length, dateString), valueCallback);\n  }\n\n  set(date, _flags, value) {\n    date.setMilliseconds(value);\n    return date;\n  }\n\n  incompatibleTokens = [\"t\", \"T\"];\n}\n", "import { constructFrom } from \"../../../constructFrom.js\";\nimport { getTimezoneOffsetInMilliseconds } from \"../../../_lib/getTimezoneOffsetInMilliseconds.js\";\nimport { timezonePatterns } from \"../constants.js\";\nimport { Parser } from \"../Parser.js\";\n\nimport { parseTimezonePattern } from \"../utils.js\";\n\n// Timezone (ISO-8601. +00:00 is `'Z'`)\nexport class ISOTimezoneWithZParser extends Parser {\n  priority = 10;\n\n  parse(dateString, token) {\n    switch (token) {\n      case \"X\":\n        return parseTimezonePattern(\n          timezonePatterns.basicOptionalMinutes,\n          dateString,\n        );\n      case \"XX\":\n        return parseTimezonePattern(timezonePatterns.basic, dateString);\n      case \"XXXX\":\n        return parseTimezonePattern(\n          timezonePatterns.basicOptionalSeconds,\n          dateString,\n        );\n      case \"XXXXX\":\n        return parseTimezonePattern(\n          timezonePatterns.extendedOptionalSeconds,\n          dateString,\n        );\n      case \"XXX\":\n      default:\n        return parseTimezonePattern(timezonePatterns.extended, dateString);\n    }\n  }\n\n  set(date, flags, value) {\n    if (flags.timestampIsSet) return date;\n    return constructFrom(\n      date,\n      date.getTime() - getTimezoneOffsetInMilliseconds(date) - value,\n    );\n  }\n\n  incompatibleTokens = [\"t\", \"T\", \"x\"];\n}\n", "import { constructFrom } from \"../../../constructFrom.js\";\nimport { getTimezoneOffsetInMilliseconds } from \"../../../_lib/getTimezoneOffsetInMilliseconds.js\";\nimport { timezonePatterns } from \"../constants.js\";\nimport { Parser } from \"../Parser.js\";\n\nimport { parseTimezonePattern } from \"../utils.js\";\n\n// Timezone (ISO-8601)\nexport class ISOTimezoneParser extends Parser {\n  priority = 10;\n\n  parse(dateString, token) {\n    switch (token) {\n      case \"x\":\n        return parseTimezonePattern(\n          timezonePatterns.basicOptionalMinutes,\n          dateString,\n        );\n      case \"xx\":\n        return parseTimezonePattern(timezonePatterns.basic, dateString);\n      case \"xxxx\":\n        return parseTimezonePattern(\n          timezonePatterns.basicOptionalSeconds,\n          dateString,\n        );\n      case \"xxxxx\":\n        return parseTimezonePattern(\n          timezonePatterns.extendedOptionalSeconds,\n          dateString,\n        );\n      case \"xxx\":\n      default:\n        return parseTimezonePattern(timezonePatterns.extended, dateString);\n    }\n  }\n\n  set(date, flags, value) {\n    if (flags.timestampIsSet) return date;\n    return constructFrom(\n      date,\n      date.getTime() - getTimezoneOffsetInMilliseconds(date) - value,\n    );\n  }\n\n  incompatibleTokens = [\"t\", \"T\", \"X\"];\n}\n", "import { constructFrom } from \"../../../constructFrom.js\";\nimport { Parser } from \"../Parser.js\";\n\nimport { parseAnyDigitsSigned } from \"../utils.js\";\n\nexport class TimestampSecondsParser extends Parser {\n  priority = 40;\n\n  parse(dateString) {\n    return parseAnyDigitsSigned(dateString);\n  }\n\n  set(date, _flags, value) {\n    return [constructFrom(date, value * 1000), { timestampIsSet: true }];\n  }\n\n  incompatibleTokens = \"*\";\n}\n", "import { constructFrom } from \"../../../constructFrom.js\";\nimport { Parser } from \"../Parser.js\";\n\nimport { parseAnyDigitsSigned } from \"../utils.js\";\n\nexport class TimestampMillisecondsParser extends Parser {\n  priority = 20;\n\n  parse(dateString) {\n    return parseAnyDigitsSigned(dateString);\n  }\n\n  set(date, _flags, value) {\n    return [constructFrom(date, value), { timestampIsSet: true }];\n  }\n\n  incompatibleTokens = \"*\";\n}\n", "import { EraParser } from \"./parsers/EraParser.js\";\nimport { YearParser } from \"./parsers/YearParser.js\";\nimport { LocalWeekYearParser } from \"./parsers/LocalWeekYearParser.js\";\nimport { ISOWeekYearParser } from \"./parsers/ISOWeekYearParser.js\";\nimport { ExtendedYearParser } from \"./parsers/ExtendedYearParser.js\";\nimport { QuarterParser } from \"./parsers/QuarterParser.js\";\nimport { StandAloneQuarterParser } from \"./parsers/StandAloneQuarterParser.js\";\nimport { MonthParser } from \"./parsers/MonthParser.js\";\nimport { StandAloneMonthParser } from \"./parsers/StandAloneMonthParser.js\";\nimport { LocalWeekParser } from \"./parsers/LocalWeekParser.js\";\nimport { ISOWeekParser } from \"./parsers/ISOWeekParser.js\";\nimport { DateParser } from \"./parsers/DateParser.js\";\nimport { DayOfYearParser } from \"./parsers/DayOfYearParser.js\";\nimport { DayParser } from \"./parsers/DayParser.js\";\nimport { LocalDayParser } from \"./parsers/LocalDayParser.js\";\nimport { StandAloneLocalDayParser } from \"./parsers/StandAloneLocalDayParser.js\";\nimport { ISODayParser } from \"./parsers/ISODayParser.js\";\nimport { AMPMParser } from \"./parsers/AMPMParser.js\";\nimport { AMPMMidnightParser } from \"./parsers/AMPMMidnightParser.js\";\nimport { DayPeriodParser } from \"./parsers/DayPeriodParser.js\";\nimport { Hour1to12Parser } from \"./parsers/Hour1to12Parser.js\";\nimport { Hour0to23Parser } from \"./parsers/Hour0to23Parser.js\";\nimport { Hour0To11Parser } from \"./parsers/Hour0To11Parser.js\";\nimport { Hour1To24Parser } from \"./parsers/Hour1To24Parser.js\";\nimport { MinuteParser } from \"./parsers/MinuteParser.js\";\nimport { SecondParser } from \"./parsers/SecondParser.js\";\nimport { FractionOfSecondParser } from \"./parsers/FractionOfSecondParser.js\";\nimport { ISOTimezoneWithZParser } from \"./parsers/ISOTimezoneWithZParser.js\";\nimport { ISOTimezoneParser } from \"./parsers/ISOTimezoneParser.js\";\nimport { TimestampSecondsParser } from \"./parsers/TimestampSecondsParser.js\";\nimport { TimestampMillisecondsParser } from \"./parsers/TimestampMillisecondsParser.js\";\n\n/*\n * |     | Unit                           |     | Unit                           |\n * |-----|--------------------------------|-----|--------------------------------|\n * |  a  | AM, PM                         |  A* | Milliseconds in day            |\n * |  b  | AM, PM, noon, midnight         |  B  | Flexible day period            |\n * |  c  | Stand-alone local day of week  |  C* | Localized hour w/ day period   |\n * |  d  | Day of month                   |  D  | Day of year                    |\n * |  e  | Local day of week              |  E  | Day of week                    |\n * |  f  |                                |  F* | Day of week in month           |\n * |  g* | Modified Julian day            |  G  | Era                            |\n * |  h  | Hour [1-12]                    |  H  | Hour [0-23]                    |\n * |  i! | ISO day of week                |  I! | ISO week of year               |\n * |  j* | Localized hour w/ day period   |  J* | Localized hour w/o day period  |\n * |  k  | Hour [1-24]                    |  K  | Hour [0-11]                    |\n * |  l* | (deprecated)                   |  L  | Stand-alone month              |\n * |  m  | Minute                         |  M  | Month                          |\n * |  n  |                                |  N  |                                |\n * |  o! | Ordinal number modifier        |  O* | Timezone (GMT)                 |\n * |  p  |                                |  P  |                                |\n * |  q  | Stand-alone quarter            |  Q  | Quarter                        |\n * |  r* | Related Gregorian year         |  R! | ISO week-numbering year        |\n * |  s  | Second                         |  S  | Fraction of second             |\n * |  t! | Seconds timestamp              |  T! | Milliseconds timestamp         |\n * |  u  | Extended year                  |  U* | Cyclic year                    |\n * |  v* | Timezone (generic non-locat.)  |  V* | Timezone (location)            |\n * |  w  | Local week of year             |  W* | Week of month                  |\n * |  x  | Timezone (ISO-8601 w/o Z)      |  X  | Timezone (ISO-8601)            |\n * |  y  | Year (abs)                     |  Y  | Local week-numbering year      |\n * |  z* | Timezone (specific non-locat.) |  Z* | Timezone (aliases)             |\n *\n * Letters marked by * are not implemented but reserved by Unicode standard.\n *\n * Letters marked by ! are non-standard, but implemented by date-fns:\n * - `o` modifies the previous token to turn it into an ordinal (see `parse` docs)\n * - `i` is ISO day of week. For `i` and `ii` is returns numeric ISO week days,\n *   i.e. 7 for Sunday, 1 for Monday, etc.\n * - `I` is ISO week of year, as opposed to `w` which is local week of year.\n * - `R` is ISO week-numbering year, as opposed to `Y` which is local week-numbering year.\n *   `R` is supposed to be used in conjunction with `I` and `i`\n *   for universal ISO week-numbering date, whereas\n *   `Y` is supposed to be used in conjunction with `w` and `e`\n *   for week-numbering date specific to the locale.\n */\nexport const parsers = {\n  G: new EraParser(),\n  y: new YearParser(),\n  Y: new LocalWeekYearParser(),\n  R: new ISOWeekYearParser(),\n  u: new ExtendedYearParser(),\n  Q: new QuarterParser(),\n  q: new StandAloneQuarterParser(),\n  M: new MonthParser(),\n  L: new StandAloneMonthParser(),\n  w: new LocalWeekParser(),\n  I: new ISOWeekParser(),\n  d: new DateParser(),\n  D: new DayOfYearParser(),\n  E: new DayParser(),\n  e: new LocalDayParser(),\n  c: new StandAloneLocalDayParser(),\n  i: new ISODayParser(),\n  a: new AMPMParser(),\n  b: new AMPMMidnightParser(),\n  B: new DayPeriodParser(),\n  h: new Hour1to12Parser(),\n  H: new Hour0to23Parser(),\n  K: new Hour0To11Parser(),\n  k: new Hour1To24Parser(),\n  m: new MinuteParser(),\n  s: new SecondParser(),\n  S: new FractionOfSecondParser(),\n  X: new ISOTimezoneWithZParser(),\n  x: new ISOTimezoneParser(),\n  t: new TimestampSecondsParser(),\n  T: new TimestampMillisecondsParser(),\n};\n", "import { defaultLocale } from \"./_lib/defaultLocale.js\";\nimport { longFormatters } from \"./_lib/format/longFormatters.js\";\nimport {\n  isProtectedDayOfYearToken,\n  isProtectedWeekYearToken,\n  warnOrThrowProtectedError,\n} from \"./_lib/protectedTokens.js\";\nimport { constructFrom } from \"./constructFrom.js\";\nimport { getDefaultOptions } from \"./getDefaultOptions.js\";\nimport { toDate } from \"./toDate.js\";\n\nimport { DateTimezoneSetter } from \"./parse/_lib/Setter.js\";\nimport { parsers } from \"./parse/_lib/parsers.js\";\n\n// Rexports of internal for libraries to use.\n// See: https://github.com/date-fns/date-fns/issues/3638#issuecomment-1877082874\nexport { longFormatters, parsers };\n\n/**\n * The {@link parse} function options.\n */\n\n// This RegExp consists of three parts separated by `|`:\n// - [yYQqMLwIdDecihHKkms]o matches any available ordinal number token\n//   (one of the certain letters followed by `o`)\n// - (\\w)\\1* matches any sequences of the same letter\n// - '' matches two quote characters in a row\n// - '(''|[^'])+('|$) matches anything surrounded by two quote characters ('),\n//   except a single quote symbol, which ends the sequence.\n//   Two quote characters do not end the sequence.\n//   If there is no matching single quote\n//   then the sequence will continue until the end of the string.\n// - . matches any single character unmatched by previous parts of the RegExps\nconst formattingTokensRegExp =\n  /[yYQqMLwIdDecihHKkms]o|(\\w)\\1*|''|'(''|[^'])+('|$)|./g;\n\n// This RegExp catches symbols escaped by quotes, and also\n// sequences of symbols P, p, and the combinations like `PPPPPPPppppp`\nconst longFormattingTokensRegExp = /P+p+|P+|p+|''|'(''|[^'])+('|$)|./g;\n\nconst escapedStringRegExp = /^'([^]*?)'?$/;\nconst doubleQuoteRegExp = /''/g;\n\nconst notWhitespaceRegExp = /\\S/;\nconst unescapedLatinCharacterRegExp = /[a-zA-Z]/;\n\n/**\n * @name parse\n * @category Common Helpers\n * @summary Parse the date.\n *\n * @description\n * Return the date parsed from string using the given format string.\n *\n * > ⚠️ Please note that the `format` tokens differ from Moment.js and other libraries.\n * > See: https://github.com/date-fns/date-fns/blob/master/docs/unicodeTokens.md\n *\n * The characters in the format string wrapped between two single quotes characters (') are escaped.\n * Two single quotes in a row, whether inside or outside a quoted sequence, represent a 'real' single quote.\n *\n * Format of the format string is based on Unicode Technical Standard #35:\n * https://www.unicode.org/reports/tr35/tr35-dates.html#Date_Field_Symbol_Table\n * with a few additions (see note 5 below the table).\n *\n * Not all tokens are compatible. Combinations that don't make sense or could lead to bugs are prohibited\n * and will throw `RangeError`. For example usage of 24-hour format token with AM/PM token will throw an exception:\n *\n * ```javascript\n * parse('23 AM', 'HH a', new Date())\n * //=> RangeError: The format string mustn't contain `HH` and `a` at the same time\n * ```\n *\n * See the compatibility table: https://docs.google.com/spreadsheets/d/e/2PACX-1vQOPU3xUhplll6dyoMmVUXHKl_8CRDs6_ueLmex3SoqwhuolkuN3O05l4rqx5h1dKX8eb46Ul-CCSrq/pubhtml?gid=0&single=true\n *\n * Accepted format string patterns:\n * | Unit                            |Prior| Pattern | Result examples                   | Notes |\n * |---------------------------------|-----|---------|-----------------------------------|-------|\n * | Era                             | 140 | G..GGG  | AD, BC                            |       |\n * |                                 |     | GGGG    | Anno Domini, Before Christ        | 2     |\n * |                                 |     | GGGGG   | A, B                              |       |\n * | Calendar year                   | 130 | y       | 44, 1, 1900, 2017, 9999           | 4     |\n * |                                 |     | yo      | 44th, 1st, 1900th, 9999999th      | 4,5   |\n * |                                 |     | yy      | 44, 01, 00, 17                    | 4     |\n * |                                 |     | yyy     | 044, 001, 123, 999                | 4     |\n * |                                 |     | yyyy    | 0044, 0001, 1900, 2017            | 4     |\n * |                                 |     | yyyyy   | ...                               | 2,4   |\n * | Local week-numbering year       | 130 | Y       | 44, 1, 1900, 2017, 9000           | 4     |\n * |                                 |     | Yo      | 44th, 1st, 1900th, 9999999th      | 4,5   |\n * |                                 |     | YY      | 44, 01, 00, 17                    | 4,6   |\n * |                                 |     | YYY     | 044, 001, 123, 999                | 4     |\n * |                                 |     | YYYY    | 0044, 0001, 1900, 2017            | 4,6   |\n * |                                 |     | YYYYY   | ...                               | 2,4   |\n * | ISO week-numbering year         | 130 | R       | -43, 1, 1900, 2017, 9999, -9999   | 4,5   |\n * |                                 |     | RR      | -43, 01, 00, 17                   | 4,5   |\n * |                                 |     | RRR     | -043, 001, 123, 999, -999         | 4,5   |\n * |                                 |     | RRRR    | -0043, 0001, 2017, 9999, -9999    | 4,5   |\n * |                                 |     | RRRRR   | ...                               | 2,4,5 |\n * | Extended year                   | 130 | u       | -43, 1, 1900, 2017, 9999, -999    | 4     |\n * |                                 |     | uu      | -43, 01, 99, -99                  | 4     |\n * |                                 |     | uuu     | -043, 001, 123, 999, -999         | 4     |\n * |                                 |     | uuuu    | -0043, 0001, 2017, 9999, -9999    | 4     |\n * |                                 |     | uuuuu   | ...                               | 2,4   |\n * | Quarter (formatting)            | 120 | Q       | 1, 2, 3, 4                        |       |\n * |                                 |     | Qo      | 1st, 2nd, 3rd, 4th                | 5     |\n * |                                 |     | QQ      | 01, 02, 03, 04                    |       |\n * |                                 |     | QQQ     | Q1, Q2, Q3, Q4                    |       |\n * |                                 |     | QQQQ    | 1st quarter, 2nd quarter, ...     | 2     |\n * |                                 |     | QQQQQ   | 1, 2, 3, 4                        | 4     |\n * | Quarter (stand-alone)           | 120 | q       | 1, 2, 3, 4                        |       |\n * |                                 |     | qo      | 1st, 2nd, 3rd, 4th                | 5     |\n * |                                 |     | qq      | 01, 02, 03, 04                    |       |\n * |                                 |     | qqq     | Q1, Q2, Q3, Q4                    |       |\n * |                                 |     | qqqq    | 1st quarter, 2nd quarter, ...     | 2     |\n * |                                 |     | qqqqq   | 1, 2, 3, 4                        | 3     |\n * | Month (formatting)              | 110 | M       | 1, 2, ..., 12                     |       |\n * |                                 |     | Mo      | 1st, 2nd, ..., 12th               | 5     |\n * |                                 |     | MM      | 01, 02, ..., 12                   |       |\n * |                                 |     | MMM     | Jan, Feb, ..., Dec                |       |\n * |                                 |     | MMMM    | January, February, ..., December  | 2     |\n * |                                 |     | MMMMM   | J, F, ..., D                      |       |\n * | Month (stand-alone)             | 110 | L       | 1, 2, ..., 12                     |       |\n * |                                 |     | Lo      | 1st, 2nd, ..., 12th               | 5     |\n * |                                 |     | LL      | 01, 02, ..., 12                   |       |\n * |                                 |     | LLL     | Jan, Feb, ..., Dec                |       |\n * |                                 |     | LLLL    | January, February, ..., December  | 2     |\n * |                                 |     | LLLLL   | J, F, ..., D                      |       |\n * | Local week of year              | 100 | w       | 1, 2, ..., 53                     |       |\n * |                                 |     | wo      | 1st, 2nd, ..., 53th               | 5     |\n * |                                 |     | ww      | 01, 02, ..., 53                   |       |\n * | ISO week of year                | 100 | I       | 1, 2, ..., 53                     | 5     |\n * |                                 |     | Io      | 1st, 2nd, ..., 53th               | 5     |\n * |                                 |     | II      | 01, 02, ..., 53                   | 5     |\n * | Day of month                    |  90 | d       | 1, 2, ..., 31                     |       |\n * |                                 |     | do      | 1st, 2nd, ..., 31st               | 5     |\n * |                                 |     | dd      | 01, 02, ..., 31                   |       |\n * | Day of year                     |  90 | D       | 1, 2, ..., 365, 366               | 7     |\n * |                                 |     | Do      | 1st, 2nd, ..., 365th, 366th       | 5     |\n * |                                 |     | DD      | 01, 02, ..., 365, 366             | 7     |\n * |                                 |     | DDD     | 001, 002, ..., 365, 366           |       |\n * |                                 |     | DDDD    | ...                               | 2     |\n * | Day of week (formatting)        |  90 | E..EEE  | Mon, Tue, Wed, ..., Sun           |       |\n * |                                 |     | EEEE    | Monday, Tuesday, ..., Sunday      | 2     |\n * |                                 |     | EEEEE   | M, T, W, T, F, S, S               |       |\n * |                                 |     | EEEEEE  | Mo, Tu, We, Th, Fr, Sa, Su        |       |\n * | ISO day of week (formatting)    |  90 | i       | 1, 2, 3, ..., 7                   | 5     |\n * |                                 |     | io      | 1st, 2nd, ..., 7th                | 5     |\n * |                                 |     | ii      | 01, 02, ..., 07                   | 5     |\n * |                                 |     | iii     | Mon, Tue, Wed, ..., Sun           | 5     |\n * |                                 |     | iiii    | Monday, Tuesday, ..., Sunday      | 2,5   |\n * |                                 |     | iiiii   | M, T, W, T, F, S, S               | 5     |\n * |                                 |     | iiiiii  | Mo, Tu, We, Th, Fr, Sa, Su        | 5     |\n * | Local day of week (formatting)  |  90 | e       | 2, 3, 4, ..., 1                   |       |\n * |                                 |     | eo      | 2nd, 3rd, ..., 1st                | 5     |\n * |                                 |     | ee      | 02, 03, ..., 01                   |       |\n * |                                 |     | eee     | Mon, Tue, Wed, ..., Sun           |       |\n * |                                 |     | eeee    | Monday, Tuesday, ..., Sunday      | 2     |\n * |                                 |     | eeeee   | M, T, W, T, F, S, S               |       |\n * |                                 |     | eeeeee  | Mo, Tu, We, Th, Fr, Sa, Su        |       |\n * | Local day of week (stand-alone) |  90 | c       | 2, 3, 4, ..., 1                   |       |\n * |                                 |     | co      | 2nd, 3rd, ..., 1st                | 5     |\n * |                                 |     | cc      | 02, 03, ..., 01                   |       |\n * |                                 |     | ccc     | Mon, Tue, Wed, ..., Sun           |       |\n * |                                 |     | cccc    | Monday, Tuesday, ..., Sunday      | 2     |\n * |                                 |     | ccccc   | M, T, W, T, F, S, S               |       |\n * |                                 |     | cccccc  | Mo, Tu, We, Th, Fr, Sa, Su        |       |\n * | AM, PM                          |  80 | a..aaa  | AM, PM                            |       |\n * |                                 |     | aaaa    | a.m., p.m.                        | 2     |\n * |                                 |     | aaaaa   | a, p                              |       |\n * | AM, PM, noon, midnight          |  80 | b..bbb  | AM, PM, noon, midnight            |       |\n * |                                 |     | bbbb    | a.m., p.m., noon, midnight        | 2     |\n * |                                 |     | bbbbb   | a, p, n, mi                       |       |\n * | Flexible day period             |  80 | B..BBB  | at night, in the morning, ...     |       |\n * |                                 |     | BBBB    | at night, in the morning, ...     | 2     |\n * |                                 |     | BBBBB   | at night, in the morning, ...     |       |\n * | Hour [1-12]                     |  70 | h       | 1, 2, ..., 11, 12                 |       |\n * |                                 |     | ho      | 1st, 2nd, ..., 11th, 12th         | 5     |\n * |                                 |     | hh      | 01, 02, ..., 11, 12               |       |\n * | Hour [0-23]                     |  70 | H       | 0, 1, 2, ..., 23                  |       |\n * |                                 |     | Ho      | 0th, 1st, 2nd, ..., 23rd          | 5     |\n * |                                 |     | HH      | 00, 01, 02, ..., 23               |       |\n * | Hour [0-11]                     |  70 | K       | 1, 2, ..., 11, 0                  |       |\n * |                                 |     | Ko      | 1st, 2nd, ..., 11th, 0th          | 5     |\n * |                                 |     | KK      | 01, 02, ..., 11, 00               |       |\n * | Hour [1-24]                     |  70 | k       | 24, 1, 2, ..., 23                 |       |\n * |                                 |     | ko      | 24th, 1st, 2nd, ..., 23rd         | 5     |\n * |                                 |     | kk      | 24, 01, 02, ..., 23               |       |\n * | Minute                          |  60 | m       | 0, 1, ..., 59                     |       |\n * |                                 |     | mo      | 0th, 1st, ..., 59th               | 5     |\n * |                                 |     | mm      | 00, 01, ..., 59                   |       |\n * | Second                          |  50 | s       | 0, 1, ..., 59                     |       |\n * |                                 |     | so      | 0th, 1st, ..., 59th               | 5     |\n * |                                 |     | ss      | 00, 01, ..., 59                   |       |\n * | Seconds timestamp               |  40 | t       | 512969520                         |       |\n * |                                 |     | tt      | ...                               | 2     |\n * | Fraction of second              |  30 | S       | 0, 1, ..., 9                      |       |\n * |                                 |     | SS      | 00, 01, ..., 99                   |       |\n * |                                 |     | SSS     | 000, 001, ..., 999                |       |\n * |                                 |     | SSSS    | ...                               | 2     |\n * | Milliseconds timestamp          |  20 | T       | 512969520900                      |       |\n * |                                 |     | TT      | ...                               | 2     |\n * | Timezone (ISO-8601 w/ Z)        |  10 | X       | -08, +0530, Z                     |       |\n * |                                 |     | XX      | -0800, +0530, Z                   |       |\n * |                                 |     | XXX     | -08:00, +05:30, Z                 |       |\n * |                                 |     | XXXX    | -0800, +0530, Z, +123456          | 2     |\n * |                                 |     | XXXXX   | -08:00, +05:30, Z, +12:34:56      |       |\n * | Timezone (ISO-8601 w/o Z)       |  10 | x       | -08, +0530, +00                   |       |\n * |                                 |     | xx      | -0800, +0530, +0000               |       |\n * |                                 |     | xxx     | -08:00, +05:30, +00:00            | 2     |\n * |                                 |     | xxxx    | -0800, +0530, +0000, +123456      |       |\n * |                                 |     | xxxxx   | -08:00, +05:30, +00:00, +12:34:56 |       |\n * | Long localized date             |  NA | P       | 05/29/1453                        | 5,8   |\n * |                                 |     | PP      | May 29, 1453                      |       |\n * |                                 |     | PPP     | May 29th, 1453                    |       |\n * |                                 |     | PPPP    | Sunday, May 29th, 1453            | 2,5,8 |\n * | Long localized time             |  NA | p       | 12:00 AM                          | 5,8   |\n * |                                 |     | pp      | 12:00:00 AM                       |       |\n * | Combination of date and time    |  NA | Pp      | 05/29/1453, 12:00 AM              |       |\n * |                                 |     | PPpp    | May 29, 1453, 12:00:00 AM         |       |\n * |                                 |     | PPPpp   | May 29th, 1453 at ...             |       |\n * |                                 |     | PPPPpp  | Sunday, May 29th, 1453 at ...     | 2,5,8 |\n * Notes:\n * 1. \"Formatting\" units (e.g. formatting quarter) in the default en-US locale\n *    are the same as \"stand-alone\" units, but are different in some languages.\n *    \"Formatting\" units are declined according to the rules of the language\n *    in the context of a date. \"Stand-alone\" units are always nominative singular.\n *    In `format` function, they will produce different result:\n *\n *    `format(new Date(2017, 10, 6), 'do LLLL', {locale: cs}) //=> '6. listopad'`\n *\n *    `format(new Date(2017, 10, 6), 'do MMMM', {locale: cs}) //=> '6. listopadu'`\n *\n *    `parse` will try to match both formatting and stand-alone units interchangeably.\n *\n * 2. Any sequence of the identical letters is a pattern, unless it is escaped by\n *    the single quote characters (see below).\n *    If the sequence is longer than listed in table:\n *    - for numerical units (`yyyyyyyy`) `parse` will try to match a number\n *      as wide as the sequence\n *    - for text units (`MMMMMMMM`) `parse` will try to match the widest variation of the unit.\n *      These variations are marked with \"2\" in the last column of the table.\n *\n * 3. `QQQQQ` and `qqqqq` could be not strictly numerical in some locales.\n *    These tokens represent the shortest form of the quarter.\n *\n * 4. The main difference between `y` and `u` patterns are B.C. years:\n *\n *    | Year | `y` | `u` |\n *    |------|-----|-----|\n *    | AC 1 |   1 |   1 |\n *    | BC 1 |   1 |   0 |\n *    | BC 2 |   2 |  -1 |\n *\n *    Also `yy` will try to guess the century of two digit year by proximity with `referenceDate`:\n *\n *    `parse('50', 'yy', new Date(2018, 0, 1)) //=> Sat Jan 01 2050 00:00:00`\n *\n *    `parse('75', 'yy', new Date(2018, 0, 1)) //=> Wed Jan 01 1975 00:00:00`\n *\n *    while `uu` will just assign the year as is:\n *\n *    `parse('50', 'uu', new Date(2018, 0, 1)) //=> Sat Jan 01 0050 00:00:00`\n *\n *    `parse('75', 'uu', new Date(2018, 0, 1)) //=> Tue Jan 01 0075 00:00:00`\n *\n *    The same difference is true for local and ISO week-numbering years (`Y` and `R`),\n *    except local week-numbering years are dependent on `options.weekStartsOn`\n *    and `options.firstWeekContainsDate` (compare [setISOWeekYear](https://date-fns.org/docs/setISOWeekYear)\n *    and [setWeekYear](https://date-fns.org/docs/setWeekYear)).\n *\n * 5. These patterns are not in the Unicode Technical Standard #35:\n *    - `i`: ISO day of week\n *    - `I`: ISO week of year\n *    - `R`: ISO week-numbering year\n *    - `o`: ordinal number modifier\n *    - `P`: long localized date\n *    - `p`: long localized time\n *\n * 6. `YY` and `YYYY` tokens represent week-numbering years but they are often confused with years.\n *    You should enable `options.useAdditionalWeekYearTokens` to use them. See: https://github.com/date-fns/date-fns/blob/master/docs/unicodeTokens.md\n *\n * 7. `D` and `DD` tokens represent days of the year but they are often confused with days of the month.\n *    You should enable `options.useAdditionalDayOfYearTokens` to use them. See: https://github.com/date-fns/date-fns/blob/master/docs/unicodeTokens.md\n *\n * 8. `P+` tokens do not have a defined priority since they are merely aliases to other tokens based\n *    on the given locale.\n *\n *    using `en-US` locale: `P` => `MM/dd/yyyy`\n *    using `en-US` locale: `p` => `hh:mm a`\n *    using `pt-BR` locale: `P` => `dd/MM/yyyy`\n *    using `pt-BR` locale: `p` => `HH:mm`\n *\n * Values will be assigned to the date in the descending order of its unit's priority.\n * Units of an equal priority overwrite each other in the order of appearance.\n *\n * If no values of higher priority are parsed (e.g. when parsing string 'January 1st' without a year),\n * the values will be taken from 3rd argument `referenceDate` which works as a context of parsing.\n *\n * `referenceDate` must be passed for correct work of the function.\n * If you're not sure which `referenceDate` to supply, create a new instance of Date:\n * `parse('02/11/2014', 'MM/dd/yyyy', new Date())`\n * In this case parsing will be done in the context of the current date.\n * If `referenceDate` is `Invalid Date` or a value not convertible to valid `Date`,\n * then `Invalid Date` will be returned.\n *\n * The result may vary by locale.\n *\n * If `formatString` matches with `dateString` but does not provides tokens, `referenceDate` will be returned.\n *\n * If parsing failed, `Invalid Date` will be returned.\n * Invalid Date is a Date, whose time value is NaN.\n * Time value of Date: http://es5.github.io/#x15.9.1.1\n *\n * @typeParam DateType - The `Date` type, the function operates on. Gets inferred from passed arguments. Allows to use extensions like [`UTCDate`](https://github.com/date-fns/utc).\n * @typeParam ResultDate - The result `Date` type, it is the type returned from the context function if it is passed, or inferred from the arguments.\n *\n * @param dateStr - The string to parse\n * @param formatStr - The string of tokens\n * @param referenceDate - defines values missing from the parsed dateString\n * @param options - An object with options.\n *   see: https://github.com/date-fns/date-fns/blob/master/docs/unicodeTokens.md\n *   see: https://github.com/date-fns/date-fns/blob/master/docs/unicodeTokens.md\n *\n * @returns The parsed date\n *\n * @throws `options.locale` must contain `match` property\n * @throws use `yyyy` instead of `YYYY` for formatting years using [format provided] to the input [input provided]; see: https://github.com/date-fns/date-fns/blob/master/docs/unicodeTokens.md\n * @throws use `yy` instead of `YY` for formatting years using [format provided] to the input [input provided]; see: https://github.com/date-fns/date-fns/blob/master/docs/unicodeTokens.md\n * @throws use `d` instead of `D` for formatting days of the month using [format provided] to the input [input provided]; see: https://github.com/date-fns/date-fns/blob/master/docs/unicodeTokens.md\n * @throws use `dd` instead of `DD` for formatting days of the month using [format provided] to the input [input provided]; see: https://github.com/date-fns/date-fns/blob/master/docs/unicodeTokens.md\n * @throws format string contains an unescaped latin alphabet character\n *\n * @example\n * // Parse 11 February 2014 from middle-endian format:\n * var result = parse('02/11/2014', 'MM/dd/yyyy', new Date())\n * //=> Tue Feb 11 2014 00:00:00\n *\n * @example\n * // Parse 28th of February in Esperanto locale in the context of 2010 year:\n * import eo from 'date-fns/locale/eo'\n * var result = parse('28-a de februaro', \"do 'de' MMMM\", new Date(2010, 0, 1), {\n *   locale: eo\n * })\n * //=> Sun Feb 28 2010 00:00:00\n */\nexport function parse(dateStr, formatStr, referenceDate, options) {\n  const invalidDate = () => constructFrom(options?.in || referenceDate, NaN);\n  const defaultOptions = getDefaultOptions();\n  const locale = options?.locale ?? defaultOptions.locale ?? defaultLocale;\n\n  const firstWeekContainsDate =\n    options?.firstWeekContainsDate ??\n    options?.locale?.options?.firstWeekContainsDate ??\n    defaultOptions.firstWeekContainsDate ??\n    defaultOptions.locale?.options?.firstWeekContainsDate ??\n    1;\n\n  const weekStartsOn =\n    options?.weekStartsOn ??\n    options?.locale?.options?.weekStartsOn ??\n    defaultOptions.weekStartsOn ??\n    defaultOptions.locale?.options?.weekStartsOn ??\n    0;\n\n  if (!formatStr)\n    return dateStr ? invalidDate() : toDate(referenceDate, options?.in);\n\n  const subFnOptions = {\n    firstWeekContainsDate,\n    weekStartsOn,\n    locale,\n  };\n\n  // If timezone isn't specified, it will try to use the context or\n  // the reference date and fallback to the system time zone.\n  const setters = [new DateTimezoneSetter(options?.in, referenceDate)];\n\n  const tokens = formatStr\n    .match(longFormattingTokensRegExp)\n    .map((substring) => {\n      const firstCharacter = substring[0];\n      if (firstCharacter in longFormatters) {\n        const longFormatter = longFormatters[firstCharacter];\n        return longFormatter(substring, locale.formatLong);\n      }\n      return substring;\n    })\n    .join(\"\")\n    .match(formattingTokensRegExp);\n\n  const usedTokens = [];\n\n  for (let token of tokens) {\n    if (\n      !options?.useAdditionalWeekYearTokens &&\n      isProtectedWeekYearToken(token)\n    ) {\n      warnOrThrowProtectedError(token, formatStr, dateStr);\n    }\n    if (\n      !options?.useAdditionalDayOfYearTokens &&\n      isProtectedDayOfYearToken(token)\n    ) {\n      warnOrThrowProtectedError(token, formatStr, dateStr);\n    }\n\n    const firstCharacter = token[0];\n    const parser = parsers[firstCharacter];\n    if (parser) {\n      const { incompatibleTokens } = parser;\n      if (Array.isArray(incompatibleTokens)) {\n        const incompatibleToken = usedTokens.find(\n          (usedToken) =>\n            incompatibleTokens.includes(usedToken.token) ||\n            usedToken.token === firstCharacter,\n        );\n        if (incompatibleToken) {\n          throw new RangeError(\n            `The format string mustn't contain \\`${incompatibleToken.fullToken}\\` and \\`${token}\\` at the same time`,\n          );\n        }\n      } else if (parser.incompatibleTokens === \"*\" && usedTokens.length > 0) {\n        throw new RangeError(\n          `The format string mustn't contain \\`${token}\\` and any other token at the same time`,\n        );\n      }\n\n      usedTokens.push({ token: firstCharacter, fullToken: token });\n\n      const parseResult = parser.run(\n        dateStr,\n        token,\n        locale.match,\n        subFnOptions,\n      );\n\n      if (!parseResult) {\n        return invalidDate();\n      }\n\n      setters.push(parseResult.setter);\n\n      dateStr = parseResult.rest;\n    } else {\n      if (firstCharacter.match(unescapedLatinCharacterRegExp)) {\n        throw new RangeError(\n          \"Format string contains an unescaped latin alphabet character `\" +\n            firstCharacter +\n            \"`\",\n        );\n      }\n\n      // Replace two single quote characters with one single quote character\n      if (token === \"''\") {\n        token = \"'\";\n      } else if (firstCharacter === \"'\") {\n        token = cleanEscapedString(token);\n      }\n\n      // Cut token from string, or, if string doesn't match the token, return Invalid Date\n      if (dateStr.indexOf(token) === 0) {\n        dateStr = dateStr.slice(token.length);\n      } else {\n        return invalidDate();\n      }\n    }\n  }\n\n  // Check if the remaining input contains something other than whitespace\n  if (dateStr.length > 0 && notWhitespaceRegExp.test(dateStr)) {\n    return invalidDate();\n  }\n\n  const uniquePrioritySetters = setters\n    .map((setter) => setter.priority)\n    .sort((a, b) => b - a)\n    .filter((priority, index, array) => array.indexOf(priority) === index)\n    .map((priority) =>\n      setters\n        .filter((setter) => setter.priority === priority)\n        .sort((a, b) => b.subPriority - a.subPriority),\n    )\n    .map((setterArray) => setterArray[0]);\n\n  let date = toDate(referenceDate, options?.in);\n\n  if (isNaN(+date)) return invalidDate();\n\n  const flags = {};\n  for (const setter of uniquePrioritySetters) {\n    if (!setter.validate(date, subFnOptions)) {\n      return invalidDate();\n    }\n\n    const result = setter.set(date, flags, subFnOptions);\n    // Result is tuple (date, flags)\n    if (Array.isArray(result)) {\n      date = result[0];\n      Object.assign(flags, result[1]);\n      // Result is date\n    } else {\n      date = result;\n    }\n  }\n\n  return date;\n}\n\nfunction cleanEscapedString(input) {\n  return input.match(escapedStringRegExp)[1].replace(doubleQuoteRegExp, \"'\");\n}\n\n// Fallback for modularized imports:\nexport default parse;\n", "import { toDate } from \"./toDate.js\";\n\n/**\n * The {@link getQuarter} function options.\n */\n\n/**\n * @name getQuarter\n * @category Quarter Helpers\n * @summary Get the year quarter of the given date.\n *\n * @description\n * Get the year quarter of the given date.\n *\n * @param date - The given date\n * @param options - An object with options\n *\n * @returns The quarter\n *\n * @example\n * // Which quarter is 2 July 2014?\n * const result = getQuarter(new Date(2014, 6, 2));\n * //=> 3\n */\nexport function getQuarter(date, options) {\n  const _date = toDate(date, options?.in);\n  const quarter = Math.trunc(_date.getMonth() / 3) + 1;\n  return quarter;\n}\n\n// Fallback for modularized imports:\nexport default getQuarter;\n", "import { getDefaultOptions } from \"./_lib/defaultOptions.js\";\nimport { constructFrom } from \"./constructFrom.js\";\nimport { startOfWeek } from \"./startOfWeek.js\";\nimport { toDate } from \"./toDate.js\";\n\n/**\n * The {@link getWeekYear} function options.\n */\n\n/**\n * @name getWeekYear\n * @category Week-Numbering Year Helpers\n * @summary Get the local week-numbering year of the given date.\n *\n * @description\n * Get the local week-numbering year of the given date.\n * The exact calculation depends on the values of\n * `options.weekStartsOn` (which is the index of the first day of the week)\n * and `options.firstWeekContainsDate` (which is the day of January, which is always in\n * the first week of the week-numbering year)\n *\n * Week numbering: https://en.wikipedia.org/wiki/Week#The_ISO_week_date_system\n *\n * @param date - The given date\n * @param options - An object with options.\n *\n * @returns The local week-numbering year\n *\n * @example\n * // Which week numbering year is 26 December 2004 with the default settings?\n * const result = getWeekYear(new Date(2004, 11, 26))\n * //=> 2005\n *\n * @example\n * // Which week numbering year is 26 December 2004 if week starts on Saturday?\n * const result = getWeekYear(new Date(2004, 11, 26), { weekStartsOn: 6 })\n * //=> 2004\n *\n * @example\n * // Which week numbering year is 26 December 2004 if the first week contains 4 January?\n * const result = getWeekYear(new Date(2004, 11, 26), { firstWeekContainsDate: 4 })\n * //=> 2004\n */\nexport function getWeekYear(date, options) {\n  const _date = toDate(date, options?.in);\n  const year = _date.getFullYear();\n\n  const defaultOptions = getDefaultOptions();\n  const firstWeekContainsDate =\n    options?.firstWeekContainsDate ??\n    options?.locale?.options?.firstWeekContainsDate ??\n    defaultOptions.firstWeekContainsDate ??\n    defaultOptions.locale?.options?.firstWeekContainsDate ??\n    1;\n\n  const firstWeekOfNextYear = constructFrom(options?.in || date, 0);\n  firstWeekOfNextYear.setFullYear(year + 1, 0, firstWeekContainsDate);\n  firstWeekOfNextYear.setHours(0, 0, 0, 0);\n  const startOfNextYear = startOfWeek(firstWeekOfNextYear, options);\n\n  const firstWeekOfThisYear = constructFrom(options?.in || date, 0);\n  firstWeekOfThisYear.setFullYear(year, 0, firstWeekContainsDate);\n  firstWeekOfThisYear.setHours(0, 0, 0, 0);\n  const startOfThisYear = startOfWeek(firstWeekOfThisYear, options);\n\n  if (+_date >= +startOfNextYear) {\n    return year + 1;\n  } else if (+_date >= +startOfThisYear) {\n    return year;\n  } else {\n    return year - 1;\n  }\n}\n\n// Fallback for modularized imports:\nexport default getWeekYear;\n", "import { toDate } from \"./toDate.js\";\n\n/**\n * The {@link getMinutes} function options.\n */\n\n/**\n * @name getMinutes\n * @category Minute Helpers\n * @summary Get the minutes of the given date.\n *\n * @description\n * Get the minutes of the given date.\n *\n * @param date - The given date\n * @param options - The options\n *\n * @returns The minutes\n *\n * @example\n * // Get the minutes of 29 February 2012 11:45:05:\n * const result = getMinutes(new Date(2012, 1, 29, 11, 45, 5))\n * //=> 45\n */\nexport function getMinutes(date, options) {\n  return toDate(date, options?.in).getMinutes();\n}\n\n// Fallback for modularized imports:\nexport default getMinutes;\n", "import { normalizeDates } from \"./_lib/normalizeDates.js\";\nimport { getQuarter } from \"./getQuarter.js\";\n\n/**\n * The {@link differenceInCalendarQuarters} function options.\n */\n\n/**\n * @name differenceInCalendarQuarters\n * @category Quarter Helpers\n * @summary Get the number of calendar quarters between the given dates.\n *\n * @description\n * Get the number of calendar quarters between the given dates.\n *\n * @param laterDate - The later date\n * @param earlierDate - The earlier date\n * @param options - An object with options\n *\n * @returns The number of calendar quarters\n *\n * @example\n * // How many calendar quarters are between 31 December 2013 and 2 July 2014?\n * const result = differenceInCalendarQuarters(\n *   new Date(2014, 6, 2),\n *   new Date(2013, 11, 31)\n * )\n * //=> 3\n */\nexport function differenceInCalendarQuarters(laterDate, earlierDate, options) {\n  const [laterDate_, earlierDate_] = normalizeDates(\n    options?.in,\n    laterDate,\n    earlierDate,\n  );\n\n  const yearsDiff = laterDate_.getFullYear() - earlierDate_.getFullYear();\n  const quartersDiff = getQuarter(laterDate_) - getQuarter(earlierDate_);\n\n  return yearsDiff * 4 + quartersDiff;\n}\n\n// Fallback for modularized imports:\nexport default differenceInCalendarQuarters;\n", "import { toDate } from \"./toDate.js\";\n\n/**\n * The {@link getHours} function options.\n */\n\n/**\n * @name getHours\n * @category Hour Helpers\n * @summary Get the hours of the given date.\n *\n * @description\n * Get the hours of the given date.\n *\n * @param date - The given date\n * @param options - An object with options\n *\n * @returns The hours\n *\n * @example\n * // Get the hours of 29 February 2012 11:45:00:\n * const result = getHours(new Date(2012, 1, 29, 11, 45))\n * //=> 11\n */\nexport function getHours(date, options) {\n  return toDate(date, options?.in).getHours();\n}\n\n// Fallback for modularized imports:\nexport default getHours;\n", "import { normalizeDates } from \"./_lib/normalizeDates.js\";\n\n/**\n * The {@link isSameYear} function options.\n */\n\n/**\n * @name isSameYear\n * @category Year Helpers\n * @summary Are the given dates in the same year?\n *\n * @description\n * Are the given dates in the same year?\n *\n * @param laterDate - The first date to check\n * @param earlierDate - The second date to check\n * @param options - An object with options\n *\n * @returns The dates are in the same year\n *\n * @example\n * // Are 2 September 2014 and 25 September 2014 in the same year?\n * const result = isSameYear(new Date(2014, 8, 2), new Date(2014, 8, 25))\n * //=> true\n */\nexport function isSameYear(laterDate, earlierDate, options) {\n  const [laterDate_, earlierDate_] = normalizeDates(\n    options?.in,\n    laterDate,\n    earlierDate,\n  );\n  return laterDate_.getFullYear() === earlierDate_.getFullYear();\n}\n\n// Fallback for modularized imports:\nexport default isSameYear;\n", "import { toDate } from \"./toDate.js\";\n\n/**\n * The {@link startOfYear} function options.\n */\n\n/**\n * @name startOfYear\n * @category Year Helpers\n * @summary Return the start of a year for the given date.\n *\n * @description\n * Return the start of a year for the given date.\n * The result will be in the local timezone.\n *\n * @typeParam DateType - The `Date` type, the function operates on. Gets inferred from passed arguments. Allows to use extensions like [`UTCDate`](https://github.com/date-fns/utc).\n * @typeParam ResultDate - The result `Date` type, it is the type returned from the context function if it is passed, or inferred from the arguments.\n *\n * @param date - The original date\n * @param options - The options\n *\n * @returns The start of a year\n *\n * @example\n * // The start of a year for 2 September 2014 11:55:00:\n * const result = startOfYear(new Date(2014, 8, 2, 11, 55, 00))\n * //=> Wed Jan 01 2014 00:00:00\n */\nexport function startOfYear(date, options) {\n  const date_ = toDate(date, options?.in);\n  date_.setFullYear(date_.getFullYear(), 0, 1);\n  date_.setHours(0, 0, 0, 0);\n  return date_;\n}\n\n// Fallback for modularized imports:\nexport default startOfYear;\n", "const formatDistanceLocale = {\n  lessThanXSeconds: {\n    one: \"less than a second\",\n    other: \"less than {{count}} seconds\",\n  },\n\n  xSeconds: {\n    one: \"1 second\",\n    other: \"{{count}} seconds\",\n  },\n\n  halfAMinute: \"half a minute\",\n\n  lessThanXMinutes: {\n    one: \"less than a minute\",\n    other: \"less than {{count}} minutes\",\n  },\n\n  xMinutes: {\n    one: \"1 minute\",\n    other: \"{{count}} minutes\",\n  },\n\n  aboutXHours: {\n    one: \"about 1 hour\",\n    other: \"about {{count}} hours\",\n  },\n\n  xHours: {\n    one: \"1 hour\",\n    other: \"{{count}} hours\",\n  },\n\n  xDays: {\n    one: \"1 day\",\n    other: \"{{count}} days\",\n  },\n\n  aboutXWeeks: {\n    one: \"about 1 week\",\n    other: \"about {{count}} weeks\",\n  },\n\n  xWeeks: {\n    one: \"1 week\",\n    other: \"{{count}} weeks\",\n  },\n\n  aboutXMonths: {\n    one: \"about 1 month\",\n    other: \"about {{count}} months\",\n  },\n\n  xMonths: {\n    one: \"1 month\",\n    other: \"{{count}} months\",\n  },\n\n  aboutXYears: {\n    one: \"about 1 year\",\n    other: \"about {{count}} years\",\n  },\n\n  xYears: {\n    one: \"1 year\",\n    other: \"{{count}} years\",\n  },\n\n  overXYears: {\n    one: \"over 1 year\",\n    other: \"over {{count}} years\",\n  },\n\n  almostXYears: {\n    one: \"almost 1 year\",\n    other: \"almost {{count}} years\",\n  },\n};\n\nexport const formatDistance = (token, count, options) => {\n  let result;\n\n  const tokenValue = formatDistanceLocale[token];\n  if (typeof tokenValue === \"string\") {\n    result = tokenValue;\n  } else if (count === 1) {\n    result = tokenValue.one;\n  } else {\n    result = tokenValue.other.replace(\"{{count}}\", count.toString());\n  }\n\n  if (options?.addSuffix) {\n    if (options.comparison && options.comparison > 0) {\n      return \"in \" + result;\n    } else {\n      return result + \" ago\";\n    }\n  }\n\n  return result;\n};\n", "export function buildFormatLongFn(args) {\n  return (options = {}) => {\n    // TODO: Remove String()\n    const width = options.width ? String(options.width) : args.defaultWidth;\n    const format = args.formats[width] || args.formats[args.defaultWidth];\n    return format;\n  };\n}\n", "import { buildFormatLongFn } from \"../../_lib/buildFormatLongFn.js\";\n\nconst dateFormats = {\n  full: \"EEEE, MMMM do, y\",\n  long: \"MMMM do, y\",\n  medium: \"MMM d, y\",\n  short: \"MM/dd/yyyy\",\n};\n\nconst timeFormats = {\n  full: \"h:mm:ss a zzzz\",\n  long: \"h:mm:ss a z\",\n  medium: \"h:mm:ss a\",\n  short: \"h:mm a\",\n};\n\nconst dateTimeFormats = {\n  full: \"{{date}} 'at' {{time}}\",\n  long: \"{{date}} 'at' {{time}}\",\n  medium: \"{{date}}, {{time}}\",\n  short: \"{{date}}, {{time}}\",\n};\n\nexport const formatLong = {\n  date: buildFormatLongFn({\n    formats: dateFormats,\n    defaultWidth: \"full\",\n  }),\n\n  time: buildFormatLongFn({\n    formats: timeFormats,\n    defaultWidth: \"full\",\n  }),\n\n  dateTime: buildFormatLongFn({\n    formats: dateTimeFormats,\n    defaultWidth: \"full\",\n  }),\n};\n", "const formatRelativeLocale = {\n  lastWeek: \"'last' eeee 'at' p\",\n  yesterday: \"'yesterday at' p\",\n  today: \"'today at' p\",\n  tomorrow: \"'tomorrow at' p\",\n  nextWeek: \"eeee 'at' p\",\n  other: \"P\",\n};\n\nexport const formatRelative = (token, _date, _baseDate, _options) =>\n  formatRelativeLocale[token];\n", "/**\n * The localize function argument callback which allows to convert raw value to\n * the actual type.\n *\n * @param value - The value to convert\n *\n * @returns The converted value\n */\n\n/**\n * The map of localized values for each width.\n */\n\n/**\n * The index type of the locale unit value. It types conversion of units of\n * values that don't start at 0 (i.e. quarters).\n */\n\n/**\n * Converts the unit value to the tuple of values.\n */\n\n/**\n * The tuple of localized era values. The first element represents BC,\n * the second element represents AD.\n */\n\n/**\n * The tuple of localized quarter values. The first element represents Q1.\n */\n\n/**\n * The tuple of localized day values. The first element represents Sunday.\n */\n\n/**\n * The tuple of localized month values. The first element represents January.\n */\n\nexport function buildLocalizeFn(args) {\n  return (value, options) => {\n    const context = options?.context ? String(options.context) : \"standalone\";\n\n    let valuesArray;\n    if (context === \"formatting\" && args.formattingValues) {\n      const defaultWidth = args.defaultFormattingWidth || args.defaultWidth;\n      const width = options?.width ? String(options.width) : defaultWidth;\n\n      valuesArray =\n        args.formattingValues[width] || args.formattingValues[defaultWidth];\n    } else {\n      const defaultWidth = args.defaultWidth;\n      const width = options?.width ? String(options.width) : args.defaultWidth;\n\n      valuesArray = args.values[width] || args.values[defaultWidth];\n    }\n    const index = args.argumentCallback ? args.argumentCallback(value) : value;\n\n    // @ts-expect-error - For some reason TypeScript just don't want to match it, no matter how hard we try. I challenge you to try to remove it!\n    return valuesArray[index];\n  };\n}\n", "import { buildLocalizeFn } from \"../../_lib/buildLocalizeFn.js\";\n\nconst eraValues = {\n  narrow: [\"B\", \"A\"],\n  abbreviated: [\"BC\", \"AD\"],\n  wide: [\"Before Christ\", \"Ann<PERSON> Domini\"],\n};\n\nconst quarterValues = {\n  narrow: [\"1\", \"2\", \"3\", \"4\"],\n  abbreviated: [\"Q1\", \"Q2\", \"Q3\", \"Q4\"],\n  wide: [\"1st quarter\", \"2nd quarter\", \"3rd quarter\", \"4th quarter\"],\n};\n\n// Note: in English, the names of days of the week and months are capitalized.\n// If you are making a new locale based on this one, check if the same is true for the language you're working on.\n// Generally, formatted dates should look like they are in the middle of a sentence,\n// e.g. in Spanish language the weekdays and months should be in the lowercase.\nconst monthValues = {\n  narrow: [\"J\", \"F\", \"M\", \"A\", \"M\", \"J\", \"J\", \"A\", \"S\", \"O\", \"N\", \"D\"],\n  abbreviated: [\n    \"Jan\",\n    \"Feb\",\n    \"Mar\",\n    \"Apr\",\n    \"May\",\n    \"Jun\",\n    \"Jul\",\n    \"Aug\",\n    \"Sep\",\n    \"Oct\",\n    \"Nov\",\n    \"Dec\",\n  ],\n\n  wide: [\n    \"January\",\n    \"February\",\n    \"March\",\n    \"April\",\n    \"May\",\n    \"June\",\n    \"July\",\n    \"August\",\n    \"September\",\n    \"October\",\n    \"November\",\n    \"December\",\n  ],\n};\n\nconst dayValues = {\n  narrow: [\"S\", \"M\", \"T\", \"W\", \"T\", \"F\", \"S\"],\n  short: [\"Su\", \"Mo\", \"Tu\", \"We\", \"Th\", \"Fr\", \"Sa\"],\n  abbreviated: [\"Sun\", \"Mon\", \"Tue\", \"Wed\", \"Thu\", \"Fri\", \"Sat\"],\n  wide: [\n    \"Sunday\",\n    \"Monday\",\n    \"Tuesday\",\n    \"Wednesday\",\n    \"Thursday\",\n    \"Friday\",\n    \"Saturday\",\n  ],\n};\n\nconst dayPeriodValues = {\n  narrow: {\n    am: \"a\",\n    pm: \"p\",\n    midnight: \"mi\",\n    noon: \"n\",\n    morning: \"morning\",\n    afternoon: \"afternoon\",\n    evening: \"evening\",\n    night: \"night\",\n  },\n  abbreviated: {\n    am: \"AM\",\n    pm: \"PM\",\n    midnight: \"midnight\",\n    noon: \"noon\",\n    morning: \"morning\",\n    afternoon: \"afternoon\",\n    evening: \"evening\",\n    night: \"night\",\n  },\n  wide: {\n    am: \"a.m.\",\n    pm: \"p.m.\",\n    midnight: \"midnight\",\n    noon: \"noon\",\n    morning: \"morning\",\n    afternoon: \"afternoon\",\n    evening: \"evening\",\n    night: \"night\",\n  },\n};\n\nconst formattingDayPeriodValues = {\n  narrow: {\n    am: \"a\",\n    pm: \"p\",\n    midnight: \"mi\",\n    noon: \"n\",\n    morning: \"in the morning\",\n    afternoon: \"in the afternoon\",\n    evening: \"in the evening\",\n    night: \"at night\",\n  },\n  abbreviated: {\n    am: \"AM\",\n    pm: \"PM\",\n    midnight: \"midnight\",\n    noon: \"noon\",\n    morning: \"in the morning\",\n    afternoon: \"in the afternoon\",\n    evening: \"in the evening\",\n    night: \"at night\",\n  },\n  wide: {\n    am: \"a.m.\",\n    pm: \"p.m.\",\n    midnight: \"midnight\",\n    noon: \"noon\",\n    morning: \"in the morning\",\n    afternoon: \"in the afternoon\",\n    evening: \"in the evening\",\n    night: \"at night\",\n  },\n};\n\nconst ordinalNumber = (dirtyNumber, _options) => {\n  const number = Number(dirtyNumber);\n\n  // If ordinal numbers depend on context, for example,\n  // if they are different for different grammatical genders,\n  // use `options.unit`.\n  //\n  // `unit` can be 'year', 'quarter', 'month', 'week', 'date', 'dayOfYear',\n  // 'day', 'hour', 'minute', 'second'.\n\n  const rem100 = number % 100;\n  if (rem100 > 20 || rem100 < 10) {\n    switch (rem100 % 10) {\n      case 1:\n        return number + \"st\";\n      case 2:\n        return number + \"nd\";\n      case 3:\n        return number + \"rd\";\n    }\n  }\n  return number + \"th\";\n};\n\nexport const localize = {\n  ordinalNumber,\n\n  era: buildLocalizeFn({\n    values: eraValues,\n    defaultWidth: \"wide\",\n  }),\n\n  quarter: buildLocalizeFn({\n    values: quarterValues,\n    defaultWidth: \"wide\",\n    argumentCallback: (quarter) => quarter - 1,\n  }),\n\n  month: buildLocalizeFn({\n    values: monthValues,\n    defaultWidth: \"wide\",\n  }),\n\n  day: buildLocalizeFn({\n    values: dayValues,\n    defaultWidth: \"wide\",\n  }),\n\n  dayPeriod: buildLocalizeFn({\n    values: dayPeriodValues,\n    defaultWidth: \"wide\",\n    formattingValues: formattingDayPeriodValues,\n    defaultFormattingWidth: \"wide\",\n  }),\n};\n", "export function buildMatchFn(args) {\n  return (string, options = {}) => {\n    const width = options.width;\n\n    const matchPattern =\n      (width && args.matchPatterns[width]) ||\n      args.matchPatterns[args.defaultMatchWidth];\n    const matchResult = string.match(matchPattern);\n\n    if (!matchResult) {\n      return null;\n    }\n    const matchedString = matchResult[0];\n\n    const parsePatterns =\n      (width && args.parsePatterns[width]) ||\n      args.parsePatterns[args.defaultParseWidth];\n\n    const key = Array.isArray(parsePatterns)\n      ? findIndex(parsePatterns, (pattern) => pattern.test(matchedString))\n      : // [TODO] -- I challenge you to fix the type\n        findKey(parsePatterns, (pattern) => pattern.test(matchedString));\n\n    let value;\n\n    value = args.valueCallback ? args.valueCallback(key) : key;\n    value = options.valueCallback\n      ? // [TODO] -- I challenge you to fix the type\n        options.valueCallback(value)\n      : value;\n\n    const rest = string.slice(matchedString.length);\n\n    return { value, rest };\n  };\n}\n\nfunction findKey(object, predicate) {\n  for (const key in object) {\n    if (\n      Object.prototype.hasOwnProperty.call(object, key) &&\n      predicate(object[key])\n    ) {\n      return key;\n    }\n  }\n  return undefined;\n}\n\nfunction findIndex(array, predicate) {\n  for (let key = 0; key < array.length; key++) {\n    if (predicate(array[key])) {\n      return key;\n    }\n  }\n  return undefined;\n}\n", "export function buildMatchPatternFn(args) {\n  return (string, options = {}) => {\n    const matchResult = string.match(args.matchPattern);\n    if (!matchResult) return null;\n    const matchedString = matchResult[0];\n\n    const parseResult = string.match(args.parsePattern);\n    if (!parseResult) return null;\n    let value = args.valueCallback\n      ? args.valueCallback(parseResult[0])\n      : parseResult[0];\n\n    // [TODO] I challenge you to fix the type\n    value = options.valueCallback ? options.valueCallback(value) : value;\n\n    const rest = string.slice(matchedString.length);\n\n    return { value, rest };\n  };\n}\n", "import { buildMatchFn } from \"../../_lib/buildMatchFn.js\";\nimport { buildMatchPatternFn } from \"../../_lib/buildMatchPatternFn.js\";\n\nconst matchOrdinalNumberPattern = /^(\\d+)(th|st|nd|rd)?/i;\nconst parseOrdinalNumberPattern = /\\d+/i;\n\nconst matchEraPatterns = {\n  narrow: /^(b|a)/i,\n  abbreviated: /^(b\\.?\\s?c\\.?|b\\.?\\s?c\\.?\\s?e\\.?|a\\.?\\s?d\\.?|c\\.?\\s?e\\.?)/i,\n  wide: /^(before christ|before common era|anno domini|common era)/i,\n};\nconst parseEraPatterns = {\n  any: [/^b/i, /^(a|c)/i],\n};\n\nconst matchQuarterPatterns = {\n  narrow: /^[1234]/i,\n  abbreviated: /^q[1234]/i,\n  wide: /^[1234](th|st|nd|rd)? quarter/i,\n};\nconst parseQuarterPatterns = {\n  any: [/1/i, /2/i, /3/i, /4/i],\n};\n\nconst matchMonthPatterns = {\n  narrow: /^[jfmasond]/i,\n  abbreviated: /^(jan|feb|mar|apr|may|jun|jul|aug|sep|oct|nov|dec)/i,\n  wide: /^(january|february|march|april|may|june|july|august|september|october|november|december)/i,\n};\nconst parseMonthPatterns = {\n  narrow: [\n    /^j/i,\n    /^f/i,\n    /^m/i,\n    /^a/i,\n    /^m/i,\n    /^j/i,\n    /^j/i,\n    /^a/i,\n    /^s/i,\n    /^o/i,\n    /^n/i,\n    /^d/i,\n  ],\n\n  any: [\n    /^ja/i,\n    /^f/i,\n    /^mar/i,\n    /^ap/i,\n    /^may/i,\n    /^jun/i,\n    /^jul/i,\n    /^au/i,\n    /^s/i,\n    /^o/i,\n    /^n/i,\n    /^d/i,\n  ],\n};\n\nconst matchDayPatterns = {\n  narrow: /^[smtwf]/i,\n  short: /^(su|mo|tu|we|th|fr|sa)/i,\n  abbreviated: /^(sun|mon|tue|wed|thu|fri|sat)/i,\n  wide: /^(sunday|monday|tuesday|wednesday|thursday|friday|saturday)/i,\n};\nconst parseDayPatterns = {\n  narrow: [/^s/i, /^m/i, /^t/i, /^w/i, /^t/i, /^f/i, /^s/i],\n  any: [/^su/i, /^m/i, /^tu/i, /^w/i, /^th/i, /^f/i, /^sa/i],\n};\n\nconst matchDayPeriodPatterns = {\n  narrow: /^(a|p|mi|n|(in the|at) (morning|afternoon|evening|night))/i,\n  any: /^([ap]\\.?\\s?m\\.?|midnight|noon|(in the|at) (morning|afternoon|evening|night))/i,\n};\nconst parseDayPeriodPatterns = {\n  any: {\n    am: /^a/i,\n    pm: /^p/i,\n    midnight: /^mi/i,\n    noon: /^no/i,\n    morning: /morning/i,\n    afternoon: /afternoon/i,\n    evening: /evening/i,\n    night: /night/i,\n  },\n};\n\nexport const match = {\n  ordinalNumber: buildMatchPatternFn({\n    matchPattern: matchOrdinalNumberPattern,\n    parsePattern: parseOrdinalNumberPattern,\n    valueCallback: (value) => parseInt(value, 10),\n  }),\n\n  era: buildMatchFn({\n    matchPatterns: matchEraPatterns,\n    defaultMatchWidth: \"wide\",\n    parsePatterns: parseEraPatterns,\n    defaultParseWidth: \"any\",\n  }),\n\n  quarter: buildMatchFn({\n    matchPatterns: matchQuarterPatterns,\n    defaultMatchWidth: \"wide\",\n    parsePatterns: parseQuarterPatterns,\n    defaultParseWidth: \"any\",\n    valueCallback: (index) => index + 1,\n  }),\n\n  month: buildMatchFn({\n    matchPatterns: matchMonthPatterns,\n    defaultMatchWidth: \"wide\",\n    parsePatterns: parseMonthPatterns,\n    defaultParseWidth: \"any\",\n  }),\n\n  day: buildMatchFn({\n    matchPatterns: matchDayPatterns,\n    defaultMatchWidth: \"wide\",\n    parsePatterns: parseDayPatterns,\n    defaultParseWidth: \"any\",\n  }),\n\n  dayPeriod: buildMatchFn({\n    matchPatterns: matchDayPeriodPatterns,\n    defaultMatchWidth: \"any\",\n    parsePatterns: parseDayPeriodPatterns,\n    defaultParseWidth: \"any\",\n  }),\n};\n", "import { formatDistance } from \"./en-US/_lib/formatDistance.js\";\nimport { formatLong } from \"./en-US/_lib/formatLong.js\";\nimport { formatRelative } from \"./en-US/_lib/formatRelative.js\";\nimport { localize } from \"./en-US/_lib/localize.js\";\nimport { match } from \"./en-US/_lib/match.js\";\n\n/**\n * @category Locales\n * @summary English locale (United States).\n * @language English\n * @iso-639-2 eng\n * <AUTHOR> [@kossnocorp](https://github.com/kossnocorp)\n * <AUTHOR> [@leshakoss](https://github.com/leshakoss)\n */\nexport const enUS = {\n  code: \"en-US\",\n  formatDistance: formatDistance,\n  formatLong: formatLong,\n  formatRelative: formatRelative,\n  localize: localize,\n  match: match,\n  options: {\n    weekStartsOn: 0 /* Sunday */,\n    firstWeekContainsDate: 1,\n  },\n};\n\n// Fallback for modularized imports:\nexport default enUS;\n", "import { addYears } from \"./addYears.js\";\n\n/**\n * The {@link subYears} function options.\n */\n\n/**\n * @name subYears\n * @category Year Helpers\n * @summary Subtract the specified number of years from the given date.\n *\n * @description\n * Subtract the specified number of years from the given date.\n *\n * @typeParam DateType - The `Date` type, the function operates on. Gets inferred from passed arguments. Allows to use extensions like [`UTCDate`](https://github.com/date-fns/utc).\n * @typeParam ResultDate - The result `Date` type, it is the type returned from the context function if it is passed, or inferred from the arguments.\n *\n * @param date - The date to be changed\n * @param amount - The amount of years to be subtracted.\n * @param options - An object with options\n *\n * @returns The new date with the years subtracted\n *\n * @example\n * // Subtract 5 years from 1 September 2014:\n * const result = subYears(new Date(2014, 8, 1), 5)\n * //=> Tue Sep 01 2009 00:00:00\n */\nexport function subYears(date, amount, options) {\n  return addYears(date, -amount, options);\n}\n\n// Fallback for modularized imports:\nexport default subYears;\n", "import { isShadowRoot, isHTMLElement } from '@floating-ui/utils/dom';\nimport * as React from 'react';\nimport { useLayoutEffect, useEffect } from 'react';\nimport { floor } from '@floating-ui/utils';\nimport { tabbable } from 'tabbable';\n\n// Avoid Chrome DevTools blue warning.\nfunction getPlatform() {\n  const uaData = navigator.userAgentData;\n  if (uaData != null && uaData.platform) {\n    return uaData.platform;\n  }\n  return navigator.platform;\n}\nfunction getUserAgent() {\n  const uaData = navigator.userAgentData;\n  if (uaData && Array.isArray(uaData.brands)) {\n    return uaData.brands.map(_ref => {\n      let {\n        brand,\n        version\n      } = _ref;\n      return brand + \"/\" + version;\n    }).join(' ');\n  }\n  return navigator.userAgent;\n}\nfunction isSafari() {\n  // Chrome DevTools does not complain about navigator.vendor\n  return /apple/i.test(navigator.vendor);\n}\nfunction isAndroid() {\n  const re = /android/i;\n  return re.test(getPlatform()) || re.test(getUserAgent());\n}\nfunction isMac() {\n  return getPlatform().toLowerCase().startsWith('mac') && !navigator.maxTouchPoints;\n}\nfunction isJSDOM() {\n  return getUserAgent().includes('jsdom/');\n}\n\nconst FOCUSABLE_ATTRIBUTE = 'data-floating-ui-focusable';\nconst TYPEABLE_SELECTOR = \"input:not([type='hidden']):not([disabled]),\" + \"[contenteditable]:not([contenteditable='false']),textarea:not([disabled])\";\nconst ARROW_LEFT = 'ArrowLeft';\nconst ARROW_RIGHT = 'ArrowRight';\nconst ARROW_UP = 'ArrowUp';\nconst ARROW_DOWN = 'ArrowDown';\n\nfunction activeElement(doc) {\n  let activeElement = doc.activeElement;\n  while (((_activeElement = activeElement) == null || (_activeElement = _activeElement.shadowRoot) == null ? void 0 : _activeElement.activeElement) != null) {\n    var _activeElement;\n    activeElement = activeElement.shadowRoot.activeElement;\n  }\n  return activeElement;\n}\nfunction contains(parent, child) {\n  if (!parent || !child) {\n    return false;\n  }\n  const rootNode = child.getRootNode == null ? void 0 : child.getRootNode();\n\n  // First, attempt with faster native method\n  if (parent.contains(child)) {\n    return true;\n  }\n\n  // then fallback to custom implementation with Shadow DOM support\n  if (rootNode && isShadowRoot(rootNode)) {\n    let next = child;\n    while (next) {\n      if (parent === next) {\n        return true;\n      }\n      // @ts-ignore\n      next = next.parentNode || next.host;\n    }\n  }\n\n  // Give up, the result is false\n  return false;\n}\nfunction getTarget(event) {\n  if ('composedPath' in event) {\n    return event.composedPath()[0];\n  }\n\n  // TS thinks `event` is of type never as it assumes all browsers support\n  // `composedPath()`, but browsers without shadow DOM don't.\n  return event.target;\n}\nfunction isEventTargetWithin(event, node) {\n  if (node == null) {\n    return false;\n  }\n  if ('composedPath' in event) {\n    return event.composedPath().includes(node);\n  }\n\n  // TS thinks `event` is of type never as it assumes all browsers support composedPath, but browsers without shadow dom don't\n  const e = event;\n  return e.target != null && node.contains(e.target);\n}\nfunction isRootElement(element) {\n  return element.matches('html,body');\n}\nfunction getDocument(node) {\n  return (node == null ? void 0 : node.ownerDocument) || document;\n}\nfunction isTypeableElement(element) {\n  return isHTMLElement(element) && element.matches(TYPEABLE_SELECTOR);\n}\nfunction isTypeableCombobox(element) {\n  if (!element) return false;\n  return element.getAttribute('role') === 'combobox' && isTypeableElement(element);\n}\nfunction matchesFocusVisible(element) {\n  // We don't want to block focus from working with `visibleOnly`\n  // (JSDOM doesn't match `:focus-visible` when the element has `:focus`)\n  if (!element || isJSDOM()) return true;\n  try {\n    return element.matches(':focus-visible');\n  } catch (_e) {\n    return true;\n  }\n}\nfunction getFloatingFocusElement(floatingElement) {\n  if (!floatingElement) {\n    return null;\n  }\n  // Try to find the element that has `{...getFloatingProps()}` spread on it.\n  // This indicates the floating element is acting as a positioning wrapper, and\n  // so focus should be managed on the child element with the event handlers and\n  // aria props.\n  return floatingElement.hasAttribute(FOCUSABLE_ATTRIBUTE) ? floatingElement : floatingElement.querySelector(\"[\" + FOCUSABLE_ATTRIBUTE + \"]\") || floatingElement;\n}\n\nfunction getNodeChildren(nodes, id, onlyOpenChildren) {\n  if (onlyOpenChildren === void 0) {\n    onlyOpenChildren = true;\n  }\n  let allChildren = nodes.filter(node => {\n    var _node$context;\n    return node.parentId === id && ((_node$context = node.context) == null ? void 0 : _node$context.open);\n  });\n  let currentChildren = allChildren;\n  while (currentChildren.length) {\n    currentChildren = onlyOpenChildren ? nodes.filter(node => {\n      var _currentChildren;\n      return (_currentChildren = currentChildren) == null ? void 0 : _currentChildren.some(n => {\n        var _node$context2;\n        return node.parentId === n.id && ((_node$context2 = node.context) == null ? void 0 : _node$context2.open);\n      });\n    }) : nodes;\n    allChildren = allChildren.concat(currentChildren);\n  }\n  return allChildren;\n}\nfunction getDeepestNode(nodes, id) {\n  let deepestNodeId;\n  let maxDepth = -1;\n  function findDeepest(nodeId, depth) {\n    if (depth > maxDepth) {\n      deepestNodeId = nodeId;\n      maxDepth = depth;\n    }\n    const children = getNodeChildren(nodes, nodeId);\n    children.forEach(child => {\n      findDeepest(child.id, depth + 1);\n    });\n  }\n  findDeepest(id, 0);\n  return nodes.find(node => node.id === deepestNodeId);\n}\nfunction getNodeAncestors(nodes, id) {\n  var _nodes$find;\n  let allAncestors = [];\n  let currentParentId = (_nodes$find = nodes.find(node => node.id === id)) == null ? void 0 : _nodes$find.parentId;\n  while (currentParentId) {\n    const currentNode = nodes.find(node => node.id === currentParentId);\n    currentParentId = currentNode == null ? void 0 : currentNode.parentId;\n    if (currentNode) {\n      allAncestors = allAncestors.concat(currentNode);\n    }\n  }\n  return allAncestors;\n}\n\nfunction stopEvent(event) {\n  event.preventDefault();\n  event.stopPropagation();\n}\nfunction isReactEvent(event) {\n  return 'nativeEvent' in event;\n}\n\n// License: https://github.com/adobe/react-spectrum/blob/b35d5c02fe900badccd0cf1a8f23bb593419f238/packages/@react-aria/utils/src/isVirtualEvent.ts\nfunction isVirtualClick(event) {\n  // FIXME: Firefox is now emitting a deprecation warning for `mozInputSource`.\n  // Try to find a workaround for this. `react-aria` source still has the check.\n  if (event.mozInputSource === 0 && event.isTrusted) {\n    return true;\n  }\n  if (isAndroid() && event.pointerType) {\n    return event.type === 'click' && event.buttons === 1;\n  }\n  return event.detail === 0 && !event.pointerType;\n}\nfunction isVirtualPointerEvent(event) {\n  if (isJSDOM()) return false;\n  return !isAndroid() && event.width === 0 && event.height === 0 || isAndroid() && event.width === 1 && event.height === 1 && event.pressure === 0 && event.detail === 0 && event.pointerType === 'mouse' ||\n  // iOS VoiceOver returns 0.333• for width/height.\n  event.width < 1 && event.height < 1 && event.pressure === 0 && event.detail === 0 && event.pointerType === 'touch';\n}\nfunction isMouseLikePointerType(pointerType, strict) {\n  // On some Linux machines with Chromium, mouse inputs return a `pointerType`\n  // of \"pen\": https://github.com/floating-ui/floating-ui/issues/2015\n  const values = ['mouse', 'pen'];\n  if (!strict) {\n    values.push('', undefined);\n  }\n  return values.includes(pointerType);\n}\n\nvar index = typeof document !== 'undefined' ? useLayoutEffect : useEffect;\n\n// https://github.com/mui/material-ui/issues/41190#issuecomment-2040873379\nconst SafeReact = {\n  ...React\n};\n\nfunction useLatestRef(value) {\n  const ref = React.useRef(value);\n  index(() => {\n    ref.current = value;\n  });\n  return ref;\n}\nconst useInsertionEffect = SafeReact.useInsertionEffect;\nconst useSafeInsertionEffect = useInsertionEffect || (fn => fn());\nfunction useEffectEvent(callback) {\n  const ref = React.useRef(() => {\n    if (process.env.NODE_ENV !== \"production\") {\n      throw new Error('Cannot call an event handler while rendering.');\n    }\n  });\n  useSafeInsertionEffect(() => {\n    ref.current = callback;\n  });\n  return React.useCallback(function () {\n    for (var _len = arguments.length, args = new Array(_len), _key = 0; _key < _len; _key++) {\n      args[_key] = arguments[_key];\n    }\n    return ref.current == null ? void 0 : ref.current(...args);\n  }, []);\n}\n\nfunction isDifferentGridRow(index, cols, prevRow) {\n  return Math.floor(index / cols) !== prevRow;\n}\nfunction isIndexOutOfListBounds(listRef, index) {\n  return index < 0 || index >= listRef.current.length;\n}\nfunction getMinListIndex(listRef, disabledIndices) {\n  return findNonDisabledListIndex(listRef, {\n    disabledIndices\n  });\n}\nfunction getMaxListIndex(listRef, disabledIndices) {\n  return findNonDisabledListIndex(listRef, {\n    decrement: true,\n    startingIndex: listRef.current.length,\n    disabledIndices\n  });\n}\nfunction findNonDisabledListIndex(listRef, _temp) {\n  let {\n    startingIndex = -1,\n    decrement = false,\n    disabledIndices,\n    amount = 1\n  } = _temp === void 0 ? {} : _temp;\n  let index = startingIndex;\n  do {\n    index += decrement ? -amount : amount;\n  } while (index >= 0 && index <= listRef.current.length - 1 && isListIndexDisabled(listRef, index, disabledIndices));\n  return index;\n}\nfunction getGridNavigatedIndex(listRef, _ref) {\n  let {\n    event,\n    orientation,\n    loop,\n    rtl,\n    cols,\n    disabledIndices,\n    minIndex,\n    maxIndex,\n    prevIndex,\n    stopEvent: stop = false\n  } = _ref;\n  let nextIndex = prevIndex;\n  if (event.key === ARROW_UP) {\n    stop && stopEvent(event);\n    if (prevIndex === -1) {\n      nextIndex = maxIndex;\n    } else {\n      nextIndex = findNonDisabledListIndex(listRef, {\n        startingIndex: nextIndex,\n        amount: cols,\n        decrement: true,\n        disabledIndices\n      });\n      if (loop && (prevIndex - cols < minIndex || nextIndex < 0)) {\n        const col = prevIndex % cols;\n        const maxCol = maxIndex % cols;\n        const offset = maxIndex - (maxCol - col);\n        if (maxCol === col) {\n          nextIndex = maxIndex;\n        } else {\n          nextIndex = maxCol > col ? offset : offset - cols;\n        }\n      }\n    }\n    if (isIndexOutOfListBounds(listRef, nextIndex)) {\n      nextIndex = prevIndex;\n    }\n  }\n  if (event.key === ARROW_DOWN) {\n    stop && stopEvent(event);\n    if (prevIndex === -1) {\n      nextIndex = minIndex;\n    } else {\n      nextIndex = findNonDisabledListIndex(listRef, {\n        startingIndex: prevIndex,\n        amount: cols,\n        disabledIndices\n      });\n      if (loop && prevIndex + cols > maxIndex) {\n        nextIndex = findNonDisabledListIndex(listRef, {\n          startingIndex: prevIndex % cols - cols,\n          amount: cols,\n          disabledIndices\n        });\n      }\n    }\n    if (isIndexOutOfListBounds(listRef, nextIndex)) {\n      nextIndex = prevIndex;\n    }\n  }\n\n  // Remains on the same row/column.\n  if (orientation === 'both') {\n    const prevRow = floor(prevIndex / cols);\n    if (event.key === (rtl ? ARROW_LEFT : ARROW_RIGHT)) {\n      stop && stopEvent(event);\n      if (prevIndex % cols !== cols - 1) {\n        nextIndex = findNonDisabledListIndex(listRef, {\n          startingIndex: prevIndex,\n          disabledIndices\n        });\n        if (loop && isDifferentGridRow(nextIndex, cols, prevRow)) {\n          nextIndex = findNonDisabledListIndex(listRef, {\n            startingIndex: prevIndex - prevIndex % cols - 1,\n            disabledIndices\n          });\n        }\n      } else if (loop) {\n        nextIndex = findNonDisabledListIndex(listRef, {\n          startingIndex: prevIndex - prevIndex % cols - 1,\n          disabledIndices\n        });\n      }\n      if (isDifferentGridRow(nextIndex, cols, prevRow)) {\n        nextIndex = prevIndex;\n      }\n    }\n    if (event.key === (rtl ? ARROW_RIGHT : ARROW_LEFT)) {\n      stop && stopEvent(event);\n      if (prevIndex % cols !== 0) {\n        nextIndex = findNonDisabledListIndex(listRef, {\n          startingIndex: prevIndex,\n          decrement: true,\n          disabledIndices\n        });\n        if (loop && isDifferentGridRow(nextIndex, cols, prevRow)) {\n          nextIndex = findNonDisabledListIndex(listRef, {\n            startingIndex: prevIndex + (cols - prevIndex % cols),\n            decrement: true,\n            disabledIndices\n          });\n        }\n      } else if (loop) {\n        nextIndex = findNonDisabledListIndex(listRef, {\n          startingIndex: prevIndex + (cols - prevIndex % cols),\n          decrement: true,\n          disabledIndices\n        });\n      }\n      if (isDifferentGridRow(nextIndex, cols, prevRow)) {\n        nextIndex = prevIndex;\n      }\n    }\n    const lastRow = floor(maxIndex / cols) === prevRow;\n    if (isIndexOutOfListBounds(listRef, nextIndex)) {\n      if (loop && lastRow) {\n        nextIndex = event.key === (rtl ? ARROW_RIGHT : ARROW_LEFT) ? maxIndex : findNonDisabledListIndex(listRef, {\n          startingIndex: prevIndex - prevIndex % cols - 1,\n          disabledIndices\n        });\n      } else {\n        nextIndex = prevIndex;\n      }\n    }\n  }\n  return nextIndex;\n}\n\n/** For each cell index, gets the item index that occupies that cell */\nfunction createGridCellMap(sizes, cols, dense) {\n  const cellMap = [];\n  let startIndex = 0;\n  sizes.forEach((_ref2, index) => {\n    let {\n      width,\n      height\n    } = _ref2;\n    if (width > cols) {\n      if (process.env.NODE_ENV !== \"production\") {\n        throw new Error(\"[Floating UI]: Invalid grid - item width at index \" + index + \" is greater than grid columns\");\n      }\n    }\n    let itemPlaced = false;\n    if (dense) {\n      startIndex = 0;\n    }\n    while (!itemPlaced) {\n      const targetCells = [];\n      for (let i = 0; i < width; i++) {\n        for (let j = 0; j < height; j++) {\n          targetCells.push(startIndex + i + j * cols);\n        }\n      }\n      if (startIndex % cols + width <= cols && targetCells.every(cell => cellMap[cell] == null)) {\n        targetCells.forEach(cell => {\n          cellMap[cell] = index;\n        });\n        itemPlaced = true;\n      } else {\n        startIndex++;\n      }\n    }\n  });\n\n  // convert into a non-sparse array\n  return [...cellMap];\n}\n\n/** Gets cell index of an item's corner or -1 when index is -1. */\nfunction getGridCellIndexOfCorner(index, sizes, cellMap, cols, corner) {\n  if (index === -1) return -1;\n  const firstCellIndex = cellMap.indexOf(index);\n  const sizeItem = sizes[index];\n  switch (corner) {\n    case 'tl':\n      return firstCellIndex;\n    case 'tr':\n      if (!sizeItem) {\n        return firstCellIndex;\n      }\n      return firstCellIndex + sizeItem.width - 1;\n    case 'bl':\n      if (!sizeItem) {\n        return firstCellIndex;\n      }\n      return firstCellIndex + (sizeItem.height - 1) * cols;\n    case 'br':\n      return cellMap.lastIndexOf(index);\n  }\n}\n\n/** Gets all cell indices that correspond to the specified indices */\nfunction getGridCellIndices(indices, cellMap) {\n  return cellMap.flatMap((index, cellIndex) => indices.includes(index) ? [cellIndex] : []);\n}\nfunction isListIndexDisabled(listRef, index, disabledIndices) {\n  if (typeof disabledIndices === 'function') {\n    return disabledIndices(index);\n  } else if (disabledIndices) {\n    return disabledIndices.includes(index);\n  }\n  const element = listRef.current[index];\n  return element == null || element.hasAttribute('disabled') || element.getAttribute('aria-disabled') === 'true';\n}\n\nconst getTabbableOptions = () => ({\n  getShadowRoot: true,\n  displayCheck:\n  // JSDOM does not support the `tabbable` library. To solve this we can\n  // check if `ResizeObserver` is a real function (not polyfilled), which\n  // determines if the current environment is JSDOM-like.\n  typeof ResizeObserver === 'function' && ResizeObserver.toString().includes('[native code]') ? 'full' : 'none'\n});\nfunction getTabbableIn(container, dir) {\n  const list = tabbable(container, getTabbableOptions());\n  const len = list.length;\n  if (len === 0) return;\n  const active = activeElement(getDocument(container));\n  const index = list.indexOf(active);\n  const nextIndex = index === -1 ? dir === 1 ? 0 : len - 1 : index + dir;\n  return list[nextIndex];\n}\nfunction getNextTabbable(referenceElement) {\n  return getTabbableIn(getDocument(referenceElement).body, 1) || referenceElement;\n}\nfunction getPreviousTabbable(referenceElement) {\n  return getTabbableIn(getDocument(referenceElement).body, -1) || referenceElement;\n}\nfunction isOutsideEvent(event, container) {\n  const containerElement = container || event.currentTarget;\n  const relatedTarget = event.relatedTarget;\n  return !relatedTarget || !contains(containerElement, relatedTarget);\n}\nfunction disableFocusInside(container) {\n  const tabbableElements = tabbable(container, getTabbableOptions());\n  tabbableElements.forEach(element => {\n    element.dataset.tabindex = element.getAttribute('tabindex') || '';\n    element.setAttribute('tabindex', '-1');\n  });\n}\nfunction enableFocusInside(container) {\n  const elements = container.querySelectorAll('[data-tabindex]');\n  elements.forEach(element => {\n    const tabindex = element.dataset.tabindex;\n    delete element.dataset.tabindex;\n    if (tabindex) {\n      element.setAttribute('tabindex', tabindex);\n    } else {\n      element.removeAttribute('tabindex');\n    }\n  });\n}\n\nexport { activeElement, contains, createGridCellMap, disableFocusInside, enableFocusInside, findNonDisabledListIndex, getDeepestNode, getDocument, getFloatingFocusElement, getGridCellIndexOfCorner, getGridCellIndices, getGridNavigatedIndex, getMaxListIndex, getMinListIndex, getNextTabbable, getNodeAncestors, getNodeChildren, getPlatform, getPreviousTabbable, getTabbableOptions, getTarget, getUserAgent, isAndroid, isDifferentGridRow, isEventTargetWithin, isIndexOutOfListBounds, isJSDOM, isListIndexDisabled, isMac, isMouseLikePointerType, isOutsideEvent, isReactEvent, isRootElement, isSafari, isTypeableCombobox, isTypeableElement, isVirtualClick, isVirtualPointerEvent, matchesFocusVisible, stopEvent, useEffectEvent, useLatestRef, index as useModernLayoutEffect };\n", "import { normalizeDates } from \"./_lib/normalizeDates.js\";\nimport { startOfDay } from \"./startOfDay.js\";\n\n/**\n * The {@link isSameDay} function options.\n */\n\n/**\n * @name isSameDay\n * @category Day Helpers\n * @summary Are the given dates in the same day (and year and month)?\n *\n * @description\n * Are the given dates in the same day (and year and month)?\n *\n * @param laterDate - The first date to check\n * @param earlierDate - The second date to check\n * @param options - An object with options\n *\n * @returns The dates are in the same day (and year and month)\n *\n * @example\n * // Are 4 September 06:00:00 and 4 September 18:00:00 in the same day?\n * const result = isSameDay(new Date(2014, 8, 4, 6, 0), new Date(2014, 8, 4, 18, 0))\n * //=> true\n *\n * @example\n * // Are 4 September and 4 October in the same day?\n * const result = isSameDay(new Date(2014, 8, 4), new Date(2014, 9, 4))\n * //=> false\n *\n * @example\n * // Are 4 September, 2014 and 4 September, 2015 in the same day?\n * const result = isSameDay(new Date(2014, 8, 4), new Date(2015, 8, 4))\n * //=> false\n */\nexport function isSameDay(laterDate, earlierDate, options) {\n  const [dateLeft_, dateRight_] = normalizeDates(\n    options?.in,\n    laterDate,\n    earlierDate,\n  );\n  return +startOfDay(dateLeft_) === +startOfDay(dateRight_);\n}\n\n// Fallback for modularized imports:\nexport default isSameDay;\n", "import { normalizeDates } from \"./_lib/normalizeDates.js\";\n\n/**\n * The {@link differenceInCalendarMonths} function options.\n */\n\n/**\n * @name differenceInCalendarMonths\n * @category Month Helpers\n * @summary Get the number of calendar months between the given dates.\n *\n * @description\n * Get the number of calendar months between the given dates.\n *\n * @param laterDate - The later date\n * @param earlierDate - The earlier date\n * @param options - An object with options\n *\n * @returns The number of calendar months\n *\n * @example\n * // How many calendar months are between 31 January 2014 and 1 September 2014?\n * const result = differenceInCalendarMonths(\n *   new Date(2014, 8, 1),\n *   new Date(2014, 0, 31)\n * )\n * //=> 8\n */\nexport function differenceInCalendarMonths(laterDate, earlierDate, options) {\n  const [laterDate_, earlierDate_] = normalizeDates(\n    options?.in,\n    laterDate,\n    earlierDate,\n  );\n\n  const yearsDiff = laterDate_.getFullYear() - earlierDate_.getFullYear();\n  const monthsDiff = laterDate_.getMonth() - earlierDate_.getMonth();\n\n  return yearsDiff * 12 + monthsDiff;\n}\n\n// Fallback for modularized imports:\nexport default differenceInCalendarMonths;\n", "import { constructFrom } from \"../constructFrom.js\";\n\nexport function normalizeDates(context, ...dates) {\n  const normalize = constructFrom.bind(\n    null,\n    context || dates.find((date) => typeof date === \"object\"),\n  );\n  return dates.map(normalize);\n}\n", "import { constructFrom } from \"./constructFrom.js\";\nimport { toDate } from \"./toDate.js\";\n\n/**\n * The {@link setYear} function options.\n */\n\n/**\n * @name setYear\n * @category Year Helpers\n * @summary Set the year to the given date.\n *\n * @description\n * Set the year to the given date.\n *\n * @typeParam DateType - The `Date` type, the function operates on. Gets inferred from passed arguments. Allows to use extensions like [`UTCDate`](https://github.com/date-fns/utc).\n * @typeParam ResultDate - The result `Date` type, it is the type returned from the context function if it is passed, or inferred from the arguments.\n *\n * @param date - The date to be changed\n * @param year - The year of the new date\n * @param options - An object with options.\n *\n * @returns The new date with the year set\n *\n * @example\n * // Set year 2013 to 1 September 2014:\n * const result = setYear(new Date(2014, 8, 1), 2013)\n * //=> Sun Sep 01 2013 00:00:00\n */\nexport function setYear(date, year, options) {\n  const date_ = toDate(date, options?.in);\n\n  // Check if date is Invalid Date because Date.prototype.setFullYear ignores the value of Invalid Date\n  if (isNaN(+date_)) return constructFrom(options?.in || date, NaN);\n\n  date_.setFullYear(year);\n  return date_;\n}\n\n// Fallback for modularized imports:\nexport default setYear;\n", "import { toDate } from \"../toDate.js\";\n\n/**\n * Google Chrome as of 67.0.3396.87 introduced timezones with offset that includes seconds.\n * They usually appear for dates that denote time before the timezones were introduced\n * (e.g. for 'Europe/Prague' timezone the offset is GMT+00:57:44 before 1 October 1891\n * and GMT+01:00:00 after that date)\n *\n * Date#getTimezoneOffset returns the offset in minutes and would return 57 for the example above,\n * which would lead to incorrect calculations.\n *\n * This function returns the timezone offset in milliseconds that takes seconds in account.\n */\nexport function getTimezoneOffsetInMilliseconds(date) {\n  const _date = toDate(date);\n  const utcDate = new Date(\n    Date.UTC(\n      _date.getFullYear(),\n      _date.getMonth(),\n      _date.getDate(),\n      _date.getHours(),\n      _date.getMinutes(),\n      _date.getSeconds(),\n      _date.getMilliseconds(),\n    ),\n  );\n  utcDate.setUTCFullYear(_date.getFullYear());\n  return +date - +utcDate;\n}\n", "import { toDate } from \"./toDate.js\";\n\n/**\n * @name isAfter\n * @category Common Helpers\n * @summary Is the first date after the second one?\n *\n * @description\n * Is the first date after the second one?\n *\n * @param date - The date that should be after the other one to return true\n * @param dateToCompare - The date to compare with\n *\n * @returns The first date is after the second date\n *\n * @example\n * // Is 10 July 1989 after 11 February 1987?\n * const result = isAfter(new Date(1989, 6, 10), new Date(1987, 1, 11))\n * //=> true\n */\nexport function isAfter(date, dateToCompare) {\n  return +toDate(date) > +toDate(dateToCompare);\n}\n\n// Fallback for modularized imports:\nexport default isAfter;\n", "import { addDays } from \"./addDays.js\";\n\n/**\n * The {@link subDays} function options.\n */\n\n/**\n * @name subDays\n * @category Day Helpers\n * @summary Subtract the specified number of days from the given date.\n *\n * @typeParam DateType - The `Date` type, the function operates on. Gets inferred from passed arguments. Allows to use extensions like [`UTCDate`](https://github.com/date-fns/utc).\n * @typeParam ResultDate - The result `Date` type, it is the type returned from the context function if it is passed, or inferred from the arguments.\n *\n * @param date - The date to be changed\n * @param amount - The amount of days to be subtracted.\n * @param options - An object with options\n *\n * @returns The new date with the days subtracted\n *\n * @example\n * // Subtract 10 days from 1 September 2014:\n * const result = subDays(new Date(2014, 8, 1), 10)\n * //=> Fri Aug 22 2014 00:00:00\n */\nexport function subDays(date, amount, options) {\n  return addDays(date, -amount, options);\n}\n\n// Fallback for modularized imports:\nexport default subDays;\n", "import { constructFrom } from \"./constructFrom.js\";\nimport { toDate } from \"./toDate.js\";\n\n/**\n * The {@link addMonths} function options.\n */\n\n/**\n * @name addMonths\n * @category Month Helpers\n * @summary Add the specified number of months to the given date.\n *\n * @description\n * Add the specified number of months to the given date.\n *\n * @typeParam DateType - The `Date` type, the function operates on. Gets inferred from passed arguments. Allows to use extensions like [`UTCDate`](https://github.com/date-fns/utc).\n * @typeParam ResultDate - The result `Date` type, it is the type returned from the context function if it is passed, or inferred from the arguments.\n *\n * @param date - The date to be changed\n * @param amount - The amount of months to be added.\n * @param options - The options object\n *\n * @returns The new date with the months added\n *\n * @example\n * // Add 5 months to 1 September 2014:\n * const result = addMonths(new Date(2014, 8, 1), 5)\n * //=> Sun Feb 01 2015 00:00:00\n *\n * // Add one month to 30 January 2023:\n * const result = addMonths(new Date(2023, 0, 30), 1)\n * //=> Tue Feb 28 2023 00:00:00\n */\nexport function addMonths(date, amount, options) {\n  const _date = toDate(date, options?.in);\n  if (isNaN(amount)) return constructFrom(options?.in || date, NaN);\n  if (!amount) {\n    // If 0 months, no-op to avoid changing times in the hour before end of DST\n    return _date;\n  }\n  const dayOfMonth = _date.getDate();\n\n  // The JS Date object supports date math by accepting out-of-bounds values for\n  // month, day, etc. For example, new Date(2020, 0, 0) returns 31 Dec 2019 and\n  // new Date(2020, 13, 1) returns 1 Feb 2021.  This is *almost* the behavior we\n  // want except that dates will wrap around the end of a month, meaning that\n  // new Date(2020, 13, 31) will return 3 Mar 2021 not 28 Feb 2021 as desired. So\n  // we'll default to the end of the desired month by adding 1 to the desired\n  // month and using a date of 0 to back up one day to the end of the desired\n  // month.\n  const endOfDesiredMonth = constructFrom(options?.in || date, _date.getTime());\n  endOfDesiredMonth.setMonth(_date.getMonth() + amount + 1, 0);\n  const daysInMonth = endOfDesiredMonth.getDate();\n  if (dayOfMonth >= daysInMonth) {\n    // If we're already at the end of the month, then this is the correct date\n    // and we're done.\n    return endOfDesiredMonth;\n  } else {\n    // Otherwise, we now know that setting the original day-of-month value won't\n    // cause an overflow, so set the desired day-of-month. Note that we can't\n    // just set the date of `endOfDesiredMonth` because that object may have had\n    // its time changed in the unusual case where where a DST transition was on\n    // the last day of the month and its local time was in the hour skipped or\n    // repeated next to a DST transition.  So we use `date` instead which is\n    // guaranteed to still have the original time.\n    _date.setFullYear(\n      endOfDesiredMonth.getFullYear(),\n      endOfDesiredMonth.getMonth(),\n      dayOfMonth,\n    );\n    return _date;\n  }\n}\n\n// Fallback for modularized imports:\nexport default addMonths;\n", "import { toDate } from \"./toDate.js\";\n\n/**\n * The {@link startOfQuarter} function options.\n */\n\n/**\n * @name startOfQuarter\n * @category Quarter Helpers\n * @summary Return the start of a year quarter for the given date.\n *\n * @description\n * Return the start of a year quarter for the given date.\n * The result will be in the local timezone.\n *\n * @typeParam DateType - The `Date` type, the function operates on. Gets inferred from passed arguments. Allows to use extensions like [`UTCDate`](https://github.com/date-fns/utc).\n * @typeParam ResultDate - The result `Date` type, it is the type returned from the context function if it is passed, or inferred from the arguments.\n *\n * @param date - The original date\n * @param options - The options\n *\n * @returns The start of a quarter\n *\n * @example\n * // The start of a quarter for 2 September 2014 11:55:00:\n * const result = startOfQuarter(new Date(2014, 8, 2, 11, 55, 0))\n * //=> Tue Jul 01 2014 00:00:00\n */\nexport function startOfQuarter(date, options) {\n  const _date = toDate(date, options?.in);\n  const currentMonth = _date.getMonth();\n  const month = currentMonth - (currentMonth % 3);\n  _date.setMonth(month, 1);\n  _date.setHours(0, 0, 0, 0);\n  return _date;\n}\n\n// Fallback for modularized imports:\nexport default startOfQuarter;\n", "import { toDate } from \"./toDate.js\";\n\n/**\n * The {@link setHours} function options.\n */\n\n/**\n * @name setHours\n * @category Hour Helpers\n * @summary Set the hours to the given date.\n *\n * @description\n * Set the hours to the given date.\n *\n * @typeParam DateType - The `Date` type, the function operates on. Gets inferred from passed arguments. Allows to use extensions like [`UTCDate`](https://github.com/date-fns/utc).\n * @typeParam ResultDate - The result `Date` type, it is the type returned from the context function if it is passed, or inferred from the arguments.\n *\n * @param date - The date to be changed\n * @param hours - The hours of the new date\n * @param options - An object with options\n *\n * @returns The new date with the hours set\n *\n * @example\n * // Set 4 hours to 1 September 2014 11:30:00:\n * const result = setHours(new Date(2014, 8, 1, 11, 30), 4)\n * //=> Mon Sep 01 2014 04:30:00\n */\nexport function setHours(date, hours, options) {\n  const _date = toDate(date, options?.in);\n  _date.setHours(hours);\n  return _date;\n}\n\n// Fallback for modularized imports:\nexport default setHours;\n", "import { addMonths } from \"./addMonths.js\";\n\n/**\n * The {@link addQuarters} function options.\n */\n\n/**\n * @name addQuarters\n * @category Quarter Helpers\n * @summary Add the specified number of year quarters to the given date.\n *\n * @description\n * Add the specified number of year quarters to the given date.\n *\n * @typeParam DateType - The `Date` type, the function operates on. Gets inferred from passed arguments. Allows to use extensions like [`UTCDate`](https://github.com/date-fns/utc).\n * @typeParam ResultDate - The result `Date` type, it is the type returned from the context function if it is passed, or inferred from the arguments.\n *\n * @param date - The date to be changed\n * @param amount - The amount of quarters to be added.\n * @param options - An object with options\n *\n * @returns The new date with the quarters added\n *\n * @example\n * // Add 1 quarter to 1 September 2014:\n * const result = addQuarters(new Date(2014, 8, 1), 1)\n * //=; Mon Dec 01 2014 00:00:00\n */\nexport function addQuarters(date, amount, options) {\n  return addMonths(date, amount * 3, options);\n}\n\n// Fallback for modularized imports:\nexport default addQuarters;\n", "import { addWeeks } from \"./addWeeks.js\";\n\n/**\n * The {@link subWeeks} function options.\n */\n\n/**\n * @name subWeeks\n * @category Week Helpers\n * @summary Subtract the specified number of weeks from the given date.\n *\n * @description\n * Subtract the specified number of weeks from the given date.\n *\n * @typeParam DateType - The `Date` type, the function operates on. Gets inferred from passed arguments. Allows to use extensions like [`UTCDate`](https://github.com/date-fns/utc).\n * @typeParam ResultDate - The result `Date` type, it is the type returned from the context function if it is passed, or inferred from the arguments.\n *\n * @param date - The date to be changed\n * @param amount - The amount of weeks to be subtracted.\n * @param options - An object with options\n *\n * @returns The new date with the weeks subtracted\n *\n * @example\n * // Subtract 4 weeks from 1 September 2014:\n * const result = subWeeks(new Date(2014, 8, 1), 4)\n * //=> Mon Aug 04 2014 00:00:00\n */\nexport function subWeeks(date, amount, options) {\n  return addWeeks(date, -amount, options);\n}\n\n// Fallback for modularized imports:\nexport default subWeeks;\n", "import { addMilliseconds } from \"./addMilliseconds.js\";\n\n/**\n * The {@link addSeconds} function options.\n */\n\n/**\n * @name addSeconds\n * @category Second Helpers\n * @summary Add the specified number of seconds to the given date.\n *\n * @description\n * Add the specified number of seconds to the given date.\n *\n * @typeParam DateType - The `Date` type, the function operates on. Gets inferred from passed arguments. Allows to use extensions like [`UTCDate`](https://github.com/date-fns/utc).\n * @typeParam ResultDate - The result `Date` type, it is the type returned from the context function if it is passed, or inferred from the arguments.\n *\n * @param date - The date to be changed\n * @param amount - The amount of seconds to be added.\n * @param options - An object with options\n *\n * @returns The new date with the seconds added\n *\n * @example\n * // Add 30 seconds to 10 July 2014 12:45:00:\n * const result = addSeconds(new Date(2014, 6, 10, 12, 45, 0), 30)\n * //=> Thu Jul 10 2014 12:45:30\n */\nexport function addSeconds(date, amount, options) {\n  return addMilliseconds(date, amount * 1000, options);\n}\n\n// Fallback for modularized imports:\nexport default addSeconds;\n", "import { startOfWeek } from \"./startOfWeek.js\";\n\n/**\n * The {@link startOfISOWeek} function options.\n */\n\n/**\n * @name startOfISOWeek\n * @category ISO Week Helpers\n * @summary Return the start of an ISO week for the given date.\n *\n * @description\n * Return the start of an ISO week for the given date.\n * The result will be in the local timezone.\n *\n * ISO week-numbering year: http://en.wikipedia.org/wiki/ISO_week_date\n *\n * @typeParam DateType - The `Date` type, the function operates on. Gets inferred from passed arguments. Allows to use extensions like [`UTCDate`](https://github.com/date-fns/utc).\n * @typeParam ResultDate - The result `Date` type, it is the type returned from the context function if it is passed, or inferred from the arguments.\n *\n * @param date - The original date\n * @param options - An object with options\n *\n * @returns The start of an ISO week\n *\n * @example\n * // The start of an ISO week for 2 September 2014 11:55:00:\n * const result = startOfISOWeek(new Date(2014, 8, 2, 11, 55, 0))\n * //=> Mon Sep 01 2014 00:00:00\n */\nexport function startOfISOWeek(date, options) {\n  return startOfWeek(date, { ...options, weekStartsOn: 1 });\n}\n\n// Fallback for modularized imports:\nexport default startOfISOWeek;\n", "import { constructFrom } from \"./constructFrom.js\";\nimport { startOfISOWeek } from \"./startOfISOWeek.js\";\nimport { toDate } from \"./toDate.js\";\n\n/**\n * The {@link getISOWeekYear} function options.\n */\n\n/**\n * @name getISOWeekYear\n * @category ISO Week-Numbering Year Helpers\n * @summary Get the ISO week-numbering year of the given date.\n *\n * @description\n * Get the ISO week-numbering year of the given date,\n * which always starts 3 days before the year's first Thursday.\n *\n * ISO week-numbering year: http://en.wikipedia.org/wiki/ISO_week_date\n *\n * @param date - The given date\n *\n * @returns The ISO week-numbering year\n *\n * @example\n * // Which ISO-week numbering year is 2 January 2005?\n * const result = getISOWeekYear(new Date(2005, 0, 2))\n * //=> 2004\n */\nexport function getISOWeekYear(date, options) {\n  const _date = toDate(date, options?.in);\n  const year = _date.getFullYear();\n\n  const fourthOfJanuaryOfNextYear = constructFrom(_date, 0);\n  fourthOfJanuaryOfNextYear.setFullYear(year + 1, 0, 4);\n  fourthOfJanuaryOfNextYear.setHours(0, 0, 0, 0);\n  const startOfNextYear = startOfISOWeek(fourthOfJanuaryOfNextYear);\n\n  const fourthOfJanuaryOfThisYear = constructFrom(_date, 0);\n  fourthOfJanuaryOfThisYear.setFullYear(year, 0, 4);\n  fourthOfJanuaryOfThisYear.setHours(0, 0, 0, 0);\n  const startOfThisYear = startOfISOWeek(fourthOfJanuaryOfThisYear);\n\n  if (_date.getTime() >= startOfNextYear.getTime()) {\n    return year + 1;\n  } else if (_date.getTime() >= startOfThisYear.getTime()) {\n    return year;\n  } else {\n    return year - 1;\n  }\n}\n\n// Fallback for modularized imports:\nexport default getISOWeekYear;\n", "import { toDate } from \"./toDate.js\";\n\n/**\n * The {@link getYear} function options.\n */\n\n/**\n * @name getYear\n * @category Year Helpers\n * @summary Get the year of the given date.\n *\n * @description\n * Get the year of the given date.\n *\n * @param date - The given date\n * @param options - An object with options\n *\n * @returns The year\n *\n * @example\n * // Which year is 2 July 2014?\n * const result = getYear(new Date(2014, 6, 2))\n * //=> 2014\n */\nexport function getYear(date, options) {\n  return toDate(date, options?.in).getFullYear();\n}\n\n// Fallback for modularized imports:\nexport default getYear;\n", "import { toDate } from \"./toDate.js\";\n\n/**\n * @name isEqual\n * @category Common Helpers\n * @summary Are the given dates equal?\n *\n * @description\n * Are the given dates equal?\n *\n * @param dateLeft - The first date to compare\n * @param dateRight - The second date to compare\n *\n * @returns The dates are equal\n *\n * @example\n * // Are 2 July 2014 06:30:45.000 and 2 July 2014 06:30:45.500 equal?\n * const result = isEqual(\n *   new Date(2014, 6, 2, 6, 30, 45, 0),\n *   new Date(2014, 6, 2, 6, 30, 45, 500)\n * )\n * //=> false\n */\nexport function isEqual(leftDate, rightDate) {\n  return +toDate(leftDate) === +toDate(rightDate);\n}\n\n// Fallback for modularized imports:\nexport default isEqual;\n", "import { normalizeDates } from \"./_lib/normalizeDates.js\";\nimport { startOfQuarter } from \"./startOfQuarter.js\";\n\n/**\n * The {@link isSameQuarter} function options.\n */\n\n/**\n * @name isSameQuarter\n * @category Quarter Helpers\n * @summary Are the given dates in the same quarter (and year)?\n *\n * @description\n * Are the given dates in the same quarter (and year)?\n *\n * @param laterDate - The first date to check\n * @param earlierDate - The second date to check\n * @param options - An object with options\n *\n * @returns The dates are in the same quarter (and year)\n *\n * @example\n * // Are 1 January 2014 and 8 March 2014 in the same quarter?\n * const result = isSameQuarter(new Date(2014, 0, 1), new Date(2014, 2, 8))\n * //=> true\n *\n * @example\n * // Are 1 January 2014 and 1 January 2015 in the same quarter?\n * const result = isSameQuarter(new Date(2014, 0, 1), new Date(2015, 0, 1))\n * //=> false\n */\nexport function isSameQuarter(laterDate, earlierDate, options) {\n  const [dateLeft_, dateRight_] = normalizeDates(\n    options?.in,\n    laterDate,\n    earlierDate,\n  );\n  return +startOfQuarter(dateLeft_) === +startOfQuarter(dateRight_);\n}\n\n// Fallback for modularized imports:\nexport default isSameQuarter;\n", "import { getDefaultOptions } from \"./_lib/defaultOptions.js\";\nimport { toDate } from \"./toDate.js\";\n\n/**\n * The {@link endOfWeek} function options.\n */\n\n/**\n * @name endOfWeek\n * @category Week Helpers\n * @summary Return the end of a week for the given date.\n *\n * @description\n * Return the end of a week for the given date.\n * The result will be in the local timezone.\n *\n * @typeParam DateType - The `Date` type, the function operates on. Gets inferred from passed arguments. Allows to use extensions like [`UTCDate`](https://github.com/date-fns/utc).\n * @typeParam ResultDate - The result `Date` type, it is the type returned from the context function if it is passed, or inferred from the arguments.\n *\n * @param date - The original date\n * @param options - An object with options\n *\n * @returns The end of a week\n *\n * @example\n * // The end of a week for 2 September 2014 11:55:00:\n * const result = endOfWeek(new Date(2014, 8, 2, 11, 55, 0))\n * //=> Sat Sep 06 2014 23:59:59.999\n *\n * @example\n * // If the week starts on Monday, the end of the week for 2 September 2014 11:55:00:\n * const result = endOfWeek(new Date(2014, 8, 2, 11, 55, 0), { weekStartsOn: 1 })\n * //=> Sun Sep 07 2014 23:59:59.999\n */\nexport function endOfWeek(date, options) {\n  const defaultOptions = getDefaultOptions();\n  const weekStartsOn =\n    options?.weekStartsOn ??\n    options?.locale?.options?.weekStartsOn ??\n    defaultOptions.weekStartsOn ??\n    defaultOptions.locale?.options?.weekStartsOn ??\n    0;\n\n  const _date = toDate(date, options?.in);\n  const day = _date.getDay();\n  const diff = (day < weekStartsOn ? -7 : 0) + 6 - (day - weekStartsOn);\n\n  _date.setDate(_date.getDate() + diff);\n  _date.setHours(23, 59, 59, 999);\n  return _date;\n}\n\n// Fallback for modularized imports:\nexport default endOfWeek;\n", "import { normalizeDates } from \"./_lib/normalizeDates.js\";\n\n/**\n * The {@link differenceInCalendarYears} function options.\n */\n\n/**\n * @name differenceInCalendarYears\n * @category Year Helpers\n * @summary Get the number of calendar years between the given dates.\n *\n * @description\n * Get the number of calendar years between the given dates.\n *\n * @param laterDate - The later date\n * @param earlierDate - The earlier date\n * @param options - An object with options\n\n * @returns The number of calendar years\n *\n * @example\n * // How many calendar years are between 31 December 2013 and 11 February 2015?\n * const result = differenceInCalendarYears(\n *   new Date(2015, 1, 11),\n *   new Date(2013, 11, 31)\n * );\n * //=> 2\n */\nexport function differenceInCalendarYears(laterDate, earlierDate, options) {\n  const [laterDate_, earlierDate_] = normalizeDates(\n    options?.in,\n    laterDate,\n    earlierDate,\n  );\n  return laterDate_.getFullYear() - earlierDate_.getFullYear();\n}\n\n// Fallback for modularized imports:\nexport default differenceInCalendarYears;\n", "/**\n * @name isDate\n * @category Common Helpers\n * @summary Is the given value a date?\n *\n * @description\n * Returns true if the given value is an instance of Date. The function works for dates transferred across iframes.\n *\n * @param value - The value to check\n *\n * @returns True if the given value is a date\n *\n * @example\n * // For a valid date:\n * const result = isDate(new Date())\n * //=> true\n *\n * @example\n * // For an invalid date:\n * const result = isDate(new Date(NaN))\n * //=> true\n *\n * @example\n * // For some value:\n * const result = isDate('2014-02-31')\n * //=> false\n *\n * @example\n * // For an object:\n * const result = isDate({})\n * //=> false\n */\nexport function isDate(value) {\n  return (\n    value instanceof Date ||\n    (typeof value === \"object\" &&\n      Object.prototype.toString.call(value) === \"[object Date]\")\n  );\n}\n\n// Fallback for modularized imports:\nexport default isDate;\n", "import { getTimezoneOffsetInMilliseconds } from \"./_lib/getTimezoneOffsetInMilliseconds.js\";\nimport { normalizeDates } from \"./_lib/normalizeDates.js\";\nimport { millisecondsInDay } from \"./constants.js\";\nimport { startOfDay } from \"./startOfDay.js\";\n\n/**\n * The {@link differenceInCalendarDays} function options.\n */\n\n/**\n * @name differenceInCalendarDays\n * @category Day Helpers\n * @summary Get the number of calendar days between the given dates.\n *\n * @description\n * Get the number of calendar days between the given dates. This means that the times are removed\n * from the dates and then the difference in days is calculated.\n *\n * @param laterDate - The later date\n * @param earlierDate - The earlier date\n * @param options - The options object\n *\n * @returns The number of calendar days\n *\n * @example\n * // How many calendar days are between\n * // 2 July 2011 23:00:00 and 2 July 2012 00:00:00?\n * const result = differenceInCalendarDays(\n *   new Date(2012, 6, 2, 0, 0),\n *   new Date(2011, 6, 2, 23, 0)\n * )\n * //=> 366\n * // How many calendar days are between\n * // 2 July 2011 23:59:00 and 3 July 2011 00:01:00?\n * const result = differenceInCalendarDays(\n *   new Date(2011, 6, 3, 0, 1),\n *   new Date(2011, 6, 2, 23, 59)\n * )\n * //=> 1\n */\nexport function differenceInCalendarDays(laterDate, earlierDate, options) {\n  const [laterDate_, earlierDate_] = normalizeDates(\n    options?.in,\n    laterDate,\n    earlierDate,\n  );\n\n  const laterStartOfDay = startOfDay(laterDate_);\n  const earlierStartOfDay = startOfDay(earlierDate_);\n\n  const laterTimestamp =\n    +laterStartOfDay - getTimezoneOffsetInMilliseconds(laterStartOfDay);\n  const earlierTimestamp =\n    +earlierStartOfDay - getTimezoneOffsetInMilliseconds(earlierStartOfDay);\n\n  // Round the number of days to the nearest integer because the number of\n  // milliseconds in a day is not constant (e.g. it's different in the week of\n  // the daylight saving time clock shift).\n  return Math.round((laterTimestamp - earlierTimestamp) / millisecondsInDay);\n}\n\n// Fallback for modularized imports:\nexport default differenceInCalendarDays;\n", "const dateLongFormatter = (pattern, formatLong) => {\n  switch (pattern) {\n    case \"P\":\n      return formatLong.date({ width: \"short\" });\n    case \"PP\":\n      return formatLong.date({ width: \"medium\" });\n    case \"PPP\":\n      return formatLong.date({ width: \"long\" });\n    case \"PPPP\":\n    default:\n      return formatLong.date({ width: \"full\" });\n  }\n};\n\nconst timeLongFormatter = (pattern, formatLong) => {\n  switch (pattern) {\n    case \"p\":\n      return formatLong.time({ width: \"short\" });\n    case \"pp\":\n      return formatLong.time({ width: \"medium\" });\n    case \"ppp\":\n      return formatLong.time({ width: \"long\" });\n    case \"pppp\":\n    default:\n      return formatLong.time({ width: \"full\" });\n  }\n};\n\nconst dateTimeLongFormatter = (pattern, formatLong) => {\n  const matchResult = pattern.match(/(P+)(p+)?/) || [];\n  const datePattern = matchResult[1];\n  const timePattern = matchResult[2];\n\n  if (!timePattern) {\n    return dateLongFormatter(pattern, formatLong);\n  }\n\n  let dateTimeFormat;\n\n  switch (datePattern) {\n    case \"P\":\n      dateTimeFormat = formatLong.dateTime({ width: \"short\" });\n      break;\n    case \"PP\":\n      dateTimeFormat = formatLong.dateTime({ width: \"medium\" });\n      break;\n    case \"PPP\":\n      dateTimeFormat = formatLong.dateTime({ width: \"long\" });\n      break;\n    case \"PPPP\":\n    default:\n      dateTimeFormat = formatLong.dateTime({ width: \"full\" });\n      break;\n  }\n\n  return dateTimeFormat\n    .replace(\"{{date}}\", dateLongFormatter(datePattern, formatLong))\n    .replace(\"{{time}}\", timeLongFormatter(timePattern, formatLong));\n};\n\nexport const longFormatters = {\n  p: timeLongFormatter,\n  P: dateTimeLongFormatter,\n};\n", "import { addDays } from \"./addDays.js\";\n\n/**\n * The {@link addWeeks} function options.\n */\n\n/**\n * @name addWeeks\n * @category Week Helpers\n * @summary Add the specified number of weeks to the given date.\n *\n * @description\n * Add the specified number of weeks to the given date.\n *\n * @typeParam DateType - The `Date` type, the function operates on. Gets inferred from passed arguments. Allows to use extensions like [`UTCDate`](https://github.com/date-fns/utc).\n * @typeParam ResultDate - The result `Date` type, it is the type returned from the context function if it is passed, or inferred from the arguments.\n *\n * @param date - The date to be changed\n * @param amount - The amount of weeks to be added.\n * @param options - An object with options\n *\n * @returns The new date with the weeks added\n *\n * @example\n * // Add 4 weeks to 1 September 2014:\n * const result = addWeeks(new Date(2014, 8, 1), 4)\n * //=> Mon Sep 29 2014 00:00:00\n */\nexport function addWeeks(date, amount, options) {\n  return addDays(date, amount * 7, options);\n}\n\n// Fallback for modularized imports:\nexport default addWeeks;\n", "let defaultOptions = {};\n\nexport function getDefaultOptions() {\n  return defaultOptions;\n}\n\nexport function setDefaultOptions(newOptions) {\n  defaultOptions = newOptions;\n}\n", "import { addMonths } from \"./addMonths.js\";\n\n/**\n * The {@link addYears} function options.\n */\n\n/**\n * @name addYears\n * @category Year Helpers\n * @summary Add the specified number of years to the given date.\n *\n * @description\n * Add the specified number of years to the given date.\n *\n * @typeParam DateType - The `Date` type, the function operates on. Gets inferred from passed arguments. Allows to use extensions like [`UTCDate`](https://github.com/date-fns/utc).\n * @typeParam ResultDate - The result `Date` type.\n *\n * @param date - The date to be changed\n * @param amount - The amount of years to be added.\n * @param options - The options\n *\n * @returns The new date with the years added\n *\n * @example\n * // Add 5 years to 1 September 2014:\n * const result = addYears(new Date(2014, 8, 1), 5)\n * //=> Sun Sep 01 2019 00:00:00\n */\nexport function addYears(date, amount, options) {\n  return addMonths(date, amount * 12, options);\n}\n\n// Fallback for modularized imports:\nexport default addYears;\n", "import { computePosition, arrow as arrow$2, offset as offset$1, shift as shift$1, limitShift as limitShift$1, flip as flip$1, size as size$1, autoPlacement as autoPlacement$1, hide as hide$1, inline as inline$1 } from '@floating-ui/dom';\nexport { autoUpdate, computePosition, detectOverflow, getOverflowAncestors, platform } from '@floating-ui/dom';\nimport * as React from 'react';\nimport { useLayoutEffect, useEffect } from 'react';\nimport * as ReactDOM from 'react-dom';\n\nvar index = typeof document !== 'undefined' ? useLayoutEffect : useEffect;\n\n// Fork of `fast-deep-equal` that only does the comparisons we need and compares\n// functions\nfunction deepEqual(a, b) {\n  if (a === b) {\n    return true;\n  }\n  if (typeof a !== typeof b) {\n    return false;\n  }\n  if (typeof a === 'function' && a.toString() === b.toString()) {\n    return true;\n  }\n  let length;\n  let i;\n  let keys;\n  if (a && b && typeof a === 'object') {\n    if (Array.isArray(a)) {\n      length = a.length;\n      if (length !== b.length) return false;\n      for (i = length; i-- !== 0;) {\n        if (!deepEqual(a[i], b[i])) {\n          return false;\n        }\n      }\n      return true;\n    }\n    keys = Object.keys(a);\n    length = keys.length;\n    if (length !== Object.keys(b).length) {\n      return false;\n    }\n    for (i = length; i-- !== 0;) {\n      if (!{}.hasOwnProperty.call(b, keys[i])) {\n        return false;\n      }\n    }\n    for (i = length; i-- !== 0;) {\n      const key = keys[i];\n      if (key === '_owner' && a.$$typeof) {\n        continue;\n      }\n      if (!deepEqual(a[key], b[key])) {\n        return false;\n      }\n    }\n    return true;\n  }\n  return a !== a && b !== b;\n}\n\nfunction getDPR(element) {\n  if (typeof window === 'undefined') {\n    return 1;\n  }\n  const win = element.ownerDocument.defaultView || window;\n  return win.devicePixelRatio || 1;\n}\n\nfunction roundByDPR(element, value) {\n  const dpr = getDPR(element);\n  return Math.round(value * dpr) / dpr;\n}\n\nfunction useLatestRef(value) {\n  const ref = React.useRef(value);\n  index(() => {\n    ref.current = value;\n  });\n  return ref;\n}\n\n/**\n * Provides data to position a floating element.\n * @see https://floating-ui.com/docs/useFloating\n */\nfunction useFloating(options) {\n  if (options === void 0) {\n    options = {};\n  }\n  const {\n    placement = 'bottom',\n    strategy = 'absolute',\n    middleware = [],\n    platform,\n    elements: {\n      reference: externalReference,\n      floating: externalFloating\n    } = {},\n    transform = true,\n    whileElementsMounted,\n    open\n  } = options;\n  const [data, setData] = React.useState({\n    x: 0,\n    y: 0,\n    strategy,\n    placement,\n    middlewareData: {},\n    isPositioned: false\n  });\n  const [latestMiddleware, setLatestMiddleware] = React.useState(middleware);\n  if (!deepEqual(latestMiddleware, middleware)) {\n    setLatestMiddleware(middleware);\n  }\n  const [_reference, _setReference] = React.useState(null);\n  const [_floating, _setFloating] = React.useState(null);\n  const setReference = React.useCallback(node => {\n    if (node !== referenceRef.current) {\n      referenceRef.current = node;\n      _setReference(node);\n    }\n  }, []);\n  const setFloating = React.useCallback(node => {\n    if (node !== floatingRef.current) {\n      floatingRef.current = node;\n      _setFloating(node);\n    }\n  }, []);\n  const referenceEl = externalReference || _reference;\n  const floatingEl = externalFloating || _floating;\n  const referenceRef = React.useRef(null);\n  const floatingRef = React.useRef(null);\n  const dataRef = React.useRef(data);\n  const hasWhileElementsMounted = whileElementsMounted != null;\n  const whileElementsMountedRef = useLatestRef(whileElementsMounted);\n  const platformRef = useLatestRef(platform);\n  const openRef = useLatestRef(open);\n  const update = React.useCallback(() => {\n    if (!referenceRef.current || !floatingRef.current) {\n      return;\n    }\n    const config = {\n      placement,\n      strategy,\n      middleware: latestMiddleware\n    };\n    if (platformRef.current) {\n      config.platform = platformRef.current;\n    }\n    computePosition(referenceRef.current, floatingRef.current, config).then(data => {\n      const fullData = {\n        ...data,\n        // The floating element's position may be recomputed while it's closed\n        // but still mounted (such as when transitioning out). To ensure\n        // `isPositioned` will be `false` initially on the next open, avoid\n        // setting it to `true` when `open === false` (must be specified).\n        isPositioned: openRef.current !== false\n      };\n      if (isMountedRef.current && !deepEqual(dataRef.current, fullData)) {\n        dataRef.current = fullData;\n        ReactDOM.flushSync(() => {\n          setData(fullData);\n        });\n      }\n    });\n  }, [latestMiddleware, placement, strategy, platformRef, openRef]);\n  index(() => {\n    if (open === false && dataRef.current.isPositioned) {\n      dataRef.current.isPositioned = false;\n      setData(data => ({\n        ...data,\n        isPositioned: false\n      }));\n    }\n  }, [open]);\n  const isMountedRef = React.useRef(false);\n  index(() => {\n    isMountedRef.current = true;\n    return () => {\n      isMountedRef.current = false;\n    };\n  }, []);\n  index(() => {\n    if (referenceEl) referenceRef.current = referenceEl;\n    if (floatingEl) floatingRef.current = floatingEl;\n    if (referenceEl && floatingEl) {\n      if (whileElementsMountedRef.current) {\n        return whileElementsMountedRef.current(referenceEl, floatingEl, update);\n      }\n      update();\n    }\n  }, [referenceEl, floatingEl, update, whileElementsMountedRef, hasWhileElementsMounted]);\n  const refs = React.useMemo(() => ({\n    reference: referenceRef,\n    floating: floatingRef,\n    setReference,\n    setFloating\n  }), [setReference, setFloating]);\n  const elements = React.useMemo(() => ({\n    reference: referenceEl,\n    floating: floatingEl\n  }), [referenceEl, floatingEl]);\n  const floatingStyles = React.useMemo(() => {\n    const initialStyles = {\n      position: strategy,\n      left: 0,\n      top: 0\n    };\n    if (!elements.floating) {\n      return initialStyles;\n    }\n    const x = roundByDPR(elements.floating, data.x);\n    const y = roundByDPR(elements.floating, data.y);\n    if (transform) {\n      return {\n        ...initialStyles,\n        transform: \"translate(\" + x + \"px, \" + y + \"px)\",\n        ...(getDPR(elements.floating) >= 1.5 && {\n          willChange: 'transform'\n        })\n      };\n    }\n    return {\n      position: strategy,\n      left: x,\n      top: y\n    };\n  }, [strategy, transform, elements.floating, data.x, data.y]);\n  return React.useMemo(() => ({\n    ...data,\n    update,\n    refs,\n    elements,\n    floatingStyles\n  }), [data, update, refs, elements, floatingStyles]);\n}\n\n/**\n * Provides data to position an inner element of the floating element so that it\n * appears centered to the reference element.\n * This wraps the core `arrow` middleware to allow React refs as the element.\n * @see https://floating-ui.com/docs/arrow\n */\nconst arrow$1 = options => {\n  function isRef(value) {\n    return {}.hasOwnProperty.call(value, 'current');\n  }\n  return {\n    name: 'arrow',\n    options,\n    fn(state) {\n      const {\n        element,\n        padding\n      } = typeof options === 'function' ? options(state) : options;\n      if (element && isRef(element)) {\n        if (element.current != null) {\n          return arrow$2({\n            element: element.current,\n            padding\n          }).fn(state);\n        }\n        return {};\n      }\n      if (element) {\n        return arrow$2({\n          element,\n          padding\n        }).fn(state);\n      }\n      return {};\n    }\n  };\n};\n\n/**\n * Modifies the placement by translating the floating element along the\n * specified axes.\n * A number (shorthand for `mainAxis` or distance), or an axes configuration\n * object may be passed.\n * @see https://floating-ui.com/docs/offset\n */\nconst offset = (options, deps) => ({\n  ...offset$1(options),\n  options: [options, deps]\n});\n\n/**\n * Optimizes the visibility of the floating element by shifting it in order to\n * keep it in view when it will overflow the clipping boundary.\n * @see https://floating-ui.com/docs/shift\n */\nconst shift = (options, deps) => ({\n  ...shift$1(options),\n  options: [options, deps]\n});\n\n/**\n * Built-in `limiter` that will stop `shift()` at a certain point.\n */\nconst limitShift = (options, deps) => ({\n  ...limitShift$1(options),\n  options: [options, deps]\n});\n\n/**\n * Optimizes the visibility of the floating element by flipping the `placement`\n * in order to keep it in view when the preferred placement(s) will overflow the\n * clipping boundary. Alternative to `autoPlacement`.\n * @see https://floating-ui.com/docs/flip\n */\nconst flip = (options, deps) => ({\n  ...flip$1(options),\n  options: [options, deps]\n});\n\n/**\n * Provides data that allows you to change the size of the floating element —\n * for instance, prevent it from overflowing the clipping boundary or match the\n * width of the reference element.\n * @see https://floating-ui.com/docs/size\n */\nconst size = (options, deps) => ({\n  ...size$1(options),\n  options: [options, deps]\n});\n\n/**\n * Optimizes the visibility of the floating element by choosing the placement\n * that has the most space available automatically, without needing to specify a\n * preferred placement. Alternative to `flip`.\n * @see https://floating-ui.com/docs/autoPlacement\n */\nconst autoPlacement = (options, deps) => ({\n  ...autoPlacement$1(options),\n  options: [options, deps]\n});\n\n/**\n * Provides data to hide the floating element in applicable situations, such as\n * when it is not in the same clipping context as the reference element.\n * @see https://floating-ui.com/docs/hide\n */\nconst hide = (options, deps) => ({\n  ...hide$1(options),\n  options: [options, deps]\n});\n\n/**\n * Provides improved positioning for inline reference elements that can span\n * over multiple lines, such as hyperlinks or range selections.\n * @see https://floating-ui.com/docs/inline\n */\nconst inline = (options, deps) => ({\n  ...inline$1(options),\n  options: [options, deps]\n});\n\n/**\n * Provides data to position an inner element of the floating element so that it\n * appears centered to the reference element.\n * This wraps the core `arrow` middleware to allow React refs as the element.\n * @see https://floating-ui.com/docs/arrow\n */\nconst arrow = (options, deps) => ({\n  ...arrow$1(options),\n  options: [options, deps]\n});\n\nexport { arrow, autoPlacement, flip, hide, inline, limitShift, offset, shift, size, useFloating };\n", "import { toDate } from \"./toDate.js\";\n\n/**\n * The {@link getMonth} function options.\n */\n\n/**\n * @name getMonth\n * @category Month Helpers\n * @summary Get the month of the given date.\n *\n * @description\n * Get the month of the given date.\n *\n * @param date - The given date\n * @param options - An object with options\n *\n * @returns The month index (0-11)\n *\n * @example\n * // Which month is 29 February 2012?\n * const result = getMonth(new Date(2012, 1, 29))\n * //=> 1\n */\nexport function getMonth(date, options) {\n  return toDate(date, options?.in).getMonth();\n}\n\n// Fallback for modularized imports:\nexport default getMonth;\n", "const dayOfYearTokenRE = /^D+$/;\nconst weekYearTokenRE = /^Y+$/;\n\nconst throwTokens = [\"D\", \"DD\", \"YY\", \"YYYY\"];\n\nexport function isProtectedDayOfYearToken(token) {\n  return dayOfYearTokenRE.test(token);\n}\n\nexport function isProtectedWeekYearToken(token) {\n  return weekYearTokenRE.test(token);\n}\n\nexport function warnOrThrowProtectedError(token, format, input) {\n  const _message = message(token, format, input);\n  console.warn(_message);\n  if (throwTokens.includes(token)) throw new RangeError(_message);\n}\n\nfunction message(token, format, input) {\n  const subject = token[0] === \"Y\" ? \"years\" : \"days of the month\";\n  return `Use \\`${token.toLowerCase()}\\` instead of \\`${token}\\` (in \\`${format}\\`) for formatting ${subject} to the input \\`${input}\\`; see: https://github.com/date-fns/date-fns/blob/master/docs/unicodeTokens.md`;\n}\n", "import { getDefaultOptions } from \"./_lib/defaultOptions.js\";\nimport { toDate } from \"./toDate.js\";\n\n/**\n * The {@link startOfWeek} function options.\n */\n\n/**\n * @name startOfWeek\n * @category Week Helpers\n * @summary Return the start of a week for the given date.\n *\n * @description\n * Return the start of a week for the given date.\n * The result will be in the local timezone.\n *\n * @typeParam DateType - The `Date` type, the function operates on. Gets inferred from passed arguments. Allows to use extensions like [`UTCDate`](https://github.com/date-fns/utc).\n * @typeParam ResultDate - The result `Date` type, it is the type returned from the context function if it is passed, or inferred from the arguments.\n *\n * @param date - The original date\n * @param options - An object with options\n *\n * @returns The start of a week\n *\n * @example\n * // The start of a week for 2 September 2014 11:55:00:\n * const result = startOfWeek(new Date(2014, 8, 2, 11, 55, 0))\n * //=> Sun Aug 31 2014 00:00:00\n *\n * @example\n * // If the week starts on Monday, the start of the week for 2 September 2014 11:55:00:\n * const result = startOfWeek(new Date(2014, 8, 2, 11, 55, 0), { weekStartsOn: 1 })\n * //=> Mon Sep 01 2014 00:00:00\n */\nexport function startOfWeek(date, options) {\n  const defaultOptions = getDefaultOptions();\n  const weekStartsOn =\n    options?.weekStartsOn ??\n    options?.locale?.options?.weekStartsOn ??\n    defaultOptions.weekStartsOn ??\n    defaultOptions.locale?.options?.weekStartsOn ??\n    0;\n\n  const _date = toDate(date, options?.in);\n  const day = _date.getDay();\n  const diff = (day < weekStartsOn ? 7 : 0) + day - weekStartsOn;\n\n  _date.setDate(_date.getDate() - diff);\n  _date.setHours(0, 0, 0, 0);\n  return _date;\n}\n\n// Fallback for modularized imports:\nexport default startOfWeek;\n", "import { isDate } from \"./isDate.js\";\nimport { toDate } from \"./toDate.js\";\n\n/**\n * @name isValid\n * @category Common Helpers\n * @summary Is the given date valid?\n *\n * @description\n * Returns false if argument is Invalid Date and true otherwise.\n * Argument is converted to Date using `toDate`. See [toDate](https://date-fns.org/docs/toDate)\n * Invalid Date is a Date, whose time value is NaN.\n *\n * Time value of Date: http://es5.github.io/#x15.9.1.1\n *\n * @param date - The date to check\n *\n * @returns The date is valid\n *\n * @example\n * // For the valid date:\n * const result = isValid(new Date(2014, 1, 31))\n * //=> true\n *\n * @example\n * // For the value, convertible into a date:\n * const result = isValid(1393804800000)\n * //=> true\n *\n * @example\n * // For the invalid date:\n * const result = isValid(new Date(''))\n * //=> false\n */\nexport function isValid(date) {\n  return !((!isDate(date) && typeof date !== \"number\") || isNaN(+toDate(date)));\n}\n\n// Fallback for modularized imports:\nexport default isValid;\n", "/**\n * @module constants\n * @summary Useful constants\n * @description\n * Collection of useful date constants.\n *\n * The constants could be imported from `date-fns/constants`:\n *\n * ```ts\n * import { maxTime, minTime } from \"./constants/date-fns/constants\";\n *\n * function isAllowedTime(time) {\n *   return time <= maxTime && time >= minTime;\n * }\n * ```\n */\n\n/**\n * @constant\n * @name daysInWeek\n * @summary Days in 1 week.\n */\nexport const daysInWeek = 7;\n\n/**\n * @constant\n * @name daysInYear\n * @summary Days in 1 year.\n *\n * @description\n * How many days in a year.\n *\n * One years equals 365.2425 days according to the formula:\n *\n * > Leap year occurs every 4 years, except for years that are divisible by 100 and not divisible by 400.\n * > 1 mean year = (365+1/4-1/100+1/400) days = 365.2425 days\n */\nexport const daysInYear = 365.2425;\n\n/**\n * @constant\n * @name maxTime\n * @summary Maximum allowed time.\n *\n * @example\n * import { maxTime } from \"./constants/date-fns/constants\";\n *\n * const isValid = 8640000000000001 <= maxTime;\n * //=> false\n *\n * new Date(8640000000000001);\n * //=> Invalid Date\n */\nexport const maxTime = Math.pow(10, 8) * 24 * 60 * 60 * 1000;\n\n/**\n * @constant\n * @name minTime\n * @summary Minimum allowed time.\n *\n * @example\n * import { minTime } from \"./constants/date-fns/constants\";\n *\n * const isValid = -8640000000000001 >= minTime;\n * //=> false\n *\n * new Date(-8640000000000001)\n * //=> Invalid Date\n */\nexport const minTime = -maxTime;\n\n/**\n * @constant\n * @name millisecondsInWeek\n * @summary Milliseconds in 1 week.\n */\nexport const millisecondsInWeek = 604800000;\n\n/**\n * @constant\n * @name millisecondsInDay\n * @summary Milliseconds in 1 day.\n */\nexport const millisecondsInDay = 86400000;\n\n/**\n * @constant\n * @name millisecondsInMinute\n * @summary Milliseconds in 1 minute\n */\nexport const millisecondsInMinute = 60000;\n\n/**\n * @constant\n * @name millisecondsInHour\n * @summary Milliseconds in 1 hour\n */\nexport const millisecondsInHour = 3600000;\n\n/**\n * @constant\n * @name millisecondsInSecond\n * @summary Milliseconds in 1 second\n */\nexport const millisecondsInSecond = 1000;\n\n/**\n * @constant\n * @name minutesInYear\n * @summary Minutes in 1 year.\n */\nexport const minutesInYear = 525600;\n\n/**\n * @constant\n * @name minutesInMonth\n * @summary Minutes in 1 month.\n */\nexport const minutesInMonth = 43200;\n\n/**\n * @constant\n * @name minutesInDay\n * @summary Minutes in 1 day.\n */\nexport const minutesInDay = 1440;\n\n/**\n * @constant\n * @name minutesInHour\n * @summary Minutes in 1 hour.\n */\nexport const minutesInHour = 60;\n\n/**\n * @constant\n * @name monthsInQuarter\n * @summary Months in 1 quarter.\n */\nexport const monthsInQuarter = 3;\n\n/**\n * @constant\n * @name monthsInYear\n * @summary Months in 1 year.\n */\nexport const monthsInYear = 12;\n\n/**\n * @constant\n * @name quartersInYear\n * @summary Quarters in 1 year\n */\nexport const quartersInYear = 4;\n\n/**\n * @constant\n * @name secondsInHour\n * @summary Seconds in 1 hour.\n */\nexport const secondsInHour = 3600;\n\n/**\n * @constant\n * @name secondsInMinute\n * @summary Seconds in 1 minute.\n */\nexport const secondsInMinute = 60;\n\n/**\n * @constant\n * @name secondsInDay\n * @summary Seconds in 1 day.\n */\nexport const secondsInDay = secondsInHour * 24;\n\n/**\n * @constant\n * @name secondsInWeek\n * @summary Seconds in 1 week.\n */\nexport const secondsInWeek = secondsInDay * 7;\n\n/**\n * @constant\n * @name secondsInYear\n * @summary Seconds in 1 year.\n */\nexport const secondsInYear = secondsInDay * daysInYear;\n\n/**\n * @constant\n * @name secondsInMonth\n * @summary Seconds in 1 month\n */\nexport const secondsInMonth = secondsInYear / 12;\n\n/**\n * @constant\n * @name secondsInQuarter\n * @summary Seconds in 1 quarter.\n */\nexport const secondsInQuarter = secondsInMonth * 3;\n\n/**\n * @constant\n * @name constructFromSymbol\n * @summary Symbol enabling Date extensions to inherit properties from the reference date.\n *\n * The symbol is used to enable the `constructFrom` function to construct a date\n * using a reference date and a value. It allows to transfer extra properties\n * from the reference date to the new date. It's useful for extensions like\n * [`TZDate`](https://github.com/date-fns/tz) that accept a time zone as\n * a constructor argument.\n */\nexport const constructFromSymbol = Symbol.for(\"constructDateFrom\");\n"], "names": ["getTime", "date", "toDate", "isWithinInterval", "interval", "options", "time", "in", "startTime", "endTime", "start", "end", "sort", "a", "b", "isBefore", "dateToCompare", "getSeconds", "getDate", "addHours", "amount", "addMilliseconds", "millisecondsInHour", "addLeadingZeros", "number", "targetLength", "output", "Math", "abs", "toString", "padStart", "sign", "lightFormatters", "y", "token", "signedYear", "getFullYear", "year", "length", "M", "month", "getMonth", "String", "d", "dayPeriodEnumValue", "getHours", "toUpperCase", "h", "H", "m", "getMinutes", "s", "S", "numberOfDigits", "trunc", "milliseconds", "getMilliseconds", "fractionalSeconds", "pow", "midnight", "noon", "morning", "afternoon", "evening", "night", "formatters", "G", "localize", "era", "width", "ordinalNumber", "unit", "Y", "signedWeekYear", "getWeekYear", "weekYear", "twoDigitYear", "getISOWeekYear", "isoWeekYear", "u", "Q", "quarter", "ceil", "context", "q", "L", "w", "week", "getWeek", "isoWeek", "getISOWeek", "D", "dayOfYear", "_date", "diff", "differenceInCalendarDays", "startOfYear", "E", "dayOfWeek", "getDay", "day", "e", "localDayOfWeek", "weekStartsOn", "c", "i", "isoDayOfWeek", "<PERSON><PERSON><PERSON><PERSON>", "toLowerCase", "hours", "dayPeriodEnum", "B", "K", "X", "_localize", "timezoneOffset", "getTimezoneOffset", "formatTimezoneWithOptionalMinutes", "formatTimezone", "x", "O", "formatTimezoneShort", "z", "t", "timestamp", "T", "offset", "delimiter", "absOffset", "minutes", "formattingTokensRegExp", "longFormattingTokensRegExp", "escapedStringRegExp", "doubleQuoteRegExp", "unescapedLatinCharacterRegExp", "format", "formatStr", "defaultOptions", "getDefaultOptions", "locale", "defaultLocale", "firstWeekContainsDate", "originalDate", "<PERSON><PERSON><PERSON><PERSON>", "RangeError", "parts", "match", "map", "firstCharacter", "substring", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "longFormatters", "formatLong", "join", "isToken", "value", "cleanEscapedString", "matched", "input", "replace", "preprocessor", "formatterOptions", "part", "useAdditionalWeekYearTokens", "isProtectedWeekYearToken", "useAdditionalDayOfYearTokens", "isProtectedDayOfYearToken", "warnOrThrowProtectedError", "formatter", "isSameMonth", "laterDate", "earlierDate", "laterDate_", "earlierDate_", "normalizeDates", "startOfDay", "setHours", "constructFrom", "constructFromSymbol", "Date", "constructor", "round", "startOfWeek", "startOfWeekYear", "firstWeek", "setFullYear", "millisecondsInWeek", "addMinutes", "setTime", "millisecondsInMinute", "max", "dates", "result", "for<PERSON>ach", "bind", "date_", "isNaN", "NaN", "setMinutes", "startOfMonth", "setDate", "subQuarters", "addQuarters", "parseISO", "argument", "invalidDate", "additionalDigits", "dateStrings", "splitDateString", "timeString", "array", "dateString", "split", "patterns", "dateTimeDelimiter", "test", "timeZoneDelimiter", "substr", "timezone", "exec", "parseYearResult", "parseYear", "regex", "captures", "restDateString", "parseInt", "century", "slice", "dateRegex", "isWeekDate", "parseDateUnit", "_year", "validateWeekDate", "dayOfISOWeekYear", "setUTCFullYear", "fourthOfJanuaryDay", "getUTCDay", "setUTCDate", "getUTCDate", "validateDate", "validateDayOfYearDate", "daysInMonths", "isLeapYearIndex", "parseTime", "timeRegex", "parseTimeUnit", "seconds", "validateTime", "parseTimezone", "_hours", "timezoneString", "timezoneRegex", "validateTimezone", "tmpDate", "getUTCFullYear", "getUTCMonth", "getUTCHours", "getUTCMinutes", "getUTCSeconds", "getUTCMilliseconds", "parseFloat", "setQuarter", "oldQuarter", "setMonth", "endOfMonth", "min", "endOfDay", "setSeconds", "endOfYear", "addDays", "midMonth", "daysInMonth", "getDaysInMonth", "monthIndex", "lastDayOfMonth", "startOfISOWeek", "startOfISOWeekYear", "fourthOfJanuary", "differenceInDays", "compareLocalAsc", "difference", "isLastDayNotFull", "Number", "subMonths", "addMonths", "<PERSON>ter", "validate", "_utcDate", "_options", "subPriority", "ValueSetter", "validate<PERSON><PERSON>ue", "set", "flags", "setValue", "priority", "DateTimezoneSetter", "timestampIsSet", "transpose", "isConstructor", "prototype", "reference", "TIMEZONE_UNIT_PRIORITY", "<PERSON><PERSON><PERSON>", "run", "parse", "setter", "rest", "_value", "<PERSON><PERSON><PERSON><PERSON>", "incompatibleTokens", "numericPatterns", "hour23h", "hour24h", "hour11h", "hour12h", "minute", "second", "singleDigit", "twoDigits", "threeDigits", "fourDigits", "anyDigitsSigned", "singleDigitSigned", "twoDigitsSigned", "threeDigitsSigned", "fourDigitsSigned", "basicOptionalMinutes", "basic", "basicOptionalSeconds", "extended", "extendedOptionalSeconds", "mapValue", "parseFnResult", "mapFn", "parseNumericPattern", "pattern", "matchResult", "parseTimezonePattern", "millisecondsInSecond", "parseAnyDigitsSigned", "parseNDigits", "n", "parseNDigitsSigned", "dayPeriodEnumToHours", "normalizeTwoDigitYear", "currentYear", "isCommonEra", "absCurrentYear", "rangeEnd", "rangeEndCentury", "isPreviousCentury", "<PERSON><PERSON><PERSON><PERSON>", "valueCallback", "isTwoDigitYear", "normalizedTwoDigitYear", "LocalWeekYearParser", "ISOWeekYearParser", "_flags", "firstWeekOfYear", "<PERSON><PERSON>ear<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "StandAloneQuarterParser", "<PERSON><PERSON><PERSON><PERSON>", "StandAloneMonthParser", "LocalWeekParser", "setWeek", "ISOWeekParser", "setISOWeek", "DAYS_IN_MONTH", "DAYS_IN_MONTH_LEAP_YEAR", "<PERSON><PERSON><PERSON><PERSON>", "isLeapYear", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "subpriority", "setDay", "currentDay", "delta", "<PERSON><PERSON><PERSON><PERSON>", "LocalDayParser", "wholeWeekDays", "floor", "StandAloneLocalDayParser", "ISODayParser", "setISODay", "AMPM<PERSON><PERSON><PERSON>", "AMPMMidnightParser", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Hour1to12<PERSON><PERSON><PERSON>", "isPM", "Hour0to23Parser", "Hour0To11Parser", "Hour1To24Parser", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Second<PERSON><PERSON><PERSON>", "FractionOfSecondParser", "setMilliseconds", "ISOTimezoneWithZParser", "timezonePatterns", "getTimezoneOffsetInMilliseconds", "ISOTimezoneParser", "TimestampSecondsParser", "TimestampMillisecondsParser", "parsers", "I", "k", "notWhitespaceRegExp", "dateStr", "referenceDate", "Object", "assign", "getInternalDefaultOptions", "subFnOptions", "setters", "tokens", "usedTokens", "parser", "Array", "isArray", "incompatibleToken", "find", "includes", "usedToken", "fullToken", "push", "parseResult", "indexOf", "uniquePrioritySetters", "filter", "index", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "getQuarter", "firstWeekOfNextYear", "startOfNextYear", "firstWeekOfThisYear", "startOfThisYear", "differenceInCalendarQuarters", "yearsDiff", "quartersDiff", "isSameYear", "formatDistanceLocale", "lessThanXSeconds", "one", "other", "xSeconds", "halfAMinute", "lessThanXMinutes", "xMinutes", "aboutXHours", "xHours", "xDays", "aboutXWeeks", "xWeeks", "aboutXMonths", "xMonths", "aboutXYears", "xYears", "overXYears", "almostXYears", "buildFormatLongFn", "args", "defaultWidth", "formats", "full", "long", "medium", "short", "timeFormats", "dateTime", "lastWeek", "yesterday", "today", "tomorrow", "nextWeek", "buildLocalizeFn", "valuesArray", "formattingValues", "defaultFormattingWidth", "values", "argument<PERSON>allback", "buildMatchFn", "string", "matchPattern", "matchPatterns", "defaultMatchWidth", "matchedString", "parsePatterns", "defaultParseWidth", "key", "findIndex", "predicate", "<PERSON><PERSON><PERSON>", "object", "hasOwnProperty", "call", "enUS", "code", "formatDistance", "count", "tokenValue", "addSuffix", "comparison", "formatRelative", "_baseDate", "formatRelativeLocale", "dirtyNumber", "rem100", "narrow", "abbreviated", "wide", "quarterValues", "month<PERSON><PERSON><PERSON>", "dayV<PERSON><PERSON>", "am", "pm", "buildMatchPatternFn", "parsePattern", "matchOrdinalNumberPattern", "parseOrdinalNumberPattern", "matchEraPatterns", "parseEraPatterns", "matchQuarterPatterns", "parseQuarterPatterns", "parseMonthPatterns", "any", "parseDayPatterns", "matchDayPeriodPatterns", "parseDayPeriodPatterns", "subYears", "addYears", "isSameDay", "dateLeft_", "dateRight_", "differenceInCalendarMonths", "monthsDiff", "normalize", "setYear", "utcDate", "UTC", "isAfter", "subDays", "dayOfMonth", "endOfDesiredMonth", "startOfQuarter", "currentMonth", "subWeeks", "addWeeks", "addSeconds", "fourthOfJanuaryOfNextYear", "fourthOfJanuaryOfThisYear", "getYear", "isEqual", "leftDate", "rightDate", "isSameQuarter", "endOfWeek", "differenceInCalendarYears", "isDate", "laterStartOfDay", "earlierStartOfDay", "laterTimestamp", "earlierTimestamp", "millisecondsInDay", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "time<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "p", "P", "dateTimeFormat", "datePattern", "timePattern", "dayOfYearTokenRE", "weekYearTokenRE", "throwTokens", "_message", "message", "subject", "console", "warn", "Symbol", "for"], "sourceRoot": "", "ignoreList": [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 66, 67, 68, 69, 70, 71, 72, 73, 74, 75, 76, 77, 78, 79, 80, 81, 82, 83, 84, 85, 86, 87, 88, 89, 90, 91, 92, 93, 94, 95, 96, 97, 98, 99, 100, 101, 102, 103, 104, 105, 106, 107, 108, 109, 110, 111, 112, 113, 114, 115, 116, 117, 118, 119, 120, 121, 122, 123, 124, 125, 126, 127, 128, 129, 130, 131, 132, 133]}