"use strict";(()=>{var e={};e.id=8825,e.ids=[636,3220,8825],e.modules={123:e=>{e.exports=require("dom-helpers/removeEventListener")},1332:e=>{e.exports=require("react-custom-scrollbars-2")},1428:e=>{e.exports=import("axios")},1680:e=>{e.exports=require("@restart/ui/utils")},1919:e=>{e.exports=require("react-transition-group/Transition")},3892:e=>{e.exports=require("classnames")},4048:e=>{e.exports=require("react-truncate")},6009:e=>{e.exports=require("dom-helpers/ownerDocument")},6952:e=>{e.exports=require("@restart/ui/Modal")},7374:e=>{e.exports=require("@restart/ui/Dropdown")},8732:e=>{e.exports=require("react/jsx-runtime")},9653:e=>{e.exports=require("@restart/ui/Overlay")},11242:e=>{e.exports=require("redux-persist/integration/react")},11688:e=>{e.exports=require("@restart/hooks/useWillUnmount")},12388:e=>{e.exports=require("dom-helpers/addEventListener")},13364:e=>{e.exports=require("redux-saga/effects")},14062:e=>{e.exports=import("react-redux")},14078:e=>{e.exports=import("swr")},14332:e=>{e.exports=require("uncontrollable")},14845:(e,r,t)=>{t.a(e,async(e,s)=>{try{t.r(r),t.d(r,{default:()=>x});var a=t(8732),o=t(82015),i=t(91353),n=t(12403),u=t(42893),p=t(63487),l=t(88751),c=t(56084),d=e([u,p]);[u,p]=d.then?(await d)():d;let x=function(e){let[r,t]=(0,o.useState)([]),[,s]=(0,o.useState)(!1),[d,x]=(0,o.useState)(0),[m,g]=(0,o.useState)(10),[q,h]=(0,o.useState)(!1),[P,v]=(0,o.useState)(""),[A,S]=(0,o.useState)({}),{t:f}=(0,l.useTranslation)("common"),b={sort:{created_at:"desc"},limit:m,page:1,query:{status:"Request Pending"}},w=[{name:f("Title"),selector:"title",cell:e=>e.title},{name:f("Email"),selector:"email",cell:e=>e.email},{name:"ContactName",selector:"contact_name",cell:e=>e.contact_name},{name:f("Action"),selector:"",cell:e=>(0,a.jsxs)("div",{children:[(0,a.jsx)(i.A,{variant:"primary",size:"sm",onClick:()=>C(e,"approve"),children:f("instu.Approves")}),"\xa0",(0,a.jsx)(i.A,{variant:"secondary",size:"sm",onClick:()=>C(e,"reject"),children:f("instu.Reject")})]})}],j=async()=>{s(!0);let e=await p.A.get("/institution",b);e&&e.data&&(t(e.data),x(e.totalCount),s(!1))},y=async(e,r)=>{b.limit=e,b.page=r,s(!0);let a=await p.A.get("/institution",b);a&&a.data&&a.data.length>0&&(t(a.data),g(e),s(!1))},C=async(e,r)=>{h(!0),v(r),e&&e._id&&S({...e,status:"approve"===r?"Approved":"Rejected"})},M=async()=>{if("Rejected"===A.status)await p.A.remove(`/institution/${A._id}`),j(),u.default.error(f("instu.Rejected")),S({}),h(!1);else{let e=await p.A.patch(`/institution/${A._id}`,A);if(e&&403===e.status)return void u.default.error(e.response&&e.response.message?e.response.message:f("adminsetting.FocalPointsApprovalTable.Somethingwentswrong"));j(),u.default.success(f("instu.Approve")),S({}),h(!1)}j(),S({}),h(!1)},_=()=>h(!1);return(0,a.jsxs)("div",{children:[(0,a.jsxs)(n.A,{show:q,onHide:_,children:[(0,a.jsx)(n.A.Header,{closeButton:!0,children:(0,a.jsxs)(n.A.Title,{children:[P.charAt(0).toUpperCase()+P.slice(1)," ",f("Organisation")]})}),(0,a.jsxs)(n.A.Body,{children:[f("adminsetting.FocalPointsApprovalTable.Areyousurewantto"),"  ",P," ",f("thisOrganisation?")]}),(0,a.jsxs)(n.A.Footer,{children:[(0,a.jsx)(i.A,{variant:"secondary",onClick:_,children:f("cancel")}),(0,a.jsx)(i.A,{variant:"primary",onClick:M,children:f("yes")})]})]}),(0,a.jsx)(c.A,{columns:w,data:r,totalRows:d,pagServer:!0,handlePerRowsChange:y,handlePageChange:e=>{b.limit=m,b.page=e,j()}})]})};s()}catch(e){s(e)}})},16116:e=>{e.exports=require("invariant")},18622:e=>{e.exports=require("yup")},19121:e=>{e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},22326:e=>{e.exports=require("react-dom")},22541:e=>{e.exports=require("redux-persist/lib/storage")},25303:e=>{e.exports=require("@restart/ui/NavItem")},26324:e=>{e.exports=require("warning")},27825:e=>{e.exports=require("lodash")},27910:e=>{e.exports=require("stream")},28217:e=>{e.exports=require("@restart/ui/DropdownItem")},29021:e=>{e.exports=require("fs")},29294:e=>{e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},29628:e=>{e.exports=require("dom-helpers/addClass")},29825:e=>{e.exports=require("prop-types")},29841:e=>{e.exports=require("dom-helpers/hasClass")},30362:e=>{e.exports=require("@restart/ui/DropdownToggle")},33873:e=>{e.exports=require("path")},36653:e=>{e.exports=require("nprogress")},36955:e=>{e.exports=require("@restart/hooks/useBreakpoint")},37766:e=>{e.exports=require("@restart/hooks/useMergedRefs")},38609:e=>{e.exports=require("react-data-table-component")},39756:e=>{e.exports=import("redux")},40051:e=>{e.exports=require("dom-helpers/removeClass")},40361:e=>{e.exports=require("next/dist/compiled/next-server/pages.runtime.prod.js")},42893:e=>{e.exports=import("react-hot-toast")},43294:e=>{e.exports=require("formik")},50009:e=>{e.exports=require("@restart/ui/DropdownMenu")},56084:(e,r,t)=>{t.d(r,{A:()=>p});var s=t(8732);t(82015);var a=t(38609),o=t.n(a),i=t(88751),n=t(30370);function u(e){let{t:r}=(0,i.useTranslation)("common"),t={rowsPerPageText:r("Rowsperpage")},{columns:a,data:u,totalRows:p,resetPaginationToggle:l,subheader:c,subHeaderComponent:d,handlePerRowsChange:x,handlePageChange:m,rowsPerPage:g,defaultRowsPerPage:q,selectableRows:h,loading:P,pagServer:v,onSelectedRowsChange:A,clearSelectedRows:S,sortServer:f,onSort:b,persistTableHead:w,sortFunction:j,...y}=e,C={paginationComponentOptions:t,noDataComponent:r("NoData"),noHeader:!0,columns:a,data:u||[],dense:!0,paginationResetDefaultPage:l,subHeader:c,progressPending:P,subHeaderComponent:d,pagination:!0,paginationServer:v,paginationPerPage:q||10,paginationRowsPerPageOptions:g||[10,15,20,25,30],paginationTotalRows:p,onChangeRowsPerPage:x,onChangePage:m,selectableRows:h,onSelectedRowsChange:A,clearSelectedRows:S,progressComponent:(0,s.jsx)(n.A,{}),sortIcon:(0,s.jsx)("i",{className:"sort-icon fas fa-exchange-alt"}),sortServer:f,onSort:b,sortFunction:j,persistTableHead:w,className:"rki-table"};return(0,s.jsx)(o(),{...C})}u.defaultProps={subHeader:!1,pagination:!0,totalRows:null,pagServer:!0,onSelectedRowsChange:null,clearSelectedRows:!1,sortServer:!1,persistTableHead:!1};let p=u},57664:e=>{e.exports=require("dom-helpers/scrollbarSize")},58928:e=>{e.exports=require("dom-helpers/canUseDOM")},59717:e=>{e.exports=require("@restart/ui/DropdownContext")},59727:(e,r,t)=>{t.a(e,async(e,s)=>{try{t.r(r),t.d(r,{config:()=>q,default:()=>d,getServerSideProps:()=>g,getStaticPaths:()=>m,getStaticProps:()=>x,reportWebVitals:()=>h,routeModule:()=>b,unstable_getServerProps:()=>S,unstable_getServerSideProps:()=>f,unstable_getStaticParams:()=>A,unstable_getStaticPaths:()=>v,unstable_getStaticProps:()=>P});var a=t(63885),o=t(80237),i=t(81413),n=t(9616),u=t.n(n),p=t(72386),l=t(14845),c=e([p,l]);[p,l]=c.then?(await c)():c;let d=(0,i.M)(l,"default"),x=(0,i.M)(l,"getStaticProps"),m=(0,i.M)(l,"getStaticPaths"),g=(0,i.M)(l,"getServerSideProps"),q=(0,i.M)(l,"config"),h=(0,i.M)(l,"reportWebVitals"),P=(0,i.M)(l,"unstable_getStaticProps"),v=(0,i.M)(l,"unstable_getStaticPaths"),A=(0,i.M)(l,"unstable_getStaticParams"),S=(0,i.M)(l,"unstable_getServerProps"),f=(0,i.M)(l,"unstable_getServerSideProps"),b=new a.PagesRouteModule({definition:{kind:o.A.PAGES,page:"/adminsettings/approval/InstitutionTable",pathname:"/adminsettings/approval/InstitutionTable",bundlePath:"",filename:""},components:{App:p.default,Document:u()},userland:l});s()}catch(e){s(e)}})},60560:e=>{e.exports=require("dom-helpers/contains")},63033:e=>{e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},65447:e=>{e.exports=require("@restart/ui/Button")},67364:e=>{e.exports=require("@restart/hooks/useCallbackRef")},68455:e=>{e.exports=import("redux-saga")},69722:e=>{e.exports=require("es6-promise")},74075:e=>{e.exports=require("zlib")},74716:e=>{e.exports=require("moment")},74987:e=>{e.exports=require("@restart/ui/ModalManager")},78097:e=>{e.exports=require("next-redux-saga")},78634:e=>{e.exports=require("@restart/ui/Anchor")},80237:(e,r)=>{Object.defineProperty(r,"A",{enumerable:!0,get:function(){return t}});var t=function(e){return e.PAGES="PAGES",e.PAGES_API="PAGES_API",e.APP_PAGE="APP_PAGE",e.APP_ROUTE="APP_ROUTE",e.IMAGE="IMAGE",e}({})},81366:e=>{e.exports=require("redux-auth-wrapper/connectedAuthWrapper")},81413:(e,r)=>{Object.defineProperty(r,"M",{enumerable:!0,get:function(){return function e(r,t){return t in r?r[t]:"then"in r&&"function"==typeof r.then?r.then(r=>e(r,t)):"function"==typeof r&&"default"===t?r:void 0}}})},81521:e=>{e.exports=require("dom-helpers/querySelectorAll")},81895:e=>{e.exports=require("@restart/hooks/useEventCallback")},82015:e=>{e.exports=require("react")},86842:e=>{e.exports=require("@restart/ui/SelectableContext")},87571:e=>{e.exports=require("dom-helpers/transitionEnd")},88751:e=>{e.exports=require("next-i18next")},93787:e=>{e.exports=require("redux-persist")},94947:e=>{e.exports=require("@restart/hooks/useTimeout")},96196:e=>{e.exports=require("next-redux-wrapper")},98320:e=>{e.exports=require("@restart/hooks/useIsomorphicEffect")},99460:e=>{e.exports=require("dom-helpers/css")}};var r=require("../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),s=r.X(0,[6089,9216,9616,2386],()=>t(59727));module.exports=s})();