{"version": 3, "file": "static/chunks/9946-1d72664cd0cae2b3.js", "mappings": "gOAiBA,IAAIA,EAAY,EAAE,CAEZC,EAAaC,UAAwB,CAErCC,EAAiB,CACrBC,KAAM,EACNC,QAAS,OACTC,cAAe,SACfC,WAAY,SACZC,eAAgB,SAChBC,MAAO,OACPC,OAAQ,OACRC,YAAa,GACbC,YAAa,UACbC,gBAAiB,UACjBC,MAAO,QACPC,WAAY,0BACd,EAEMC,EAAa,CACjBX,QAAS,cACTY,aAAc,EACdC,OAAQ,iBACRC,aAAc,EACdC,YAAa,GACbX,MAAO,IACPC,OAAQ,IACRW,QAAS,EACTC,SAAU,WACVC,UAAW,mCACXC,UAAW,YACb,EAEMC,EAAuB,CAC3BpB,QAAS,OACTC,cAAe,MACfE,eAAgB,aAChBkB,SAAU,OACVC,UAAW,EACb,EAEMC,EAAkB,CACtBvB,QAAS,OACTwB,SAAU,EACVC,SAAU,QACZ,EAEMC,EAAY,CAChBT,SAAU,WACVU,SAAU,OACVC,IAAK,QACLC,MAAO,QACPC,OAAQ,IACRC,OAAQ,UACRvB,gBAAiB,OACjBC,MAAO,OACPG,aAAc,KAChB,EAEMoB,EAAM,CACVhC,QAAS,QACTI,MAAO,OACPC,OAAQ,MACV,EAEM4B,EAAmB,CACvB1B,YAAa,SACf,EA0IA,EAxI4B,IAC1B,GAAM,CAAE2B,CAAC,CAAE,CAAGC,CAAAA,EAAAA,EAAAA,EAAAA,CAAcA,CAAC,QAuIhBC,EAtIP,CAACC,EAAOC,EAAS,CAAGC,CAAAA,EAAAA,EAAAA,MAsIMH,EAtING,CAAQA,CAAQ,EAAE,EAGtCC,EAAc,MAAOC,IACZ,MAAMC,EAAAA,CAAUA,CAACC,MAAM,CAAC,UAAa,OAAHF,GACjD,EAEMG,EAAcC,IAClB,IAAMC,EAAMD,GAAQA,EAAKE,GAAG,CAAG,CAAEC,SAAUH,EAAKE,GAAG,EAAK,CAAEF,KAAMA,CAAK,EAC/DI,EAASC,IAAAA,SAAW,CAACvD,EAAMmD,GACjCN,EAAY7C,CAAI,CAACsD,EAAO,CAACD,QAAQ,EACjCrD,EAAKwD,MAAM,CAACF,EAAQ,GACpBG,EAAMC,QAAQ,CAAC1D,EAAMyD,EAAME,KAAK,CAAGF,EAAME,KAAK,CAAG,GACjD,IAAMC,EAAgB,IAAIlB,EAAM,CAChCkB,EAASJ,MAAM,CAACI,EAASC,OAAO,CAACX,GAAO,GACxCP,EAASiB,EAEX,EAEME,EAAgBZ,GACZ,UAACb,MAAAA,CAAI0B,IAAKb,EAAKc,OAAO,CAAEC,MAAO5B,IAGnC6B,EAAcxB,EAAMyB,GAAG,CAAC,CAACjB,EAAWkB,IAEtC,UAACC,MAAAA,UACC,WAACA,MAAAA,CAAIJ,MAAOjD,YACV,UAACqD,MAAAA,CAAIJ,MAAOrC,WACTkC,EAAaZ,KAGhB,UAACoB,EAAAA,CAAeA,CAAAA,CACdvC,KAAMwC,EAAAA,GAAaA,CACnBN,MAAOlC,EACPjB,MAAM,QACN0D,QAAS,IAAMvB,EAAWC,SAVtBkB,IAiBdK,CAAAA,EAAAA,EAAAA,SAAAA,CAASA,CAAC,KACR/B,EAAMgC,OAAO,CAAC,GAAeC,IAAIC,eAAe,CAAC1B,EAAKc,OAAO,GAC7DhE,EAAO,EAAE,EACR,EAAE,EAELyE,CAAAA,EAAAA,EAAAA,SAAAA,CAASA,CAAC,KAEJhB,GAASA,EAAMoB,KAAK,EAAE,EAOf,IANMpB,EAAMoB,KAAK,CAACV,GAAG,CAAC,CAACW,EAAWC,KAEzC/E,EAAKgF,IAAI,CAAC,CAAE3B,SAAUyB,EAAK1B,GAAG,CAAEO,MAAOF,EAAME,KAAK,CAAGF,EAAME,KAAK,CAAG,CAAE,GAChD,CAAE,GAAGmB,CAAI,CAAEd,QAAS,GAAwCc,MAAAA,CAArC5E,8BAAsB,CAAC,gBAAuB,OAAT4E,EAAK1B,GAAG,CAAG,IAG1E,CAExB,EAAG,CAACK,EAAMoB,KAAK,CAAC,EAEhB,IAAMI,EAAc,MAAOC,EAAqBvB,KAE9C,GAAIuB,EAAeC,MAAM,CAAGxB,EAC1B,GAAI,CACF,CAF+B,GAEzByB,EAAY,IAAIC,SACtBD,EAAKE,MAAM,CAAC,OAAQJ,CAAc,CAACvB,EAAM,EACzC,IAAM4B,EAAM,MAAMxC,EAAAA,CAAUA,CAACyC,IAAI,CAAE,SAASJ,EAAM,CAAE,eAAgB,qBAAsB,GAC1FpF,EAAKgF,IAAI,CAAC,CAAE3B,SAAUkC,EAAInC,GAAG,CAAEF,KAAMgC,CAAc,CAACvB,EAAM,CAAEA,MAAOF,EAAME,KAAK,CAAGF,EAAME,KAAK,CAAG,CAAE,GACjGsB,EAAYC,EAAgBvB,EAAQ,EACtC,CAAE,MAAO8B,EAAO,CACdR,EAAYC,EAAgBvB,EAAQ,EACtC,MAEAF,EAAMC,QAAQ,CAAC1D,EAAMyD,EAAME,KAAK,CAAGF,EAAME,KAAK,CAAG,EAErD,EAEM+B,EAASC,CAAAA,EAAAA,EAAAA,WAAAA,CAAWA,CAAC,IACzBV,EAAYW,EAAY,GAMxBjD,EAAS,IALQiD,EAAWzB,GAAG,CAAC,CAACjB,EAAWkB,IAC1CyB,OAAOC,MAAM,CAAC5C,EAAM,CAClBc,QAASW,IAAIoB,eAAe,CAAC7C,EAC/B,IAEoB,CACxB,EAAG,EAAE,EAEC,cACJ8C,CAAY,eACZC,CAAa,cACbC,CAAY,CACZC,cAAY,cACZC,CAAY,gBACZC,CAAc,CACf,CAAGC,CAAAA,EAAAA,EAAAA,EAAAA,CAAWA,CAAC,CACdC,OAAQ,UACRC,UAAU,EACVC,QAAS,EACTC,QAASzG,SACTyF,CACF,GAEMzB,EAAQ0C,CAAAA,EAAAA,EAAAA,OAAAA,CAAOA,CACnB,IAAO,EACL,GAAGxG,CAAS,CACZ,GAAI+F,EAAe5D,EAAc,CAAEsE,QAAS,iBAAkB,CAAC,CAC/D,GAAIT,EACA,CAAES,QAAS,oBAAqB,EAChC,CAAEA,QAAS,iBAAkB,CAAC,CAClC,GAAIR,EAAe,CAAEQ,QAAS,gBAAiB,EAAI,aAACtE,CAAW,CAAC,CAClE,EACA,CAAC4D,EAAcE,EAAa,EAGxBS,EAAiBR,EAAelB,MAAM,CAAG,GAAKkB,CAAc,CAAC,EAAE,CAACnD,IAAI,CAAC4D,IAAI,CAAG7G,EAClF,MACE,iCACE,UAACoE,MAAAA,CAAI0C,UAAU,yDAAyD9C,MAAO,CAAExD,MAAO,OAAQC,OAAQ,OAAQ,WAC9G,WAAC2D,MAAAA,CAAK,GAAG2B,EAAa,CAAE/B,OAAM,EAAE,WAC9B,UAAC+C,QAAAA,CAAO,GAAGf,GAAe,GAC1B,UAAC3B,EAAAA,CAAeA,CAAAA,CAACvC,KAAMkF,EAAAA,GAAgBA,CAAEH,KAAK,KAAKhG,MAAM,SACzD,UAACoG,IAAAA,CAAEjD,MAAO,CAAEnD,MAAO,UAAWK,aAAc,KAAM,WAAIoB,EAAE,mDACxD,UAAC4E,QAAAA,CAAMlD,MAAO,CAAEnD,MAAO,SAAU,WAAIyB,EAAE,oBACvC,WAAC4E,QAAAA,CAAMlD,MAAO,CAAEnD,MAAO,SAAU,YAAG,UAACsG,IAAAA,UAAG7E,EAAE,WAAa,IAAEA,EAAE,mCAE1DsE,GAAkB,WAACM,QAAAA,CAAMJ,UAAU,6BAAmB,IAAC,UAACzC,EAAAA,CAAeA,CAAAA,CAACvC,KAAMsF,EAAAA,GAAmBA,CAAEP,KAAK,KAAKhG,MAAM,QAAQ,IAAEyB,EAAE,2CAC/H6D,GAAgB,WAACe,QAAAA,CAAMJ,UAAU,cAAc9C,MAAO,CAAEnD,MAAO,SAAU,YAAG,UAACwD,EAAAA,CAAeA,CAAAA,CAACvC,KAAMsF,EAAAA,GAAmBA,CAAEP,KAAK,KAAKhG,MAAM,QAAQ,IAAEyB,EAAE,mCAGzJ,UAAC8B,MAAAA,CAAIJ,MAAOxC,WAAkByC,MAGpC,uRC+LA,MAvYmB,IACf,GAAM,CAAE3B,CAAC,MAAE+E,CAAI,CAAE,CAAG9E,CAAAA,EAAAA,EAAAA,EAAAA,CAAcA,CAAC,CAsYxB+E,SArYLC,CAqYeD,CArYDD,CAqYE,CArYGG,QAAQ,EAAIH,SAAKG,QAAQ,CAAY,KAAOH,EAAKG,QAAQ,CAC5EC,EAAiB,CACnBC,MAAO,CACHC,GAAI,GACJC,GAAI,GACJC,GAAI,EACR,EACAC,YAAa,GACbC,YAAa,CAAC,EACdC,SAAS,EACTC,QAAS,KACTC,eAAgB,GAChBC,WAAY,EAAE,EAEZC,EAAU,UACZ,IAAMC,EAA+B,MAAMvF,EAAAA,CAAUA,CAACwF,GAAG,CAAC,YAAaC,GACnEF,GACAG,EAAQH,EAASI,GADP,CACW,CAE7B,EAEMF,EAAa,CACfG,MAAO,CAAC,EACRC,KAAM,CAAEjB,MAAO,KAAM,EACrB1H,MAAO,IACP4I,OAAQ,8BACZ,EAEM,CAACC,EAAYC,EAAc,CAAGnG,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,CAAM8E,GAC5C,CAACsB,EAAoBC,EAAsB,CAAGrG,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,CAAQ,EAAE,EAChE,EAAGsG,EAAiB,CAAGtG,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,CAAQ,EAAE,EACzC,CAACuG,EAAYC,EAAc,CAAGxG,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,CAAe,EAAE,EACvD,CAACyG,EAAMZ,EAAQ,CAAG7F,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,CAAQ,EAAE,EACpC,CAAC0G,EAAQC,EAAU,CAAG3G,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,CAChC0E,EAAKG,QAAQ,EAAsB,OAAlBH,EAAKG,QAAQ,EAA6B,OAAhBD,EAAuB,KAAOA,GAGvEgC,EAAoB/F,EAAMgG,MAAM,EAAIhG,kBAAMgG,MAAM,CAAC,EAAE,EAAsBhG,EAAMgG,MAAM,CAAC,EAAE,CAExFC,EAAe,MAAOC,EAAYC,SAwDhCtB,EACAuB,EAxDJF,EAAMG,cAAc,GAEpB,IAAMC,EAAaH,GAAUd,EACzBkB,EAAmB,CAAC,EACpBC,EAAkB,CAAC,EACvBZ,GACIA,EAAKlF,GAAG,CAAC,CAACW,EAAMC,KACZ,IAAMmF,EAAWpF,EAAKqF,IAAI,CAC1BH,EAAc,CACV,GAAGA,CAAW,CACd,CAACE,EAAS,CAAEH,EAAWpC,KAAK,CAACuC,EAAS,EAE1CD,EAAa,CACT,GAAGA,CAAU,CACb,CAACC,EAAS,CAAEH,EAAW/B,WAAW,CAACkC,EAAS,CAEpD,GAWAF,EAAY,EAAK,EAAE,CACnBA,EAAY,EADD,CACSA,EAAY,EAAK,CACrCA,CADW,CACC,EAAK,CAAGA,CADW,CACC,EAAK,EAGrCA,EAAY,EAAK,CAHc,CAGZ,CACnBA,EAAY,EADD,CACSA,EAAY,EAAK,CACrCA,CADW,CACC,EAAK,CAAGA,CADW,CACC,EAAK,EAA1B,EAGC,EAAK,CAHc,CAGZ,CACnBA,EAAY,EADD,CACSA,EAAY,EAAK,CACrCA,CADW,CACC,EAAK,CAAGA,CADW,CACC,EAAK,EAA1B,IAET7G,CAF6B,CAEvB,CACRwE,MAAO,CACH,GAAGqC,CACP,EACAjC,YAAagC,EAAWhC,WAAW,CACnCC,YAAa,CACT,GAAGiC,CAAU,EAEjBhC,QAAS8B,EAAW9B,OAAO,CAC3BC,QAAS6B,EAAW7B,OAAO,CAC3BC,eAAgB4B,EAAW5B,cAAc,CACzCC,WAAY2B,EAAW3B,UAAU,EAKjCoB,GACAK,EAAW,KADD,yBAEVvB,EAAW,MAAMvF,EAAAA,CAAUA,CAACqH,KAAK,CAAC,WAA2B,OAAhB3G,EAAMgG,MAAM,CAAC,EAAE,EAAItG,KAEhE0G,EAAW,4BACXvB,EAAW,MAAMvF,EAAAA,CAAUA,CAACyC,IAAI,CAAC,UAAWrC,IAE5CmF,GAAYA,EAASlF,GAAG,EAAE,EAC1BiH,EAAKA,CAACC,OAAO,CAAC/H,EAAEsH,IAChBU,IAAAA,IAAW,CAAC,0BAERjC,OAAAA,EAAAA,KAAAA,EAAAA,EAAUkC,SAAS,CAAnBlC,GAAwB,KACxB+B,EAD+B,EAC1BA,CAAC5E,KAAK,CAAClD,EAAE,yBAEd8H,EAAAA,EAAKA,CAAC5E,KAAK,CAAC6C,EAGxB,EAEMmC,EAAiB,IACnBlB,EAAUzE,EAAKqF,IAAI,CACvB,EAUMO,EAAe,IACjB,GAAIC,EAAEC,MAAM,CAAE,CACV,GAAM,MAAEC,CAAI,OAAEC,CAAK,CAAE,CAAGH,EAAEC,MAAM,CAChC7B,EAAc,GAAqB,EAC/B,GAAGgC,CAAS,CACZ,CAACF,CAF8B,CAEzB,CAAEC,EACZ,EACJ,CACJ,EAqBME,EAAkB,MAAOC,IAC3B,IAAM3C,EAAsC,MAAMvF,EAAAA,CAAUA,CAACwF,GAAG,CAAC,cAAe0C,GAC5E3C,GACAc,EAAcd,EAASI,GADb,CACiB,CAEnC,EAEMwC,EAAoB,IACtB,IAAMC,EAAW,CACb,GAAGrC,EAAWd,WAAW,CACzB,CAACsB,EAAO,CAAEwB,CACd,EACA/B,EAAc,GAAqB,EAC/B,GAAGgC,CAAS,CACZ/C,EAF+B,UAElBmD,EACjB,EACJ,EAEMC,EAAgB,MAAOH,IACzB,IAAM3C,EAAgB,MAAMvF,EAAAA,CAAUA,CAACwF,GAAG,CAAC,WAA2B,OAAhB9E,EAAMgG,MAAM,CAAC,EAAE,EAAIwB,GAEzE,GAAI3C,EAAU,CACV,GAAM,aAAEP,CAAW,SAAEG,CAAO,CAAE,CAAGI,EACjCA,EAASP,WAAW,CAAGA,GAAeA,EAAY3E,GAAG,CAAG2E,EAAY3E,GAAG,CAAG,GAGtEkF,EAASX,KAAK,EAA8B,UAA1B,OAAOW,EAASX,KAAK,CACvCW,EAASX,KAAK,CAAG,CACbC,GAAIU,EAASX,KAAK,CAACC,EAAE,EAAI,GACzBC,GAAIS,EAASX,KAAK,CAACE,EAAE,EAAI,GACzBC,GAAIQ,EAASX,KAAK,CAACG,EAAE,EAAI,EAC7B,EAEAQ,EAASX,KAAK,CAAG,CACbC,GAAI,GACJC,GAAI,GACJC,GAAI,EACR,EAIAQ,EAASN,WAAW,EAAoC,UAAhC,OAAOM,EAASN,WAAW,CACnDM,EAASN,WAAW,CAAG,CACnBJ,GAAIU,EAASN,WAAW,CAACJ,EAAE,EAAI,GAC/BC,GAAIS,EAASN,WAAW,CAACH,EAAE,EAAI,GAC/BC,GAAIQ,EAASN,WAAW,CAACF,EAAE,EAAI,GAC/B,GAAGQ,EAASN,WAAW,EAG3BM,EAASN,WAAW,CAAG,CAAC,EAGb,MAAXE,GACAe,EAAsB,CAACf,EAAQ,EAEnCgB,EAAiBZ,EAASF,UAAU,CAAGE,EAASF,UAAU,CAAG,EAAE,EAC/DW,EAAc,GAAqB,EAAE,GAAGgC,CAAS,CAAE,EAAhB,CAAmBzC,CAAQ,CAAC,EACnE,CACJ,EAEA7D,CAAAA,EAAAA,EAAAA,SAAAA,CAASA,CAAC,KACN4D,IACA,IAAM4C,EAAe,CACjBtC,MAAO,CAAC,EACRC,KAAM,CAAEjB,MAAO,KAAM,EACrB1H,MAAO,GACX,EACIuJ,GACA4B,EAAcH,GAElBD,EAHc,EAIlB,EAAG,EAAE,EAEL,IAAMK,EAAUC,CAAAA,EAAAA,EAAAA,MAAAA,CAAMA,CAAM,MAEtBC,EAAQ,IACV,IAAMnI,EAAMN,EAAGqB,GAAG,CAAEW,GAAcA,EAAKzB,QAAQ,EAC/C0F,EAAc,GAAqB,EAAE,GAAGgC,CAAS,CAAE7C,EAAhB,MAAyB9E,EAAI,EACpE,EAEA,MACI,UAACoI,EAAAA,CAASA,CAAAA,CAACzE,UAAU,WAAW0E,KAAK,aACjC,UAACC,EAAAA,CAAIA,CAAAA,CACDzH,MAAO,CACHtC,UAAW,MACXJ,UAAW,kEACf,WAEA,UAACoK,EAAAA,CAAqBA,CAAAA,CAACC,SAAUlC,EAAcmC,IAAKR,EAASS,cAAehD,EAAYiD,oBAAoB,WACxG,WAACL,EAAAA,CAAIA,CAACM,IAAI,YACN,UAACC,EAAAA,CAAGA,CAAAA,UACA,UAACC,EAAAA,CAAGA,CAAAA,UACA,UAACR,EAAAA,CAAIA,CAACS,KAAK,WACN1I,EAAMgG,MAAM,EAAwB,gBAApBhG,EAAMgG,MAAM,CAAC,EAAE,CAC1BlH,EAAE,cACFA,EAAE,mBAIpB,UAAC6J,KAAAA,CAAAA,GACD,WAACH,EAAAA,CAAGA,CAAAA,CAAClF,UAAU,iBACX,UAACmF,EAAAA,CAAGA,CAAAA,CAACG,EAAE,IAACC,GAAI,EAAGC,GAAI,YACf,WAACC,EAAAA,CAAIA,CAACC,KAAK,YACP,UAACD,EAAAA,CAAIA,CAACE,KAAK,EAAC3F,UAAU,0BAAkBxE,EAAE,gBAC1C,UAACoK,EAAAA,EAASA,CAAAA,CACN9B,KAAK,QACL/H,GAAG,QACH8J,SAAqB,OAAXtD,EACVwB,MAAkB,OAAXxB,EAAmBR,EAAWnB,KAAK,CAAC,EAAK,EAAI,GAAOmB,EAAWnB,KAAK,CAAC2B,EAAO,EAAI,GACvFuD,UAAY/B,GAAkE,KAAnDgC,OAAOhE,EAAWnB,KAAK,CAAC2B,EAAO,EAAK,IAAIyD,IAAI,GACvEC,aAAc,CACVH,UAAWtK,EAAE,0BACjB,EACA0K,SApIhB,CAoI0BC,GAnI1C,GAAM,CAAErC,MAAI,OAAEC,CAAK,CAAE,CAAGH,EAAEC,MAAM,CAC1BuC,EAAW,CACb,GAAGrE,EAAWnB,KAAK,CACnB,CAAC2B,EAAO,CAAEwB,CACd,EACA/B,EAAc,GAAqB,EAC/B,GAAGgC,CAAS,CACZ,CAACF,CAF8B,CAEzB,CAAEsC,EACZ,EACJ,SA8HwB,UAACjB,EAAAA,CAAGA,CAAAA,CAACG,EAAE,IAACC,GAAI,EAAGC,GAAI,YACf,WAACC,EAAAA,CAAIA,CAACC,KAAK,YACP,UAACD,EAAAA,CAAIA,CAACE,KAAK,EAAC3F,UAAU,sCAA8BxE,EAAE,gBACtD,WAAC6K,EAAAA,EAAWA,CAAAA,CACRvC,KAAK,cACL/H,GAAG,cACH8J,QAAQ,IACR9B,MAAOhC,EAAWf,WAAW,CAC7BiF,aAAc,CAAEH,UAAWtK,EAAE,yBAAyB,EACtD0K,SAAUvC,YAEV,UAAC2C,SAAAA,CAAOvC,MAAM,YAAIvI,EAAE,yCACnB4G,EAAWhE,MAAM,EAAI,EAChBgE,EAAWhF,GAAG,CAAC,CAACW,EAAMC,IAEhB,UAACsI,SAAAA,CAAsBvC,MAAOhG,EAAK1B,GAAG,UACjC0B,EAAK6C,KAAK,EADF7C,EAAK1B,GAAG,GAK3B,gBAKtB,UAAC6I,EAAAA,CAAGA,CAAAA,CAAClF,UAAU,gBACX,WAACmF,EAAAA,CAAGA,CAAAA,CAACG,GAAI,EAAGtF,UAAU,SAAS9C,MAAO,CAAEtC,UAAW,MAAO,YACtD,UAAC6K,EAAAA,CAAIA,CAACE,KAAK,WAAEnK,EAAE,oBACf,UAAC+K,EAAAA,CAAcA,CAAAA,CACX3F,MAAO2B,EAAOiE,WAAW,GACzBC,QAAQ,oBACR1K,GAAG,iBACHiE,UAAU,gBAETsC,GACGA,EAAKlF,GAAG,CAAC,CAACW,EAAMV,IACZ,UAACC,MAAAA,UACG,WAACoJ,EAAAA,CAAQA,CAACC,IAAI,EACVC,OAAQ7I,EAAKqF,IAAI,GAAKb,EACtBsE,SAAU9I,EAAK1B,GAAG,CAClBoB,QAAS,IAAMiG,EAAe3F,aAE7BA,EAAKqF,IAAI,CAACoD,WAAW,GAAG,IAAEzI,EAAK6C,KAAK,CAAC4F,WAAW,OAN/CnJ,WAa9B,UAACyJ,KAAAA,CAAAA,GACD,UAAC5B,EAAAA,CAAGA,CAAAA,UACA,UAACO,EAAAA,CAAIA,CAACsB,KAAK,EACP/G,UAAU,QACVgH,KAAK,SACLlD,KAAK,gBACL/H,GAAG,gBACHmK,SApLJ,CAoLce,GAnLlCjF,EAAc,GAAqB,EAC/B,GAAGgC,CAAS,CACZ9C,EAF+B,MAEtB,CAAC8C,EAAU9C,OAAO,CAC/B,EACJ,EAgL4BgG,MAAO1L,EAAE,aACT2L,QAASpF,EAAWb,OAAO,KAGnC,UAAC4F,KAAAA,CAAAA,GACD,UAAC5B,EAAAA,CAAGA,CAAAA,UACA,UAACC,EAAAA,CAAGA,CAAAA,UACA,WAACM,EAAAA,CAAIA,CAACC,KAAK,YACP,UAACD,EAAAA,CAAIA,CAACE,KAAK,WAAEnK,EAAE,iBACf,UAAC4L,EAAAA,CAAeA,CAAAA,CAACC,YACE,OAAX9E,EACOR,EAAWd,WAAW,CAAC,EAAK,EAAI,GAChCc,EAAWd,WAAW,CAACsB,EAAO,EAAI,GAC3C2D,SAAU,GAAc/B,EAAkBmD,YAI5D,UAACpC,EAAAA,CAAGA,CAAAA,UACA,UAACC,EAAAA,CAAGA,CAAAA,UACA,UAACzJ,EAAAA,OAAmBA,CAAAA,CAACoC,MAAOmE,EAAoBtF,SAAU,GAAa6H,EAAMzI,SAGrF,UAACmJ,EAAAA,CAAGA,CAAAA,UACA,UAACC,EAAAA,CAAGA,CAAAA,UACA,WAACM,EAAAA,CAAIA,CAACC,KAAK,YACP,UAACD,EAAAA,CAAIA,CAACE,KAAK,WAAEnK,EAAE,uBACf,UAACoK,EAAAA,EAASA,CAAAA,CACN9B,KAAK,iBACL/H,GAAG,iBACHgI,MAAOhC,EAAWX,cAAc,EAAI,GACpC8E,SAAUvC,WAK1B,UAACuB,EAAAA,CAAGA,CAAAA,CAAClF,UAAU,gBACX,WAACmF,EAAAA,CAAGA,CAAAA,WACA,UAACoC,EAAAA,CAAMA,CAAAA,CAACvH,UAAU,OAAOgH,KAAK,SAASP,QAAQ,mBAC1CjL,EAAE,YAEP,UAAC+L,EAAAA,CAAMA,CAAAA,CAACvH,UAAU,OAAOvC,QA3PhC,CA2PyC+J,IA1P1DxF,EAAcrB,GACduB,EAAsB,EAAE,EACxBC,EAAiB,EAAE,EAEnBsF,OAAOC,QAAQ,CAAC,EAAG,EACvB,EAqP4EjB,QAAQ,gBACnDjL,EAAE,WAEP,UAACmM,IAAIA,CACDC,KAAK,6BACLC,GAAK,OAFJF,0BAID,UAACJ,EAAAA,CAAMA,CAAAA,CAACd,QAAQ,qBAAajL,EAAE,2BASnE", "sources": ["webpack://_N_E/./pages/adminsettings/hazard/hazardReactDropZone.tsx", "webpack://_N_E/./pages/adminsettings/hazard/forms.tsx"], "sourcesContent": ["//Import Library\r\nimport React, { useMemo, useEffect, useState, useCallback } from \"react\";\r\nimport { useDropzone } from \"react-dropzone\";\r\nimport { FontAwesomeIcon } from \"@fortawesome/react-fontawesome\";\r\nimport {\r\n  faTimesCircle,\r\n  faExclamationCircle,\r\n  faCloudUploadAlt,\r\n\r\n} from \"@fortawesome/free-solid-svg-icons\";\r\nimport _ from 'lodash';\r\n\r\n//Import services/components\r\nimport apiService from \"../../../services/apiService\";\r\nimport { useTranslation } from 'next-i18next';\r\n\r\n\r\nlet temp: any = [];\r\n\r\nconst limit: any = process.env.UPLOAD_LIMIT;\r\n\r\nconst baseStyle: any = {\r\n  flex: 1,\r\n  display: \"flex\",\r\n  flexDirection: \"column\",\r\n  alignItems: \"center\",\r\n  justifyContent: \"center\",\r\n  width: \"100%\",\r\n  height: \"100%\",\r\n  borderWidth: 0.1,\r\n  borderColor: \"#fafafa\",\r\n  backgroundColor: \"#fafafa\",\r\n  color: \"black\",\r\n  transition: \"border  .24s ease-in-out\",\r\n};\r\n\r\nconst thumb: any = {\r\n  display: \"inline-flex\",\r\n  borderRadius: 2,\r\n  border: \"1px solid #ddd\",\r\n  marginBottom: 8,\r\n  marginRight: 20,\r\n  width: 100,\r\n  height: 100,\r\n  padding: 2,\r\n  position: \"relative\",\r\n  boxShadow: \"0 0 15px 0.25px rgba(0,0,0,0.25)\",\r\n  boxSizing: \"border-box\"\r\n};\r\n\r\nconst thumbsContainer: any = {\r\n  display: \"flex\",\r\n  flexDirection: \"row\",\r\n  justifyContent: \"flex-start\",\r\n  flexWrap: \"wrap\",\r\n  marginTop: 20,\r\n};\r\n\r\nconst thumbInner: any = {\r\n  display: \"flex\",\r\n  minWidth: 0,\r\n  overflow: \"hidden\",\r\n};\r\n\r\nconst icon: any = {\r\n  position: \"absolute\",\r\n  fontSize: \"22px\",\r\n  top: \"-10px\",\r\n  right: \"-10px\",\r\n  zIndex: 1000,\r\n  cursor: \"pointer\",\r\n  backgroundColor: \"#fff\",\r\n  color: \"#000\",\r\n  borderRadius: \"50%\"\r\n};\r\n\r\nconst img = {\r\n  display: \"block\",\r\n  width: \"auto\",\r\n  height: \"100%\",\r\n};\r\n\r\nconst activeStyle: any = {\r\n  borderColor: \"#2196f3\",\r\n};\r\n\r\nconst HazardReactDropZone = (props: any) => {\r\n  const { t } = useTranslation('common');\r\n  const [files, setFiles] = useState<any[]>([]);\r\n\r\n\r\n  const imageDelete = async (id: any) => {\r\n    const _res = await apiService.remove(`/image/${id}`);\r\n  }\r\n\r\n  const removeFile = (file: any) => {\r\n    const obj = file && file._id ? { serverID: file._id } : { file: file }\r\n    const _index = _.findIndex(temp, obj);\r\n    imageDelete(temp[_index].serverID)\r\n    temp.splice(_index, 1);\r\n    props.getImgID(temp, props.index ? props.index : 0)\r\n    const newFiles: any = [...files];\r\n    newFiles.splice(newFiles.indexOf(file), 1);\r\n    setFiles(newFiles);\r\n\r\n  };\r\n\r\n  const getComponent = (file: any) => {\r\n    return (<img src={file.preview} style={img} />);\r\n  }\r\n\r\n  const thumbs: any = files.map((file: any, i) => {\r\n    return (\r\n      <div key={i}>\r\n        <div style={thumb}>\r\n          <div style={thumbInner}>\r\n            {getComponent(file)}\r\n          </div>\r\n\r\n          <FontAwesomeIcon\r\n            icon={faTimesCircle}\r\n            style={icon}\r\n            color=\"black\"\r\n            onClick={() => removeFile(file)}\r\n          />\r\n        </div>\r\n      </div>\r\n    );\r\n  });\r\n\r\n  useEffect(() => {\r\n    files.forEach((file: any) => URL.revokeObjectURL(file.preview));\r\n    temp = [];\r\n  }, []);\r\n\r\n  useEffect(() => {\r\n\r\n    if (props && props.datas) {\r\n      const newObj = props.datas.map((item: any, _i: any) => {\r\n\r\n        temp.push({ serverID: item._id, index: props.index ? props.index : 0 });\r\n        const previewState = { ...item, preview: `${process.env.API_SERVER}/image/show/${item._id}` }\r\n        return previewState;\r\n      });\r\n      setFiles([...newObj])\r\n    }\r\n  }, [props.datas]);\r\n\r\n  const filesUpload = async (files_initials: any, index: any) => {\r\n\r\n    if (files_initials.length > index) {\r\n      try {\r\n        const form: any = new FormData();\r\n        form.append(\"file\", files_initials[index]);\r\n        const res = await apiService.post(`/image`, form, { 'Content-Type': 'multipart/form-data' });\r\n        temp.push({ serverID: res._id, file: files_initials[index], index: props.index ? props.index : 0 });\r\n        filesUpload(files_initials, index + 1);\r\n      } catch (error) {\r\n        filesUpload(files_initials, index + 1);\r\n      }\r\n    } else {\r\n      props.getImgID(temp, props.index ? props.index : 0);\r\n    }\r\n  }\r\n\r\n  const onDrop = useCallback((drop_files: any) => {\r\n    filesUpload(drop_files, 0);\r\n    const accFiles = drop_files.map((file: any, i: any) =>\r\n      Object.assign(file, {\r\n        preview: URL.createObjectURL(file),\r\n      }),\r\n    );\r\n    setFiles([...accFiles])\r\n  }, []);\r\n\r\n  const {\r\n    getRootProps,\r\n    getInputProps,\r\n    isDragActive,\r\n    isDragAccept,\r\n    isDragReject,\r\n    fileRejections\r\n  } = useDropzone({\r\n    accept: \"image/*\",\r\n    multiple: false,\r\n    minSize: 0,\r\n    maxSize: limit,\r\n    onDrop\r\n  })\r\n\r\n  const style = useMemo(\r\n    () => ({\r\n      ...baseStyle,\r\n      ...(isDragActive ? activeStyle : { outline: \"2px dashed #bbb\" }),\r\n      ...(isDragAccept\r\n        ? { outline: \"2px dashed #595959\" }\r\n        : { outline: \"2px dashed #bbb\" }),\r\n      ...(isDragReject ? { outline: \"2px dashed red\" } : {activeStyle}),\r\n    }),\r\n    [isDragActive, isDragReject]\r\n  );\r\n\r\n  const isFileTooLarge = fileRejections.length > 0 && fileRejections[0].file.size > limit;\r\n  return (\r\n    <>\r\n      <div className=\" d-flex justify-content-center align-items-center mt-3\" style={{ width: \"100%\", height: \"180px\" }}>\r\n        <div {...getRootProps({ style })}>\r\n          <input {...getInputProps()} />\r\n          <FontAwesomeIcon icon={faCloudUploadAlt} size=\"4x\" color=\"#999\" />\r\n          <p style={{ color: '#595959', marginBottom: \"0px\" }}>{t(\"Drag'n'dropsomefileshere,orclicktoselectfiles\")}</p>\r\n          <small style={{ color: '#595959' }}>{t(\"ImageWeSupport\")}</small>\r\n          <small style={{ color: '#595959' }}><b>{t(\"Note:\")}</b> {t(\"Onesingleimagewillbeaccepted\")}</small>\r\n\r\n          {isFileTooLarge && <small className=\"text-danger mt-2\"> <FontAwesomeIcon icon={faExclamationCircle} size=\"1x\" color=\"red\" /> {t(\"FileistoolargeItshouldbelessthan20MB\")}</small>}\r\n          {isDragReject && <small className=\"text-danger\" style={{ color: '#595959' }}><FontAwesomeIcon icon={faExclamationCircle} size=\"1x\" color=\"red\" /> {t(\"Filetypenotacceptedsorr\")}</small>}\r\n        </div>\r\n      </div>\r\n      <div style={thumbsContainer}>{thumbs}</div>\r\n    </>\r\n  );\r\n};\r\n\r\nexport default HazardReactDropZone;\r\n", "//Import Library\r\nimport { Con<PERSON><PERSON>, <PERSON>, Row, Col, Form, Button, Dropdown, DropdownButton } from \"react-bootstrap\";\r\nimport ValidationFormWrapper from \"../../../components/common/ValidationFormWrapper\";\r\nimport { TextInput, SelectGroup } from \"../../../components/common/FormValidation\";\r\nimport React, { useRef, useState, useEffect } from \"react\";\r\nimport toast from 'react-hot-toast';\r\nimport Router from \"next/router\";\r\nimport Link from \"next/link\";\r\n\r\n//Import services/components\r\nimport apiService from \"../../../services/apiService\";\r\nimport HazardReactDropZone from \"./hazardReactDropZone\";\r\nimport { useTranslation } from 'next-i18next';\r\nimport { EditorComponent } from \"../../../shared/quill-editor/quill-editor.component\";\r\nimport { Hazard, HazardType, ApiResponse } from \"../../../types\";\r\n\r\ninterface HazardFormProps {\r\n    [key: string]: any;\r\n}\r\n\r\nconst HazardForm = (props: HazardFormProps) => {\r\n    const { t, i18n } = useTranslation(\"common\");\r\n    const currentLang = i18n.language && i18n.language === \"fr\" ? \"en\" : i18n.language;\r\n    const _initialHazard = {\r\n        title: {\r\n            en: \"\",\r\n            fr: \"\",\r\n            de: \"\",\r\n        },\r\n        hazard_type: \"\",\r\n        description: {},\r\n        enabled: true,\r\n        picture: null,\r\n        picture_source: \"\",\r\n        images_src: [],\r\n    };\r\n    const getLang = async () => {\r\n        const response: ApiResponse<any[]> = await apiService.get(\"/language\", langParams);\r\n        if (response) {\r\n            setLang(response.data);\r\n        }\r\n    };\r\n\r\n    const langParams = {\r\n        query: {},\r\n        sort: { title: \"asc\" },\r\n        limit: \"~\",\r\n        select: \"-_id -created_at -updated_at\",\r\n    };\r\n\r\n    const [initialVal, setInitialVal] = useState<any>(_initialHazard);\r\n    const [dropZoneCollection, setDropZoneCollection] = useState<any[]>([]);\r\n    const [, setSrcCollection] = useState<any[]>([]);\r\n    const [hazardtype, setHazardType] = useState<HazardType[]>([]);\r\n    const [lang, setLang] = useState<any[]>([]);\r\n    const [locale, setLocale] = useState<string>(\r\n        i18n.language && i18n.language === \"fr\" && currentLang === \"en\" ? \"fr\" : currentLang\r\n    );\r\n\r\n    const editform: boolean = props.routes && props.routes[0] === \"edit_hazard\" && props.routes[1];\r\n\r\n    const handleSubmit = async (event: any, values?: any) => {\r\n        event.preventDefault();\r\n        // Use Formik values if available, otherwise fall back to initialVal\r\n        const formValues = values || initialVal;\r\n        let titleResult: any = {};\r\n        let descResult: any = {};\r\n        lang &&\r\n            lang.map((item, _i) => {\r\n                const langItem = item.abbr;\r\n                titleResult = {\r\n                    ...titleResult,\r\n                    [langItem]: formValues.title[langItem],\r\n                };\r\n                descResult = {\r\n                    ...descResult,\r\n                    [langItem]: formValues.description[langItem],\r\n                };\r\n            });\r\n\r\n        //Setting both the title as same\r\n        // if (titleResult['de'] == \"\") {\r\n        //     titleResult['de'] = titleResult['en'];\r\n        // }\r\n\r\n        // if (titleResult['en'] == \"\") {\r\n        //     titleResult['en'] = titleResult['de'];\r\n        // }\r\n        // new conditions \r\n        if (titleResult['en']) {\r\n            titleResult['de'] = titleResult['en'];\r\n            titleResult['fr'] = titleResult['en'];\r\n        }\r\n\r\n        if (titleResult['de']) {\r\n            titleResult['en'] = titleResult['de'];\r\n            titleResult['fr'] = titleResult['de'];\r\n        }\r\n\r\n        if (titleResult['fr']) {\r\n            titleResult['en'] = titleResult['fr'];\r\n            titleResult['de'] = titleResult['fr'];\r\n        }\r\n        const obj = {\r\n            title: {\r\n                ...titleResult,\r\n            },\r\n            hazard_type: formValues.hazard_type,\r\n            description: {\r\n                ...descResult,\r\n            },\r\n            enabled: formValues.enabled,\r\n            picture: formValues.picture,\r\n            picture_source: formValues.picture_source,\r\n            images_src: formValues.images_src,\r\n        };\r\n\r\n        let response;\r\n        let toastMsg;\r\n        if (editform) {\r\n            toastMsg = \"Hazardisupdatedsuccessfully\";\r\n            response = await apiService.patch(`/hazard/${props.routes[1]}`, obj);\r\n        } else {\r\n            toastMsg = \"Hazardisaddedsuccessfully\";\r\n            response = await apiService.post(\"/hazard\", obj);\r\n        }\r\n        if (response && response._id) {\r\n            toast.success(t(toastMsg));\r\n            Router.push(\"/adminsettings/hazard\");\r\n        } else {            \r\n            if (response?.errorCode === 11000) {\r\n                toast.error(t(\"duplicatesNotAllowed\"));\r\n            } else {\r\n                toast.error(response);\r\n            }\r\n        }\r\n    };\r\n\r\n    const onChangeLocale = (item: any): void => {\r\n        setLocale(item.abbr);\r\n    };\r\n\r\n    const resetHandler = () => {\r\n        setInitialVal(_initialHazard);\r\n        setDropZoneCollection([]);\r\n        setSrcCollection([]);\r\n        // Reset validation state (Formik handles this automatically)\r\n        window.scrollTo(0, 0);\r\n    };\r\n\r\n    const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement | HTMLTextAreaElement>) => {\r\n        if (e.target) {\r\n            const { name, value } = e.target;\r\n            setInitialVal((prevState: any) => ({\r\n                ...prevState,\r\n                [name]: value,\r\n            }));\r\n        }\r\n    };\r\n\r\n    const handleTitle = (e: React.ChangeEvent<HTMLInputElement>) => {\r\n        const { name, value } = e.target;\r\n        const titleObj = {\r\n            ...initialVal.title,\r\n            [locale]: value,\r\n        };\r\n        setInitialVal((prevState: any) => ({\r\n            ...prevState,\r\n            [name]: titleObj,\r\n        }));\r\n    };\r\n\r\n    const handleMonitored = (e: React.ChangeEvent<HTMLInputElement>) => {\r\n        setInitialVal((prevState: any) => ({\r\n            ...prevState,\r\n            enabled: !prevState.enabled,\r\n        }));\r\n    };\r\n\r\n    const getHazardRegion = async (hazardParams: any) => {\r\n        const response: ApiResponse<HazardType[]> = await apiService.get(\"/hazardtype\", hazardParams);\r\n        if (response) {\r\n            setHazardType(response.data);\r\n        }\r\n    };\r\n\r\n    const handleDescription = (value: string) => {\r\n        const descrObj = {\r\n            ...initialVal.description,\r\n            [locale]: value,\r\n        };\r\n        setInitialVal((prevState: any) => ({\r\n            ...prevState,\r\n            description: descrObj,\r\n        }));\r\n    };\r\n\r\n    const getHazardData = async (hazardParams: any) => {\r\n        const response: any = await apiService.get(`/hazard/${props.routes[1]}`, hazardParams);\r\n\r\n        if (response) {\r\n            const { hazard_type, picture } = response;\r\n            response.hazard_type = hazard_type && hazard_type._id ? hazard_type._id : \"\";\r\n\r\n            // Ensure title has the correct structure\r\n            if (response.title && typeof response.title === 'object') {\r\n                response.title = {\r\n                    en: response.title.en || \"\",\r\n                    fr: response.title.fr || \"\",\r\n                    de: response.title.de || \"\",\r\n                };\r\n            } else {\r\n                response.title = {\r\n                    en: \"\",\r\n                    fr: \"\",\r\n                    de: \"\",\r\n                };\r\n            }\r\n\r\n            // Ensure description has the correct structure\r\n            if (response.description && typeof response.description === 'object') {\r\n                response.description = {\r\n                    en: response.description.en || \"\",\r\n                    fr: response.description.fr || \"\",\r\n                    de: response.description.de || \"\",\r\n                    ...response.description\r\n                };\r\n            } else {\r\n                response.description = {};\r\n            }\r\n\r\n            if (picture != null) {\r\n                setDropZoneCollection([picture]);\r\n            }\r\n            setSrcCollection(response.images_src ? response.images_src : []);\r\n            setInitialVal((prevState: any) => ({ ...prevState, ...response }));\r\n        }\r\n    };\r\n\r\n    useEffect(() => {\r\n        getLang();\r\n        const hazardParams = {\r\n            query: {},\r\n            sort: { title: \"asc\" },\r\n            limit: \"~\",\r\n        };\r\n        if (editform) {\r\n            getHazardData(hazardParams);\r\n        }\r\n        getHazardRegion(hazardParams);\r\n    }, []);\r\n\r\n    const formRef = useRef<any>(null);\r\n\r\n    const getID = (id: any) => {\r\n        const _id = id.map((item: any) => item.serverID);\r\n        setInitialVal((prevState: any) => ({ ...prevState, picture: _id }));\r\n    };\r\n\r\n    return (\r\n        <Container className=\"formCard\" fluid>\r\n            <Card\r\n                style={{\r\n                    marginTop: \"5px\",\r\n                    boxShadow: \"0 4px 8px 0 rgba(0, 0, 0, 0.2), 0 6px 20px 0 rgba(0, 0, 0, 0.19)\",\r\n                }}\r\n            >\r\n                <ValidationFormWrapper onSubmit={handleSubmit} ref={formRef} initialValues={initialVal} enableReinitialize={true}>\r\n                    <Card.Body>\r\n                        <Row>\r\n                            <Col>\r\n                                <Card.Title>\r\n                                    {props.routes && props.routes[0] === \"edit_hazard\"\r\n                                        ? t(\"editHazard\")\r\n                                        : t(\"addHazard\")}\r\n                                </Card.Title>\r\n                            </Col>\r\n                        </Row>\r\n                        <hr />\r\n                        <Row className=\"mb-3\">\r\n                            <Col md lg={6} sm={12}>\r\n                                <Form.Group>\r\n                                    <Form.Label className=\"required-field\">{t(\"hazardName\")}</Form.Label>\r\n                                    <TextInput\r\n                                        name=\"title\"\r\n                                        id=\"title\"\r\n                                        required={locale === \"en\"}\r\n                                        value={locale === \"fr\" ? (initialVal.title[\"en\"] || \"\") : (initialVal.title[locale] || \"\")}\r\n                                        validator={(value: any) => String(initialVal.title[locale]  || '').trim() !== \"\"}\r\n                                        errorMessage={{\r\n                                            validator: t(\"adminsetting.hazard.add\"),\r\n                                        }}\r\n                                        onChange={handleTitle}\r\n                                    />\r\n                                </Form.Group>\r\n                            </Col>\r\n                            <Col md lg={6} sm={12}>\r\n                                <Form.Group>\r\n                                    <Form.Label className=\"required-field d-flex me-3\">{t(\"hazardType\")}</Form.Label>\r\n                                    <SelectGroup\r\n                                        name=\"hazard_type\"\r\n                                        id=\"hazard_type\"\r\n                                        required\r\n                                        value={initialVal.hazard_type}\r\n                                        errorMessage={{ validator: t(\"pleaseAddtheHazardType\")}}\r\n                                        onChange={handleChange}\r\n                                    >\r\n                                        <option value=\"\">{t(\"hazardTypeCategory.selectHazardType\")}</option>\r\n                                        {hazardtype.length >= 1\r\n                                            ? hazardtype.map((item, _i) => {\r\n                                                return (\r\n                                                    <option key={item._id} value={item._id}>\r\n                                                        {item.title}\r\n                                                    </option>\r\n                                                );\r\n                                            })\r\n                                            : null}\r\n                                    </SelectGroup>\r\n                                </Form.Group>\r\n                            </Col>\r\n                        </Row>\r\n                        <Row className=\"mb-3\">\r\n                            <Col md={4} className=\"d-flex\" style={{ marginTop: \"10px\" }}>\r\n                                <Form.Label>{t(\"chooseLanguage\")}</Form.Label>\r\n                                <DropdownButton\r\n                                    title={locale.toUpperCase()}\r\n                                    variant=\"outline-secondary\"\r\n                                    id=\"basic-dropdown\"\r\n                                    className=\"ms-2\"\r\n                                >\r\n                                    {lang &&\r\n                                        lang.map((item, i) => (\r\n                                            <div key={i}>\r\n                                                <Dropdown.Item\r\n                                                    active={item.abbr === locale}\r\n                                                    eventKey={item._id}\r\n                                                    onClick={() => onChangeLocale(item)}\r\n                                                >\r\n                                                    {item.abbr.toUpperCase()}-{item.title.toUpperCase()}\r\n                                                </Dropdown.Item>\r\n                                            </div>\r\n                                        ))}\r\n                                </DropdownButton>\r\n                            </Col>\r\n                        </Row>\r\n                        <br />\r\n                        <Row>\r\n                            <Form.Check\r\n                                className=\" ms-4\"\r\n                                type=\"switch\"\r\n                                name=\"rki_monitored\"\r\n                                id=\"custom-switch\"\r\n                                onChange={handleMonitored}\r\n                                label={t(\"published\")}\r\n                                checked={initialVal.enabled}\r\n                            />\r\n                        </Row>\r\n                        <br />\r\n                        <Row>\r\n                            <Col>\r\n                                <Form.Group>\r\n                                    <Form.Label>{t(\"description\")}</Form.Label>\r\n                                    <EditorComponent initContent={\r\n                                            locale === \"fr\"\r\n                                                ? (initialVal.description[\"en\"] || \"\")\r\n                                                : (initialVal.description[locale] || \"\")\r\n                                        } onChange={(evt: any) => handleDescription(evt)} />\r\n                                </Form.Group>\r\n                            </Col>\r\n                        </Row>\r\n                        <Row>\r\n                            <Col>\r\n                                <HazardReactDropZone datas={dropZoneCollection} getImgID={(id: any) => getID(id)} />\r\n                            </Col>\r\n                        </Row>\r\n                        <Row>\r\n                            <Col>\r\n                                <Form.Group>\r\n                                    <Form.Label>{t(\"imageSourceCredit\")}</Form.Label>\r\n                                    <TextInput\r\n                                        name=\"picture_source\"\r\n                                        id=\"picture_source\"\r\n                                        value={initialVal.picture_source || \"\"}\r\n                                        onChange={handleChange}\r\n                                    />\r\n                                </Form.Group>\r\n                            </Col>\r\n                        </Row>\r\n                        <Row className=\"my-4\">\r\n                            <Col>\r\n                                <Button className=\"me-2\" type=\"submit\" variant=\"primary\">\r\n                                    {t(\"submit\")}\r\n                                </Button>\r\n                                <Button className=\"me-2\" onClick={resetHandler} variant=\"info\">\r\n                                    {t(\"reset\")}\r\n                                </Button>\r\n                                <Link\r\n                                    href=\"/adminsettings/[...routes]\"\r\n                                    as={`/adminsettings/hazard`}\r\n                                    >\r\n                                    <Button variant=\"secondary\">{t(\"Cancel\")}</Button>\r\n                                </Link>\r\n                            </Col>\r\n                        </Row>\r\n                    </Card.Body>\r\n                </ValidationFormWrapper>\r\n            </Card>\r\n        </Container>\r\n    );\r\n};\r\n\r\nexport default HazardForm;\r\n"], "names": ["temp", "limit", "process", "baseStyle", "flex", "display", "flexDirection", "alignItems", "justifyContent", "width", "height", "borderWidth", "borderColor", "backgroundColor", "color", "transition", "thumb", "borderRadius", "border", "marginBottom", "marginRight", "padding", "position", "boxShadow", "boxSizing", "thumbsContainer", "flexWrap", "marginTop", "thumbInner", "min<PERSON><PERSON><PERSON>", "overflow", "icon", "fontSize", "top", "right", "zIndex", "cursor", "img", "activeStyle", "t", "useTranslation", "HazardReactDropZone", "files", "setFiles", "useState", "imageDelete", "id", "apiService", "remove", "removeFile", "file", "obj", "_id", "serverID", "_index", "_", "splice", "props", "getImgID", "index", "newFiles", "indexOf", "getComponent", "src", "preview", "style", "thumbs", "map", "i", "div", "FontAwesomeIcon", "faTimesCircle", "onClick", "useEffect", "for<PERSON>ach", "URL", "revokeObjectURL", "datas", "item", "_i", "push", "filesUpload", "files_initials", "length", "form", "FormData", "append", "res", "post", "error", "onDrop", "useCallback", "drop_files", "Object", "assign", "createObjectURL", "getRootProps", "getInputProps", "isDragActive", "isDragAccept", "isDragReject", "fileRejections", "useDropzone", "accept", "multiple", "minSize", "maxSize", "useMemo", "outline", "isFileTooLarge", "size", "className", "input", "faCloudUploadAlt", "p", "small", "b", "faExclamationCircle", "i18n", "HazardForm", "currentLang", "language", "_initialHazard", "title", "en", "fr", "de", "hazard_type", "description", "enabled", "picture", "picture_source", "images_src", "getLang", "response", "get", "langParams", "setLang", "data", "query", "sort", "select", "initialVal", "setInitialVal", "dropZoneCollection", "setDropZoneCollection", "setSrcCollection", "hazardtype", "setHazardType", "lang", "locale", "setLocale", "editform", "routes", "handleSubmit", "event", "values", "toastMsg", "preventDefault", "formValues", "titleResult", "descR<PERSON>ult", "langItem", "abbr", "patch", "toast", "success", "Router", "errorCode", "onChangeLocale", "handleChange", "e", "target", "name", "value", "prevState", "getHazardRegion", "hazardParams", "handleDescription", "descrObj", "getHazardData", "formRef", "useRef", "getID", "Container", "fluid", "Card", "ValidationFormWrapper", "onSubmit", "ref", "initialValues", "enableReinitialize", "Body", "Row", "Col", "Title", "hr", "md", "lg", "sm", "Form", "Group", "Label", "TextInput", "required", "validator", "String", "trim", "errorMessage", "onChange", "handleTitle", "titleObj", "SelectGroup", "option", "DropdownButton", "toUpperCase", "variant", "Dropdown", "<PERSON><PERSON>", "active", "eventKey", "br", "Check", "type", "handleMonitored", "label", "checked", "EditorComponent", "initContent", "evt", "<PERSON><PERSON>", "re<PERSON><PERSON><PERSON><PERSON>", "window", "scrollTo", "Link", "href", "as"], "sourceRoot": "", "ignoreList": []}