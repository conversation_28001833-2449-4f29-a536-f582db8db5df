{"version": 3, "file": "static/chunks/pages/adminsettings/approval/InstitutionTable-9405518d728e0cd2.js", "mappings": "0KAqCA,SAASA,EAASC,CAAoB,EACpC,GAAM,CAAEC,CAAC,CAAE,CAAGC,CAAAA,EAAAA,EAAAA,EAAAA,CAAcA,CAAC,UACvBC,EAA6B,CACjCC,gBAAiBH,EAAE,cACnB,EACI,SACJI,CAAO,MACPC,CAAI,WACJC,CAAS,uBACTC,CAAqB,WACrBC,CAAS,oBACTC,CAAkB,qBAClBC,CAAmB,kBACnBC,CAAgB,aAChBC,CAAW,oBACXC,CAAkB,CAClBC,gBAAc,CACdC,SAAO,WACPC,CAAS,sBACTC,CAAoB,mBACpBC,CAAiB,YACjBC,CAAU,QACVC,CAAM,kBACNC,CAAgB,CAChBC,cAAY,CAEZ,CADA,EACGC,EACJ,CAAGxB,EAGEyB,EAAiB,4BACrBtB,EACAuB,gBAAiBzB,EAAE,IAP0C,MAQ7D0B,UAAU,UACVtB,EACAC,KAAMA,GAAQ,EAAE,CAChBsB,OAAO,EACPC,2BAA4BrB,EAC5BsB,UAAWrB,EACXsB,gBAAiBf,qBACjBN,EACAsB,YAAY,EACZC,iBAAkBhB,EAClBiB,kBAAmBpB,GAA0C,GAC7DqB,eADwCrB,cACVD,GAA4B,CAAC,GAAI,GAAI,GAAI,CAA3BA,EAA+B,GAAG,CAC9EuB,oBAAqB7B,EACrB8B,oBAAqB1B,EACrB2B,aAAc1B,iBACdG,EACAG,uBACAC,oBACAoB,kBAAmB,UAACC,EAAAA,CAAYA,CAAAA,CAAAA,GAChCC,SAAU,UAACC,IAAAA,CAAEC,UAAU,6CACvBvB,SACAC,eACAE,mBACAD,EACAqB,UAAW,WACb,EACA,MACE,UAACC,EAAAA,EAASA,CAAAA,CAAE,GAAGnB,CAAc,EAEjC,CAEA1B,EAAS8C,YAAY,CAAG,CACtBf,WAAW,EACXE,WAAY,GACZzB,UAAW,KACXU,WAAW,EACXC,qBAAsB,KACtBC,mBAAmB,EACnBC,YAAY,EACZE,iBAAkB,EACpB,EAEA,MAAevB,QAAQA,EAAC,SC/GxB,4CACA,2CACA,WACA,OAAe,EAAQ,KAAgE,CACvF,EACA,SAFsB,iJC+KtB,MAxKA,SAA0B+C,CAAW,EACnC,GAAM,CAACC,EAAWC,EAAe,CAAGC,CAAAA,EAAAA,EAAAA,EAuKvBC,MAvKuBD,CAAQA,CAAQ,EAAE,EAChD,EAAGE,EAAW,CAAGF,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,CAAU,IACnC,CAAC1C,EAAW6C,EAAa,CAAGH,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,CAAS,GAC7C,CAACI,EAASC,EAAW,CAAGL,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,CAAS,IACzC,CAACM,EAAaC,EAAS,CAAGP,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,CAAU,IAC5C,CAACQ,EAAWC,EAAa,CAAGT,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,CAAS,IAC7C,CAACU,EAAmBC,EAAqB,CAAGX,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,CAAM,CAAC,GAC3D,CAAEhD,GAAC,CAAE,CAAGC,CAAAA,EAAAA,EAAAA,EAAAA,CAAcA,CAAC,UAIvB2D,EAAa,CACjBC,KAAM,CAAEC,WAAY,MAAO,EAC3BC,MAAOX,EACPY,KAAM,EACNC,MAAO,CAAEC,OAAQ,iBAAkB,CACrC,EAEM9D,EAAU,CACd,CACE+D,KAAMnE,EAAE,SACRoE,SAAU,QACVC,KAAM,GAAYC,EAAEC,KAAK,EAE3B,CACEJ,KAAMnE,EAAE,SACRoE,SAAU,QACVC,KAAM,GAAYC,EAAEE,KAAK,EAE3B,CACEL,KAAO,cACPC,SAAU,eACVC,KAAM,GAAYC,EAAEG,YAAY,EAElC,CACEN,KAAMnE,EAAE,UACRoE,SAAU,GACVC,KAAM,GACJ,WAACK,MAAAA,WACC,UAACC,EAAAA,CAAMA,CAAAA,CACLC,QAAQ,UACRC,KAAK,KACLC,QAAS,IAAMC,EAAWT,EAAG,oBAE5BtE,EAAE,oBACI,OAET,UAAC2E,EAAAA,CAAMA,CAAAA,CACLC,QAAQ,YACRC,KAAK,KACLC,QAAS,IAAMC,EAAWT,EAAG,mBAE5BtE,EAAE,oBAIX,EACD,CAEKgF,EAAc,UAClB9B,GAAW,GACX,IAAM+B,EAAW,MAAMC,EAAAA,CAAUA,CAACC,GAAG,CAAC,eAAgBvB,GAClDqB,GAAYA,EAAS5E,IAAI,EAAE,CAC7B0C,EAAekC,EAAS5E,IAAI,EAC5B8C,EAAa8B,EAASG,UAAU,EAChClC,GAAW,GAEf,EAOMxC,EAAsB,MAAO2E,EAAoBrB,KACrDJ,EAAWG,KAAK,CAAGsB,EACnBzB,EAAWI,IAAI,CAAGA,EAClBd,GAAW,GACX,IAAM+B,EAAW,MAAMC,EAAAA,CAAUA,CAACC,GAAG,CAAC,eAAgBvB,GAClDqB,GAAYA,EAAS5E,IAAI,EAAI4E,EAAS5E,IAAI,CAACiF,MAAM,CAAG,GAAG,CACzDvC,EAAekC,EAAS5E,IAAI,EAC5BgD,EAAWgC,GACXnC,GAAW,GAEf,EAEAqC,CAAAA,EAAAA,EAAAA,SAAAA,CAASA,CAAC,KACRP,GACF,EAAG,EAAE,EAEL,IAAMD,EAAa,MAAOT,EAAQJ,KAChCX,GAAS,GACTE,EAAaS,GACTI,GAAKA,EAAEkB,GAAG,EAAE,EAEO,CAAE,GAAGlB,CAAC,CAAEJ,OADA,CACQuB,WADnBvB,EAAuB,WAAa,UACP,EAEnD,EAEMwB,EAAe,UAEnB,GAAoC,YAAW,CAA3ChC,EAAkB,MAAS,CAC7B,MAAMwB,EADa,CACHA,CAACS,MAAM,CAAC,gBAAyC,OAAzBjC,EAAkB,GAAM,GAChEsB,IACAY,EAAAA,EAAKA,CAACC,KAAK,CAAC7F,EAAE,mBACd2D,EAAqB,CAAC,GACtBJ,GAAS,OAEL,CACJ,IAAMuC,EAAc,MAAMZ,EAAAA,CAAUA,CAACa,KAAK,CACxC,gBAAyC,OAAzBrC,EAAkB,GAAM,EACxCA,GAEF,GAAIoC,GAAeA,CAHgB,OAGJ5B,MAAM,CAAU,YAC7C0B,EAAAA,EAAKA,CAACC,KAAK,CACTC,EAAYb,QAAQ,EAAIa,EAAYb,QAAQ,CAACe,OAAO,CAChDF,EAAYb,QAAQ,CAACe,OAAO,CAC5BhG,EAAE,8DAIRgF,IACAY,EAAAA,EAAKA,CAACK,OAAO,CAACjG,EAAE,kBAChB2D,EAAqB,CAAC,GACtBJ,GAAS,EAEb,CACAyB,IACArB,EAAqB,CAAC,GACtBJ,GAAS,EACX,EAEM2C,EAAY,IAAM3C,GAAS,GAEjC,MACE,WAACmB,MAAAA,WACC,WAACyB,EAAAA,CAAKA,CAAAA,CAACC,KAAM9C,EAAa+C,OAAQH,YAChC,UAACC,EAAAA,CAAKA,CAACG,MAAM,EAACC,WAAW,aACvB,WAACJ,EAAAA,CAAKA,CAACK,KAAK,YACThD,EAAUiD,MAAM,CAAC,GAAGC,WAAW,GAAKlD,EAAUmD,KAAK,CAAC,GAAG,IAAE3G,EAAE,qBAGhE,WAACmG,EAAAA,CAAKA,CAACS,IAAI,YACV5G,EAAE,0DAA0D,KAAGwD,EAAU,IAAExD,EAAE,wBAE9E,WAACmG,EAAAA,CAAKA,CAACU,MAAM,YACX,UAAClC,EAAAA,CAAMA,CAAAA,CAACC,QAAQ,YAAYE,QAASoB,WACpClG,EAAE,YAEH,UAAC2E,EAAAA,CAAMA,CAAAA,CAACC,QAAQ,UAAUE,QAASY,WAClC1F,EAAE,eAKP,UAACF,EAAAA,CAAQA,CAAAA,CACPM,QAASA,EACTC,KAAMyC,EACNxC,UAAWA,EACXU,WAAW,EACXN,oBAAqBA,EACrBC,iBA7FmB,CA6FDA,GA5FtBiD,EAAWG,KAAK,CAAGX,EACnBQ,EAAWI,IAAI,CAAGA,EAClBgB,GACF,MA6FF", "sources": ["webpack://_N_E/./components/common/RKITable.tsx", "webpack://_N_E/?cfce", "webpack://_N_E/./pages/adminsettings/approval/InstitutionTable.tsx"], "sourcesContent": ["//Import Library\r\nimport React from 'react'\r\nimport DataTable  from 'react-data-table-component';\r\n\r\n//Import services/components\r\nimport { useTranslation } from 'next-i18next';\r\nimport CustomLoader from './CustomLoader';\r\n\r\ninterface RKITableProps {\r\n  columns: any[];\r\n  data: any[];\r\n  totalRows?: number;\r\n  resetPaginationToggle?: boolean;\r\n  subheader?: boolean;\r\n  subHeaderComponent?: React.ReactNode;\r\n  handlePerRowsChange?: (newPerPage: number, page: number) => void;\r\n  handlePageChange?: (page: number) => void;\r\n  rowsPerPage?: number[];\r\n  defaultRowsPerPage?: number;\r\n  selectableRows?: boolean;\r\n  loading?: boolean;\r\n  pagServer?: boolean;\r\n  onSelectedRowsChange?: (selectedRows: any) => void;\r\n  clearSelectedRows?: boolean;\r\n  sortServer?: boolean;\r\n  onSort?: (column: any, sortDirection: string) => void;\r\n  persistTableHead?: boolean;\r\n  sortFunction?: (rows: any[], field: any, direction: 'asc' | 'desc') => any[];\r\n  noHeader?: boolean;\r\n  dense?: boolean;\r\n  pagination?: boolean;\r\n  paginationServer?: boolean;\r\n  paginationTotalRows?: number;\r\n  subHeaderAlign?: string;\r\n  [key: string]: any;\r\n}\r\n\r\nfunction RKITable(props: RKITableProps) {\r\n  const { t } = useTranslation('common');\r\n  const paginationComponentOptions = {\r\n    rowsPerPageText: t('Rowsperpage'),\r\n    };\r\n  const {\r\n    columns,\r\n    data,\r\n    totalRows,\r\n    resetPaginationToggle,\r\n    subheader,\r\n    subHeaderComponent,\r\n    handlePerRowsChange,\r\n    handlePageChange,\r\n    rowsPerPage,\r\n    defaultRowsPerPage,\r\n    selectableRows,\r\n    loading,\r\n    pagServer,\r\n    onSelectedRowsChange,\r\n    clearSelectedRows,\r\n    sortServer,\r\n    onSort,\r\n    persistTableHead,\r\n    sortFunction,\r\n    // Filter out any unknown props that might cause DOM warnings\r\n    ...otherProps\r\n  } = props;\r\n\r\n  // Only pass known DataTable props to avoid DOM warnings\r\n  const dataTableProps = {\r\n    paginationComponentOptions,\r\n    noDataComponent: t(\"NoData\"),\r\n    noHeader: true,\r\n    columns,\r\n    data: data || [],\r\n    dense: true,\r\n    paginationResetDefaultPage: resetPaginationToggle,\r\n    subHeader: subheader,\r\n    progressPending: loading,\r\n    subHeaderComponent,\r\n    pagination: true,\r\n    paginationServer: pagServer,\r\n    paginationPerPage: defaultRowsPerPage ? defaultRowsPerPage : 10,\r\n    paginationRowsPerPageOptions: rowsPerPage ? rowsPerPage : [10, 15, 20, 25, 30],\r\n    paginationTotalRows: totalRows,\r\n    onChangeRowsPerPage: handlePerRowsChange,\r\n    onChangePage: handlePageChange,\r\n    selectableRows,\r\n    onSelectedRowsChange,\r\n    clearSelectedRows,\r\n    progressComponent: <CustomLoader/>,\r\n    sortIcon: <i className=\"sort-icon fas fa-exchange-alt\" />,\r\n    sortServer,\r\n    onSort,\r\n    sortFunction,\r\n    persistTableHead,\r\n    className: \"rki-table\"\r\n  };\r\n  return (\r\n    <DataTable {...dataTableProps} />\r\n  )\r\n};\r\n\r\nRKITable.defaultProps = {\r\n  subHeader: false,\r\n  pagination: true,\r\n  totalRows: null,\r\n  pagServer: true,\r\n  onSelectedRowsChange: null,\r\n  clearSelectedRows: false,\r\n  sortServer: false,\r\n  persistTableHead: false\r\n}\r\n\r\nexport default RKITable;\r\n\r\n", "\n    (window.__NEXT_P = window.__NEXT_P || []).push([\n      \"/adminsettings/approval/InstitutionTable\",\n      function () {\n        return require(\"private-next-pages/adminsettings/approval/InstitutionTable.tsx\");\n      }\n    ]);\n    if(module.hot) {\n      module.hot.dispose(function () {\n        window.__NEXT_P.push([\"/adminsettings/approval/InstitutionTable\"])\n      });\n    }\n  ", "//Import Library\r\nimport React, { useEffect, useState } from \"react\";\r\nimport { <PERSON><PERSON>, <PERSON><PERSON> } from \"react-bootstrap\";\r\nimport toast from 'react-hot-toast';\r\n\r\n//Import services/components\r\nimport apiService from \"../../../services/apiService\";\r\nimport { useTranslation } from 'next-i18next';\r\nimport RKITable from \"../../../components/common/RKITable\";\r\n\r\n\r\nfunction InstitutionTable(_props: any) {\r\n  const [tabledata, setDataToTable] = useState<any[]>([]);\r\n  const [, setLoading] = useState<boolean>(false);\r\n  const [totalRows, setTotalRows] = useState<number>(0);\r\n  const [perPage, setPerPage] = useState<number>(10);\r\n  const [isModalShow, setModal] = useState<boolean>(false);\r\n  const [newStatus, setNewStatus] = useState<string>(\"\");\r\n  const [selectUserDetails, setSelectUserDetails] = useState<any>({});\r\n  const { t } = useTranslation('common');\r\n\r\n\r\n\r\n  const instParams = {\r\n    sort: { created_at: \"desc\" },\r\n    limit: perPage,\r\n    page: 1,\r\n    query: { status: \"Request Pending\" },\r\n  };\r\n\r\n  const columns = [\r\n    {\r\n      name: t(\"Title\"),\r\n      selector: \"title\",\r\n      cell: (d: any) => d.title,\r\n    },\r\n    {\r\n      name: t(\"Email\"),\r\n      selector: \"email\",\r\n      cell: (d: any) => d.email,\r\n    },\r\n    {\r\n      name: (\"ContactName\"),\r\n      selector: \"contact_name\",\r\n      cell: (d: any) => d.contact_name,\r\n    },\r\n    {\r\n      name: t(\"Action\"),\r\n      selector: \"\",\r\n      cell: (d: any) => (\r\n        <div>\r\n          <Button\r\n            variant=\"primary\"\r\n            size=\"sm\"\r\n            onClick={() => instAction(d, \"approve\")}\r\n          >\r\n            {t(\"instu.Approves\")}\r\n          </Button>\r\n          &nbsp;\r\n          <Button\r\n            variant=\"secondary\"\r\n            size=\"sm\"\r\n            onClick={() => instAction(d, \"reject\")}\r\n          >\r\n            {t(\"instu.Reject\")}\r\n          </Button>\r\n        </div>\r\n      ),\r\n    },\r\n  ];\r\n\r\n  const getInstData = async () => {\r\n    setLoading(true);\r\n    const response = await apiService.get(\"/institution\", instParams);\r\n    if (response && response.data) {\r\n      setDataToTable(response.data);\r\n      setTotalRows(response.totalCount);\r\n      setLoading(false);\r\n    }\r\n  };\r\n  const handlePageChange = (page: number) => {\r\n    instParams.limit = perPage;\r\n    instParams.page = page;\r\n    getInstData();\r\n  };\r\n\r\n  const handlePerRowsChange = async (newPerPage: number, page: number) => {\r\n    instParams.limit = newPerPage;\r\n    instParams.page = page;\r\n    setLoading(true);\r\n    const response = await apiService.get(\"/institution\", instParams);\r\n    if (response && response.data && response.data.length > 0) {\r\n      setDataToTable(response.data);\r\n      setPerPage(newPerPage);\r\n      setLoading(false);\r\n    }\r\n  };\r\n\r\n  useEffect(() => {\r\n    getInstData();\r\n  }, []);\r\n\r\n  const instAction = async (d: any, status: string) => {\r\n    setModal(true);\r\n    setNewStatus(status);\r\n    if (d && d._id) {\r\n      const setStatus = status === \"approve\" ? \"Approved\" : \"Rejected\";\r\n      setSelectUserDetails({ ...d, status: setStatus });\r\n    }\r\n  };\r\n\r\n  const modalConfirm = async () => {\r\n\r\n    if( selectUserDetails['status'] === \"Rejected\"){\r\n      await apiService.remove(`/institution/${selectUserDetails[\"_id\"]}`);\r\n      getInstData();\r\n      toast.error(t(\"instu.Rejected\"));\r\n      setSelectUserDetails({});\r\n      setModal(false);\r\n\r\n    }else {\r\n      const updatedData = await apiService.patch(\r\n        `/institution/${selectUserDetails[\"_id\"]}`,\r\n        selectUserDetails\r\n      );\r\n      if (updatedData && updatedData.status === 403) {\r\n        toast.error(\r\n          updatedData.response && updatedData.response.message\r\n            ? updatedData.response.message\r\n            : t(\"adminsetting.FocalPointsApprovalTable.Somethingwentswrong\")\r\n        );\r\n        return;\r\n      } else {\r\n        getInstData();\r\n        toast.success(t(\"instu.Approve\"));\r\n        setSelectUserDetails({});\r\n        setModal(false);\r\n      }\r\n    }\r\n    getInstData();\r\n    setSelectUserDetails({});\r\n    setModal(false);\r\n  };\r\n\r\n  const modalHide = () => setModal(false);\r\n\r\n  return (\r\n    <div>\r\n      <Modal show={isModalShow} onHide={modalHide}>\r\n        <Modal.Header closeButton>\r\n          <Modal.Title>\r\n            {newStatus.charAt(0).toUpperCase() + newStatus.slice(1)} {t(\"Organisation\")}\r\n          </Modal.Title>\r\n        </Modal.Header>\r\n        <Modal.Body>\r\n        {t(\"adminsetting.FocalPointsApprovalTable.Areyousurewantto\")}  {newStatus} {t(\"thisOrganisation?\")}\r\n        </Modal.Body>\r\n        <Modal.Footer>\r\n          <Button variant=\"secondary\" onClick={modalHide}>\r\n          {t(\"cancel\")}\r\n          </Button>\r\n          <Button variant=\"primary\" onClick={modalConfirm}>\r\n          {t(\"yes\")}\r\n          </Button>\r\n        </Modal.Footer>\r\n      </Modal>\r\n\r\n      <RKITable\r\n        columns={columns}\r\n        data={tabledata}\r\n        totalRows={totalRows}\r\n        pagServer={true}\r\n        handlePerRowsChange={handlePerRowsChange}\r\n        handlePageChange={handlePageChange}\r\n      />\r\n    </div>\r\n  );\r\n}\r\n\r\nexport default InstitutionTable;\r\n"], "names": ["RKITable", "props", "t", "useTranslation", "paginationComponentOptions", "rowsPerPageText", "columns", "data", "totalRows", "resetPaginationToggle", "subheader", "subHeaderComponent", "handlePerRowsChange", "handlePageChange", "rowsPerPage", "defaultRowsPerPage", "selectableRows", "loading", "pagServer", "onSelectedRowsChange", "clearSelectedRows", "sortServer", "onSort", "persistTableHead", "sortFunction", "otherProps", "dataTableProps", "noDataComponent", "<PERSON><PERSON><PERSON><PERSON>", "dense", "paginationResetDefaultPage", "subHeader", "progressPending", "pagination", "paginationServer", "paginationPerPage", "paginationRowsPerPageOptions", "paginationTotalRows", "onChangeRowsPerPage", "onChangePage", "progressComponent", "CustomLoader", "sortIcon", "i", "className", "DataTable", "defaultProps", "_props", "tabledata", "setDataToTable", "useState", "InstitutionTable", "setLoading", "setTotalRows", "perPage", "setPerPage", "isModalShow", "setModal", "newStatus", "setNewStatus", "selectUserDetails", "setSelectUserDetails", "instParams", "sort", "created_at", "limit", "page", "query", "status", "name", "selector", "cell", "d", "title", "email", "contact_name", "div", "<PERSON><PERSON>", "variant", "size", "onClick", "instAction", "getInstData", "response", "apiService", "get", "totalCount", "newPerPage", "length", "useEffect", "_id", "setStatus", "modalConfirm", "remove", "toast", "error", "updatedData", "patch", "message", "success", "modalHide", "Modal", "show", "onHide", "Header", "closeButton", "Title", "char<PERSON>t", "toUpperCase", "slice", "Body", "Footer"], "sourceRoot": "", "ignoreList": []}