"use strict";exports.id=5387,exports.ids=[5387],exports.modules={6417:(e,a,t)=>{t.d(a,{A:()=>n});let r=t(82015).createContext(null);r.displayName="CardHeaderContext";let n=r},15653:(e,a,t)=>{t.d(a,{ks:()=>l,s3:()=>i});var r=t(8732);t(82015);var n=t(59549),s=t(43294);let l=({name:e,id:a,required:t,validator:l,errorMessage:i,onChange:o,value:d,as:c,multiline:u,rows:m,pattern:v,...f})=>(0,r.jsx)(s.Field,{name:e,validate:e=>{let a="string"==typeof e?e:String(e||"");return t&&(!e||""===a.trim())?i?.validator||"This field is required":l&&!l(e)?i?.validator||"Invalid value":v&&e&&!new RegExp(v).test(e)?i?.pattern||"Invalid format":void 0},children:({field:e,meta:t})=>(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)(n.A.Control,{...e,...f,id:a,as:c||"input",rows:m,isInvalid:t.touched&&!!t.error,onChange:a=>{e.onChange(a),o&&o(a)},value:void 0!==d?d:e.value}),t.touched&&t.error?(0,r.jsx)(n.A.Control.Feedback,{type:"invalid",children:t.error}):null]})}),i=({name:e,id:a,required:t,errorMessage:l,onChange:i,value:o,children:d,...c})=>(0,r.jsx)(s.Field,{name:e,validate:e=>{if(t&&(!e||""===e))return l?.validator||"This field is required"},children:({field:e,meta:t})=>(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)(n.A.Control,{as:"select",...e,...c,id:a,isInvalid:t.touched&&!!t.error,onChange:a=>{e.onChange(a),i&&i(a)},value:void 0!==o?o:e.value,children:d}),t.touched&&t.error?(0,r.jsx)(n.A.Control.Feedback,{type:"invalid",children:t.error}):null]})})},18597:(e,a,t)=>{t.d(a,{A:()=>N});var r=t(3892),n=t.n(r),s=t(82015),l=t(80739),i=t(8732);let o=s.forwardRef(({className:e,bsPrefix:a,as:t="div",...r},s)=>(a=(0,l.oU)(a,"card-body"),(0,i.jsx)(t,{ref:s,className:n()(e,a),...r})));o.displayName="CardBody";let d=s.forwardRef(({className:e,bsPrefix:a,as:t="div",...r},s)=>(a=(0,l.oU)(a,"card-footer"),(0,i.jsx)(t,{ref:s,className:n()(e,a),...r})));d.displayName="CardFooter";var c=t(6417);let u=s.forwardRef(({bsPrefix:e,className:a,as:t="div",...r},o)=>{let d=(0,l.oU)(e,"card-header"),u=(0,s.useMemo)(()=>({cardHeaderBsPrefix:d}),[d]);return(0,i.jsx)(c.A.Provider,{value:u,children:(0,i.jsx)(t,{ref:o,...r,className:n()(a,d)})})});u.displayName="CardHeader";let m=s.forwardRef(({bsPrefix:e,className:a,variant:t,as:r="img",...s},o)=>{let d=(0,l.oU)(e,"card-img");return(0,i.jsx)(r,{ref:o,className:n()(t?`${d}-${t}`:d,a),...s})});m.displayName="CardImg";let v=s.forwardRef(({className:e,bsPrefix:a,as:t="div",...r},s)=>(a=(0,l.oU)(a,"card-img-overlay"),(0,i.jsx)(t,{ref:s,className:n()(e,a),...r})));v.displayName="CardImgOverlay";let f=s.forwardRef(({className:e,bsPrefix:a,as:t="a",...r},s)=>(a=(0,l.oU)(a,"card-link"),(0,i.jsx)(t,{ref:s,className:n()(e,a),...r})));f.displayName="CardLink";var p=t(7783);let x=(0,p.A)("h6"),h=s.forwardRef(({className:e,bsPrefix:a,as:t=x,...r},s)=>(a=(0,l.oU)(a,"card-subtitle"),(0,i.jsx)(t,{ref:s,className:n()(e,a),...r})));h.displayName="CardSubtitle";let b=s.forwardRef(({className:e,bsPrefix:a,as:t="p",...r},s)=>(a=(0,l.oU)(a,"card-text"),(0,i.jsx)(t,{ref:s,className:n()(e,a),...r})));b.displayName="CardText";let j=(0,p.A)("h5"),y=s.forwardRef(({className:e,bsPrefix:a,as:t=j,...r},s)=>(a=(0,l.oU)(a,"card-title"),(0,i.jsx)(t,{ref:s,className:n()(e,a),...r})));y.displayName="CardTitle";let A=s.forwardRef(({bsPrefix:e,className:a,bg:t,text:r,border:s,body:d=!1,children:c,as:u="div",...m},v)=>{let f=(0,l.oU)(e,"card");return(0,i.jsx)(u,{ref:v,...m,className:n()(a,f,t&&`bg-${t}`,r&&`text-${r}`,s&&`border-${s}`),children:d?(0,i.jsx)(o,{children:c}):c})});A.displayName="Card";let N=Object.assign(A,{Img:m,Title:y,Subtitle:h,Body:o,Link:f,Text:b,Header:u,Footer:d,ImgOverlay:v})},23579:(e,a,t)=>{t.d(a,{sx:()=>c,s3:()=>n.s3,ks:()=>n.ks,yk:()=>r.A});var r=t(66994),n=t(15653),s=t(8732),l=t(82015),i=t.n(l),o=t(43294),d=t(59549);let c={RadioGroup:({name:e,valueSelected:a,onChange:t,errorMessage:r,children:n})=>{let{errors:l,touched:d}=(0,o.useFormikContext)(),c=d[e]&&l[e];i().useMemo(()=>({name:e}),[e]);let u=i().Children.map(n,a=>i().isValidElement(a)&&function(e){return"object"==typeof e&&null!==e}(a.props)?i().cloneElement(a,{name:e,...a.props}):a);return(0,s.jsxs)("div",{children:[(0,s.jsx)("div",{className:"radio-group",children:u}),c&&(0,s.jsx)("div",{className:"invalid-feedback d-block",children:r||("string"==typeof l[e]?l[e]:String(l[e]))})]})},RadioItem:({id:e,label:a,value:t,name:r,disabled:n})=>{let{values:l,setFieldValue:i}=(0,o.useFormikContext)(),c=r||e;return(0,s.jsx)(d.A.Check,{type:"radio",id:e,label:a,value:t,name:c,checked:l[c]===t,onChange:e=>{i(c,e.target.value)},disabled:n,inline:!0})}};r.A,n.ks,n.s3},28778:(e,a,t)=>{t.d(a,{A:()=>l});var r=t(80860),n=t.n(r),s=t(19799);function l(e){return"boolean"==typeof e?e?s.A:n():e}},33120:(e,a,t)=>{t.d(a,{A:()=>x});var r=t(3892),n=t.n(r),s=t(82015),l=t(86842),i=t.n(l),o=t(65209),d=t.n(o),c=t(21964),u=t(80739),m=t(19799),v=t(28778),f=t(8732);let p=s.forwardRef(({bsPrefix:e,transition:a,...t},r)=>{let[{className:s,as:l="div",...o},{isActive:p,onEnter:x,onEntering:h,onEntered:b,onExit:j,onExiting:y,onExited:A,mountOnEnter:N,unmountOnExit:C,transition:g=m.A}]=(0,c.useTabPanel)({...t,transition:(0,v.A)(a)}),P=(0,u.oU)(e,"tab-pane");return(0,f.jsx)(d().Provider,{value:null,children:(0,f.jsx)(i().Provider,{value:null,children:(0,f.jsx)(g,{in:p,onEnter:x,onEntering:h,onEntered:b,onExit:j,onExiting:y,onExited:A,mountOnEnter:N,unmountOnExit:C,children:(0,f.jsx)(l,{...o,ref:r,className:n()(s,P,p&&"active")})})})})});p.displayName="TabPane";let x=p},58070:(e,a,t)=>{t.d(a,{A:()=>l});var r=t(8732);t(82015);var n=t(29780),s=t.n(n);let l=e=>(0,r.jsx)(s(),{...e})},66994:(e,a,t)=>{t.d(a,{A:()=>o});var r=t(8732),n=t(82015),s=t(43294),l=t(18622);let i=(0,n.forwardRef)((e,a)=>{let{children:t,onSubmit:n,autoComplete:i,className:o,onKeyPress:d,initialValues:c,...u}=e,m=l.object().shape({});return(0,r.jsx)(s.Formik,{initialValues:c||{},validationSchema:m,onSubmit:(e,a)=>{let t={preventDefault:()=>{},stopPropagation:()=>{},currentTarget:null,target:null,nativeEvent:new Event("submit"),bubbles:!1,cancelable:!0,defaultPrevented:!1,eventPhase:0,isTrusted:!1,timeStamp:Date.now(),type:"submit",isDefaultPrevented:()=>!1,isPropagationStopped:()=>!1,persist:()=>{}};n&&n(t,e,a)},...u,children:e=>(0,r.jsx)(s.Form,{ref:a,onSubmit:e.handleSubmit,autoComplete:i,className:o,onKeyPress:d,children:"function"==typeof t?t(e):t})})});i.displayName="ValidationFormWrapper";let o=i},72521:(e,a,t)=>{t.d(a,{A:()=>f});var r=t(29825),n=t.n(r),s=t(70947),l=t.n(s),i=t(28778),o=t(8732);let d=({transition:e,...a})=>(0,o.jsx)(l(),{...a,transition:(0,i.A)(e)});d.displayName="TabContainer";var c=t(92561),u=t(33120);let m={eventKey:n().oneOfType([n().string,n().number]),title:n().node.isRequired,disabled:n().bool,tabClassName:n().string,tabAttrs:n().object},v=()=>{throw Error("ReactBootstrap: The `Tab` component is not meant to be rendered! It's an abstract component that is only valid as a direct Child of the `Tabs` Component. For custom tabs components use TabPane and TabsContainer directly")};v.propTypes=m;let f=Object.assign(v,{Container:d,Content:c.A,Pane:u.A})},80237:(e,a)=>{Object.defineProperty(a,"A",{enumerable:!0,get:function(){return t}});var t=function(e){return e.PAGES="PAGES",e.PAGES_API="PAGES_API",e.APP_PAGE="APP_PAGE",e.APP_ROUTE="APP_ROUTE",e.IMAGE="IMAGE",e}({})},81413:(e,a)=>{Object.defineProperty(a,"M",{enumerable:!0,get:function(){return function e(a,t){return t in a?a[t]:"then"in a&&"function"==typeof a.then?a.then(a=>e(a,t)):"function"==typeof a&&"default"===t?a:void 0}}})},88271:(e,a,t)=>{t.a(e,async(e,r)=>{try{t.d(a,{A:()=>v});var n=t(8732),s=t(82015),l=t(12403),i=t(82053),o=t(54131),d=t(44233),c=t.n(d),u=t(88751),m=e([o]);o=(m.then?(await m)():m)[0];let v=({type:e,id:a})=>{let[t,r]=(0,s.useState)(5),{t:d}=(0,u.useTranslation)("common");return(0,s.useEffect)(()=>{t>0?setTimeout(()=>r(t-1),1e3):c().push({pathname:"/vspace/create",query:{id:a,source:e}})}),(0,n.jsxs)(l.A,{show:!0,children:[(0,n.jsx)("div",{className:"modal--align mt-2",children:(0,n.jsx)("div",{className:"modal--icon",children:(0,n.jsx)(i.FontAwesomeIcon,{icon:o.faCheck,color:"#4dc724",size:"4x"})})}),(0,n.jsx)("div",{className:"text-center mt-4",children:(0,n.jsxs)("p",{className:"lead",children:[e," ",d("vspace.formSubmittedSuccessfully"),(0,n.jsx)("br",{}),(0,n.jsx)("small",{children:d("vspace.vspaceCreationRedirect")}),(0,n.jsx)("br",{}),(0,n.jsx)("small",{children:(0,n.jsxs)("b",{children:[" ",d("vspace.waitFor")," ",t," ",d("vspace.waitForSec")]})})]})})]})};r()}catch(e){r(e)}})},92561:(e,a,t)=>{t.d(a,{A:()=>d});var r=t(82015),n=t(3892),s=t.n(n),l=t(80739),i=t(8732);let o=r.forwardRef(({className:e,bsPrefix:a,as:t="div",...r},n)=>(a=(0,l.oU)(a,"tab-content"),(0,i.jsx)(t,{ref:n,className:s()(e,a),...r})));o.displayName="TabContent";let d=o},96158:(e,a,t)=>{t.d(a,{A:()=>P});var r=t(82015),n=t(14332),s=t(70947),l=t.n(s),i=t(3892),o=t.n(i),d=t(9532),c=t.n(d),u=t(80739),m=t(44696),v=t(6417),f=t(8732);let p=r.forwardRef(({className:e,bsPrefix:a,as:t="div",...r},n)=>(a=(0,u.oU)(a,"nav-item"),(0,f.jsx)(t,{ref:n,className:o()(e,a),...r})));p.displayName="NavItem";var x=t(67776);let h=r.forwardRef((e,a)=>{let t,s,{as:l="div",bsPrefix:i,variant:d,fill:p=!1,justify:x=!1,navbar:h,navbarScroll:b,className:j,activeKey:y,...A}=(0,n.useUncontrolled)(e,{activeKey:"onSelect"}),N=(0,u.oU)(i,"nav"),C=!1,g=(0,r.useContext)(m.A),P=(0,r.useContext)(v.A);return g?(t=g.bsPrefix,C=null==h||h):P&&({cardHeaderBsPrefix:s}=P),(0,f.jsx)(c(),{as:l,ref:a,activeKey:y,className:o()(j,{[N]:!C,[`${t}-nav`]:C,[`${t}-nav-scroll`]:C&&b,[`${s}-${d}`]:!!s,[`${N}-${d}`]:!!d,[`${N}-fill`]:p,[`${N}-justified`]:x}),...A})});h.displayName="Nav";let b=Object.assign(h,{Item:p,Link:x.A});var j=t(92561),y=t(33120),A=t(58562),N=t(28778);function C(e){let{title:a,eventKey:t,disabled:r,tabClassName:n,tabAttrs:s,id:l}=e.props;return null==a?null:(0,f.jsx)(p,{as:"li",role:"presentation",children:(0,f.jsx)(x.A,{as:"button",type:"button",eventKey:t,disabled:r,id:l,className:n,...s,children:a})})}let g=e=>{let{id:a,onSelect:t,transition:r,mountOnEnter:s=!1,unmountOnExit:i=!1,variant:o="tabs",children:d,activeKey:c=function(e){let a;return(0,A.jJ)(e,e=>{null==a&&(a=e.props.eventKey)}),a}(d),...u}=(0,n.useUncontrolled)(e,{activeKey:"onSelect"});return(0,f.jsxs)(l(),{id:a,activeKey:c,onSelect:t,transition:(0,N.A)(r),mountOnEnter:s,unmountOnExit:i,children:[(0,f.jsx)(b,{id:a,...u,role:"tablist",as:"ul",variant:o,children:(0,A.Tj)(d,C)}),(0,f.jsx)(j.A,{children:(0,A.Tj)(d,e=>{let a={...e.props};return delete a.title,delete a.disabled,delete a.tabClassName,delete a.tabAttrs,(0,f.jsx)(y.A,{...a})})})]})};g.displayName="Tabs";let P=g}};