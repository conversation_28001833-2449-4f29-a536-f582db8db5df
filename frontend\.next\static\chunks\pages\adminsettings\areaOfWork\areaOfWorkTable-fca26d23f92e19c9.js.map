{"version": 3, "file": "static/chunks/pages/adminsettings/areaOfWork/areaOfWorkTable-fca26d23f92e19c9.js", "mappings": "0KAqCA,SAASA,EAASC,CAAoB,EACpC,GAAM,GAAEC,CAAC,CAAE,CAAGC,CAAAA,EAAAA,EAAAA,EAAAA,CAAcA,CAAC,UACvBC,EAA6B,CACjCC,gBAAiBH,EAAE,cACnB,EACI,SACJI,CAAO,MACPC,CAAI,WACJC,CAAS,uBACTC,CAAqB,WACrBC,CAAS,oBACTC,CAAkB,qBAClBC,CAAmB,CACnBC,kBAAgB,aAChBC,CAAW,oBACXC,CAAkB,gBAClBC,CAAc,SACdC,CAAO,WACPC,CAAS,sBACTC,CAAoB,mBACpBC,CAAiB,YACjBC,CAAU,QACVC,CAAM,kBACNC,CAAgB,cAChBC,CAAY,CAEZ,CADA,EACGC,EACJ,CAAGxB,EAGEyB,EAAiB,4BACrBtB,EACAuB,gBAAiBzB,EAAE,IAP0C,MAQ7D0B,UAAU,UACVtB,EACAC,KAAMA,GAAQ,EAAE,CAChBsB,OAAO,EACPC,2BAA4BrB,EAC5BsB,UAAWrB,EACXsB,gBAAiBf,qBACjBN,EACAsB,YAAY,EACZC,iBAAkBhB,EAClBiB,kBAAmBpB,GAA0C,GAC7DqB,eADwCrB,cACVD,GAA4B,CAAC,GAAI,GAAI,GAAI,CAA3BA,EAA+B,GAAG,CAC9EuB,oBAAqB7B,EACrB8B,oBAAqB1B,EACrB2B,aAAc1B,iBACdG,EACAG,yCACAC,EACAoB,kBAAmB,UAACC,EAAAA,CAAYA,CAAAA,CAAAA,GAChCC,SAAU,UAACC,IAAAA,CAAEC,UAAU,6CACvBvB,SACAC,eACAE,mBACAD,EACAqB,UAAW,WACb,EACA,MACE,UAACC,EAAAA,EAASA,CAAAA,CAAE,GAAGnB,CAAc,EAEjC,CAEA1B,EAAS8C,YAAY,CAAG,CACtBf,WAAW,EACXE,YAAY,EACZzB,UAAW,KACXU,WAAW,EACXC,qBAAsB,KACtBC,mBAAmB,EACnBC,YAAY,EACZE,kBAAkB,CACpB,EAEA,MAAevB,QAAQA,EAAC,SC/GxB,4CACA,4CACA,WACA,OAAe,EAAQ,KAAiE,CACxF,EACA,SAFsB,qKCgItB,MAzHwB,IACpB,GAAM,GAAEE,CAAC,CAAE,CAAGC,CAAAA,EAAAA,EAAAA,EAAAA,CAAcA,CAAC,MAwHlB4C,IAvHL,CAACC,EAAWC,EAAe,CAAGC,CAAAA,EAAAA,EAAAA,EAuHT,MAvHSA,CAAQA,CAAC,EAAE,EACzC,EAAGC,EAAW,CAAGD,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,EAAC,GAC1B,CAAC1C,EAAW4C,EAAa,CAAGF,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,CAAC,GACrC,CAACG,EAASC,EAAW,CAAGJ,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,CAAC,IACjC,CAACK,EAAaC,EAAS,CAAGN,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,EAAC,GACnC,CAACO,EAAmBC,EAAqB,CAAGR,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,CAAC,CAAC,GAGtDS,EAAmB,CACrBC,KAAM,CAAEC,MAAO,KAAM,EACrBC,MAAOT,EACPU,KAAM,EACNC,MAAO,CAAC,CACZ,EAEM1D,EAAU,CACZ,CACI2D,KAAM/D,EAAE,uCACRgE,SAAU,OACd,EACA,CACID,KAAM/D,EAAE,wCACRgE,SAAU,GACVC,KAAM,GACF,WAACC,MAAAA,WACG,WAACC,IAAIA,CAACC,KAAK,6BAA6BC,GAAI,OAAvCF,6BAAiF,OAANG,EAAEC,GAAG,YAEhF,IACD,UAAC9B,IAAAA,CAAEC,UAAU,wBAEV,OAEP,UAAC8B,IAAAA,CAAEC,QAAS,IAAMC,EAAWJ,YACzB,UAAC7B,IAAAA,CAAEC,UAAU,4BACZ,MAGjB,EACH,CAEKiC,EAAoB,MAAOC,IAC7B3B,GAAW,GACX,IAAM4B,EAAW,MAAMC,EAAAA,CAAUA,CAACC,GAAG,CAAC,cAAeH,GACjDC,GAAYA,EAASxE,IAAI,EAAIwE,EAASxE,IAAI,CAAC2E,MAAM,CAAG,GAAG,CACvDjC,EAAe8B,EAASxE,IAAI,EAC5B6C,EAAa2B,EAASI,UAAU,EAChChC,GAAW,GAEnB,EAQMvC,EAAsB,MAAOwE,EAAiBrB,KAChDJ,EAAiBG,KAAK,CAAGsB,EACzBzB,EAAiBI,IAAI,CAAGA,EACxBZ,GAAW,GACX,IAAM4B,EAAW,MAAMC,EAAAA,CAAUA,CAACC,GAAG,CAAC,cAAetB,GACjDoB,GAAYA,EAASxE,IAAI,EAAIwE,EAASxE,IAAI,CAAC2E,MAAM,CAAG,GAAG,CACvDjC,EAAe8B,EAASxE,IAAI,EAC5B+C,EAAW8B,GACXjC,GAAW,GAEnB,EAEMyB,EAAa,MAAOS,IACtB3B,EAAqB2B,EAAIZ,GAAG,EAC5BjB,GAAS,EACb,EAEM8B,EAAe,UACjB,GAAI,CACA,MAAMN,EAAAA,CAAUA,CAACO,MAAM,CAAC,eAAiC,OAAlB9B,IACvCoB,EAAkBlB,GAClBH,GAAS,GACTgC,EAAAA,EAAKA,CAACC,OAAO,CAACvF,EAAE,+DACpB,CAAE,MAAOwF,EAAO,CACZF,EAAAA,EAAKA,CAACE,KAAK,CAACxF,EAAE,yDAClB,CACJ,EAEMyF,EAAY,IAAMnC,GAAS,GAMjC,MAJAoC,CAAAA,EAAAA,EAAAA,SAAAA,CAASA,CAAC,KACNf,EAAkBlB,EACtB,EAAG,EAAE,EAGD,WAACS,MAAAA,WACG,WAACyB,EAAAA,CAAKA,CAAAA,CAACC,KAAMvC,EAAawC,OAAQJ,YAC9B,UAACE,EAAAA,CAAKA,CAACG,MAAM,EAACC,WAAW,aACrB,UAACJ,EAAAA,CAAKA,CAACK,KAAK,WAAEhG,EAAE,sDAEpB,UAAC2F,EAAAA,CAAKA,CAACM,IAAI,WAAEjG,EAAE,yEACf,WAAC2F,EAAAA,CAAKA,CAACO,MAAM,YACT,UAACC,EAAAA,CAAMA,CAAAA,CAACC,QAAQ,YAAY3B,QAASgB,WAChCzF,EAAE,0CAEP,UAACmG,EAAAA,CAAMA,CAAAA,CAACC,QAAQ,UAAU3B,QAASW,WAC9BpF,EAAE,6CAKf,UAACF,EAAAA,CAAQA,CAAAA,CACLM,QAASA,EACTC,KAAMyC,EACNxC,UAAWA,EACXU,WAAW,EACXN,oBAAqBA,EACrBC,iBA/Da,CA+DKA,GA9D1B8C,EAAiBG,KAAK,CAAGT,EACzBM,EAAiBI,IAAI,CAAGA,EACxBc,EAAkBlB,EACtB,MA+DJ", "sources": ["webpack://_N_E/./components/common/RKITable.tsx", "webpack://_N_E/?2854", "webpack://_N_E/./pages/adminsettings/areaOfWork/areaOfWorkTable.tsx"], "sourcesContent": ["//Import Library\r\nimport React from 'react'\r\nimport DataTable  from 'react-data-table-component';\r\n\r\n//Import services/components\r\nimport { useTranslation } from 'next-i18next';\r\nimport CustomLoader from './CustomLoader';\r\n\r\ninterface RKITableProps {\r\n  columns: any[];\r\n  data: any[];\r\n  totalRows?: number;\r\n  resetPaginationToggle?: boolean;\r\n  subheader?: boolean;\r\n  subHeaderComponent?: React.ReactNode;\r\n  handlePerRowsChange?: (newPerPage: number, page: number) => void;\r\n  handlePageChange?: (page: number) => void;\r\n  rowsPerPage?: number[];\r\n  defaultRowsPerPage?: number;\r\n  selectableRows?: boolean;\r\n  loading?: boolean;\r\n  pagServer?: boolean;\r\n  onSelectedRowsChange?: (selectedRows: any) => void;\r\n  clearSelectedRows?: boolean;\r\n  sortServer?: boolean;\r\n  onSort?: (column: any, sortDirection: string) => void;\r\n  persistTableHead?: boolean;\r\n  sortFunction?: (rows: any[], field: any, direction: 'asc' | 'desc') => any[];\r\n  noHeader?: boolean;\r\n  dense?: boolean;\r\n  pagination?: boolean;\r\n  paginationServer?: boolean;\r\n  paginationTotalRows?: number;\r\n  subHeaderAlign?: string;\r\n  [key: string]: any;\r\n}\r\n\r\nfunction RKITable(props: RKITableProps) {\r\n  const { t } = useTranslation('common');\r\n  const paginationComponentOptions = {\r\n    rowsPerPageText: t('Rowsperpage'),\r\n    };\r\n  const {\r\n    columns,\r\n    data,\r\n    totalRows,\r\n    resetPaginationToggle,\r\n    subheader,\r\n    subHeaderComponent,\r\n    handlePerRowsChange,\r\n    handlePageChange,\r\n    rowsPerPage,\r\n    defaultRowsPerPage,\r\n    selectableRows,\r\n    loading,\r\n    pagServer,\r\n    onSelectedRowsChange,\r\n    clearSelectedRows,\r\n    sortServer,\r\n    onSort,\r\n    persistTableHead,\r\n    sortFunction,\r\n    // Filter out any unknown props that might cause DOM warnings\r\n    ...otherProps\r\n  } = props;\r\n\r\n  // Only pass known DataTable props to avoid DOM warnings\r\n  const dataTableProps = {\r\n    paginationComponentOptions,\r\n    noDataComponent: t(\"NoData\"),\r\n    noHeader: true,\r\n    columns,\r\n    data: data || [],\r\n    dense: true,\r\n    paginationResetDefaultPage: resetPaginationToggle,\r\n    subHeader: subheader,\r\n    progressPending: loading,\r\n    subHeaderComponent,\r\n    pagination: true,\r\n    paginationServer: pagServer,\r\n    paginationPerPage: defaultRowsPerPage ? defaultRowsPerPage : 10,\r\n    paginationRowsPerPageOptions: rowsPerPage ? rowsPerPage : [10, 15, 20, 25, 30],\r\n    paginationTotalRows: totalRows,\r\n    onChangeRowsPerPage: handlePerRowsChange,\r\n    onChangePage: handlePageChange,\r\n    selectableRows,\r\n    onSelectedRowsChange,\r\n    clearSelectedRows,\r\n    progressComponent: <CustomLoader/>,\r\n    sortIcon: <i className=\"sort-icon fas fa-exchange-alt\" />,\r\n    sortServer,\r\n    onSort,\r\n    sortFunction,\r\n    persistTableHead,\r\n    className: \"rki-table\"\r\n  };\r\n  return (\r\n    <DataTable {...dataTableProps} />\r\n  )\r\n};\r\n\r\nRKITable.defaultProps = {\r\n  subHeader: false,\r\n  pagination: true,\r\n  totalRows: null,\r\n  pagServer: true,\r\n  onSelectedRowsChange: null,\r\n  clearSelectedRows: false,\r\n  sortServer: false,\r\n  persistTableHead: false\r\n}\r\n\r\nexport default RKITable;\r\n\r\n", "\n    (window.__NEXT_P = window.__NEXT_P || []).push([\n      \"/adminsettings/areaOfWork/areaOfWorkTable\",\n      function () {\n        return require(\"private-next-pages/adminsettings/areaOfWork/areaOfWorkTable.tsx\");\n      }\n    ]);\n    if(module.hot) {\n      module.hot.dispose(function () {\n        window.__NEXT_P.push([\"/adminsettings/areaOfWork/areaOfWorkTable\"])\n      });\n    }\n  ", "//Import Library\r\nimport { useState, useEffect } from \"react\";\r\nimport Link from \"next/link\";\r\nimport { <PERSON><PERSON>, Button } from \"react-bootstrap\";\r\nimport toast from 'react-hot-toast';\r\n\r\n//Import services/components\r\nimport RKITable from \"../../../components/common/RKITable\";\r\nimport apiService from \"../../../services/apiService\";\r\nimport { useTranslation } from 'next-i18next';\r\n\r\nconst AreaOfWorkTable = (_props: any) => {\r\n    const { t } = useTranslation('common');\r\n    const [tabledata, setDataToTable] = useState([]);\r\n    const [, setLoading] = useState(false);\r\n    const [totalRows, setTotalRows] = useState(0);\r\n    const [perPage, setPerPage] = useState(10);\r\n    const [isModalShow, setModal] = useState(false);\r\n    const [AreaOfWorkCountry, setAreaOfWorkCountry] = useState({});\r\n\r\n\r\n    const areaOfWorkParams = {\r\n        sort: { title: \"asc\" },\r\n        limit: perPage,\r\n        page: 1,\r\n        query: {},\r\n    };\r\n\r\n    const columns = [\r\n        {\r\n            name: t(\"adminsetting.areaofwork.Table.Title\"),\r\n            selector: \"title\",\r\n        },\r\n        {\r\n            name: t(\"adminsetting.areaofwork.Table.Action\"),\r\n            selector: \"\",\r\n            cell: (d: any) => (\r\n                <div>\r\n                    <Link href=\"/adminsettings/[...routes]\" as={`/adminsettings/edit_area_of_work/${d._id}`}>\r\n\r\n                        {\" \"}\r\n                        <i className=\"icon fas fa-edit\" />\r\n\r\n                    </Link>\r\n                    &nbsp;\r\n                    <a onClick={() => userAction(d)}>\r\n                        <i className=\"icon fas fa-trash-alt\" />\r\n                    </a>{\" \"}\r\n                </div>\r\n            ),\r\n        },\r\n    ];\r\n\r\n    const getAredOfWorkData = async (areaOfWorkParamsinitials: any) => {\r\n        setLoading(true);\r\n        const response = await apiService.get(\"/areaofwork\", areaOfWorkParamsinitials);\r\n        if (response && response.data && response.data.length > 0) {\r\n            setDataToTable(response.data);\r\n            setTotalRows(response.totalCount);\r\n            setLoading(false);\r\n        }\r\n    };\r\n\r\n    const handlePageChange = (page: any) => {\r\n        areaOfWorkParams.limit = perPage;\r\n        areaOfWorkParams.page = page;\r\n        getAredOfWorkData(areaOfWorkParams);\r\n    };\r\n\r\n    const handlePerRowsChange = async (newPerPage: any, page: any) => {\r\n        areaOfWorkParams.limit = newPerPage;\r\n        areaOfWorkParams.page = page;\r\n        setLoading(true);\r\n        const response = await apiService.get(\"/areaofwork\", areaOfWorkParams);\r\n        if (response && response.data && response.data.length > 0) {\r\n            setDataToTable(response.data);\r\n            setPerPage(newPerPage);\r\n            setLoading(false);\r\n        }\r\n    };\r\n\r\n    const userAction = async (row: any) => {\r\n        setAreaOfWorkCountry(row._id);\r\n        setModal(true);\r\n    };\r\n\r\n    const modalConfirm = async () => {\r\n        try {\r\n            await apiService.remove(`/areaofwork/${AreaOfWorkCountry}`);\r\n            getAredOfWorkData(areaOfWorkParams);\r\n            setModal(false);\r\n            toast.success(t(\"adminsetting.areaofwork.Table.areaOfWorkDeletedSuccessfully\"));\r\n        } catch (error) {\r\n            toast.error(t(\"adminsetting.areaofwork.Table.errorDeletingAreaOfWork\"));\r\n        }\r\n    };\r\n\r\n    const modalHide = () => setModal(false);\r\n\r\n    useEffect(() => {\r\n        getAredOfWorkData(areaOfWorkParams);\r\n    }, []);\r\n\r\n    return (\r\n        <div>\r\n            <Modal show={isModalShow} onHide={modalHide}>\r\n                <Modal.Header closeButton>\r\n                    <Modal.Title>{t(\"adminsetting.areaofwork.Table.DeleteAreaofwork\")}</Modal.Title>\r\n                </Modal.Header>\r\n                <Modal.Body>{t(\"adminsetting.areaofwork.Table.Areyousurewanttodeletethisareaofwork?\")}</Modal.Body>\r\n                <Modal.Footer>\r\n                    <Button variant=\"secondary\" onClick={modalHide}>\r\n                        {t(\"adminsetting.areaofwork.Table.Cancel\")}\r\n                    </Button>\r\n                    <Button variant=\"primary\" onClick={modalConfirm}>\r\n                        {t(\"adminsetting.areaofwork.Table.Yes\")}\r\n                    </Button>\r\n                </Modal.Footer>\r\n            </Modal>\r\n\r\n            <RKITable\r\n                columns={columns}\r\n                data={tabledata}\r\n                totalRows={totalRows}\r\n                pagServer={true}\r\n                handlePerRowsChange={handlePerRowsChange}\r\n                handlePageChange={handlePageChange}\r\n            />\r\n        </div>\r\n    );\r\n};\r\n\r\nexport default AreaOfWorkTable;\r\n"], "names": ["RKITable", "props", "t", "useTranslation", "paginationComponentOptions", "rowsPerPageText", "columns", "data", "totalRows", "resetPaginationToggle", "subheader", "subHeaderComponent", "handlePerRowsChange", "handlePageChange", "rowsPerPage", "defaultRowsPerPage", "selectableRows", "loading", "pagServer", "onSelectedRowsChange", "clearSelectedRows", "sortServer", "onSort", "persistTableHead", "sortFunction", "otherProps", "dataTableProps", "noDataComponent", "<PERSON><PERSON><PERSON><PERSON>", "dense", "paginationResetDefaultPage", "subHeader", "progressPending", "pagination", "paginationServer", "paginationPerPage", "paginationRowsPerPageOptions", "paginationTotalRows", "onChangeRowsPerPage", "onChangePage", "progressComponent", "CustomLoader", "sortIcon", "i", "className", "DataTable", "defaultProps", "AreaOfWorkTable", "tabledata", "setDataToTable", "useState", "setLoading", "setTotalRows", "perPage", "setPerPage", "isModalShow", "setModal", "AreaOfWorkCountry", "setAreaOfWorkCountry", "areaOfWorkParams", "sort", "title", "limit", "page", "query", "name", "selector", "cell", "div", "Link", "href", "as", "d", "_id", "a", "onClick", "userAction", "getAredOfWorkData", "areaOfWorkParamsinitials", "response", "apiService", "get", "length", "totalCount", "newPerPage", "row", "modalConfirm", "remove", "toast", "success", "error", "modalHide", "useEffect", "Modal", "show", "onHide", "Header", "closeButton", "Title", "Body", "Footer", "<PERSON><PERSON>", "variant"], "sourceRoot": "", "ignoreList": []}