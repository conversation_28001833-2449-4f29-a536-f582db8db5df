{"version": 3, "file": "static/chunks/pages/project/components/ProjectDetailsAccordion-ad69ef3e9a3c60dd.js", "mappings": "uKAMA,IAAMA,EAAwBC,EAAAA,OAAb,GAA6B,CAAC,GAK5CC,IALyB,IAAoB,WAC9CC,CAAS,UACTC,CAAQ,CACRC,GAAIC,EAAY,KAAK,CACrB,GAAGC,EACJ,GAEC,OADAH,EAAWI,CAAAA,EAAAA,EAAAA,EAAAA,CAAkBA,CAACJ,EAAU,aACpBK,CAAAA,EAAAA,EAAAA,GAAAA,CAAIA,CAACH,EAAW,CAClCJ,IAAKA,EACLC,UAAWO,IAAWP,EAAWC,GACjC,GAAGG,CAAK,EAEZ,GACAP,EAJyBU,WAIL,CAAG,WCbvB,IAAMC,EAA0BV,EAAAA,SAAb,CAA6B,CAAC,GAK9CC,MAL2B,EAAoB,CAChDC,WAAS,UACTC,CAAQ,CACRC,GAAIC,EAAY,KAAK,CACrB,GAAGC,EACJ,GAEC,OADAH,EAAWI,CAAAA,EAAAA,EAAAA,EAAAA,CAAkBA,CAACJ,EAAU,eACpBK,CAAAA,EAAAA,EAAAA,GAAAA,CAAIA,CAACH,EAAW,CAClCJ,IAAKA,EACLC,UAAWO,IAAWP,EAAWC,GACjC,GAAGG,CAAK,EAEZ,GACAI,EAAWC,WAAW,CAAG,4BCXzB,IAAMC,EAA0BZ,EAAAA,SAAb,CAA6B,CAAC,GAM9CC,MAN2B,EAAoB,UAChDE,CAAQ,CACRD,WAAS,CAETE,CADA,EACIC,EAAY,KAAK,CACrB,GAAGC,EACJ,GACOO,EAASN,CAAAA,EAAAA,EAAAA,EAAAA,CAAkBA,CAACJ,EAAU,eACtCW,EAAeC,CAAAA,EAAAA,EAAAA,OAAAA,CAAOA,CAAC,IAAO,EAClCC,mBAAoBH,EACtB,EAAI,CAACA,EAAO,EACZ,MAAoBL,CAAb,EAAaA,EAAAA,GAAAA,CAAIA,CAACS,EAAP,CAAwBA,CAACC,QAAQ,CAAE,CACnDC,MAAOL,EACPM,SAAuBZ,CAAb,EAAaA,EAAAA,GAAAA,CAAIA,CAACH,EAAW,CACrCJ,IAAKA,EACL,GAAGK,CAAK,CACRJ,UAAWO,IAAWP,EAAWW,EACnC,EACF,EACF,EACAD,GAAWD,GAJgBF,QAIL,CAAG,aCtBzB,IAAMY,EAAuBrB,EAAAA,MAAb,IAA6B,CAC7C,CACA,EAMGC,GARwB,KAE1B,CACCE,UAAQ,WACRD,CAAS,SACToB,CAAO,CACPlB,GAAIC,EAAY,KAAK,CACrB,GAAGC,EACJ,GACOO,EAASN,CAAAA,EAAAA,EAAAA,EAAAA,CAAkBA,CAACJ,EAAU,YAC5C,MAAoBK,CAAb,EAAaA,EAAAA,GAAAA,CAAIA,CAACH,EAAW,CAClCJ,IAAKA,EACLC,UAAWO,IAAWa,EAAU,GAAaA,MAAAA,CAAVT,EAAO,EAArBJ,GAAgC,OAARa,CAX0G,EAW9FT,EAAQX,GACjE,GAAGI,CAAK,EAEZ,GACAe,EAAQV,WAAW,CAAG,UChBtB,IAAMY,EAA8BvB,EAAAA,UAAgB,CAAC,EAA9B,CAKpBC,QALmD,EAApB,SAChCC,CAAS,UACTC,CAAQ,CACRC,GAAIC,EAAY,KAAK,CACrB,GAAGC,EACJ,GAEC,OADAH,EAAWI,CAAAA,EAAAA,EAAAA,EAAAA,CAAkBA,CAACJ,EAAU,oBACpBK,CAAAA,EAAAA,EAAAA,GAAAA,CAAIA,CAACH,EAAW,CAClCJ,IAAKA,EACLC,UAAWO,IAAWP,EAAWC,GACjC,GAAGG,CAAK,EAEZ,EACAiB,GAAeZ,WAAW,CAAG,iBCb7B,IAAMa,EAAwBxB,EAAAA,OAAb,GAA6B,CAAC,GAK5CC,IALyB,IAAoB,WAC9CC,CAAS,UACTC,CAAQ,CACRC,GAAIC,EAAY,GAAG,CACnB,GAAGC,EACJ,GAEC,OADAH,EAAWI,CAAAA,EAAAA,EAAAA,EAAAA,CAAkBA,CAACJ,EAAU,aACpBK,CAAAA,EAAAA,EAAAA,GAAAA,CAAIA,CAACH,EAAW,CAClCJ,IAAKA,EACLC,UAAWO,IAAWP,EAAWC,GACjC,GAAGG,CAAK,EAEZ,EACAkB,GAASb,WAAW,CAAG,0BCZvB,IAAMc,EAAgBC,CAAAA,EAAAA,EAAAA,CAAAA,CAAgBA,CAAC,MACjCC,EAA4B3B,EAAAA,UAAgB,CAAC,GAKhDC,QALiD,WAClDC,CAAS,UACTC,CAAQ,CACRC,GAAIC,EAAYoB,CAAa,CAC7B,GAAGnB,EACJ,GAEC,OADAH,EAAWI,CAAAA,EAAAA,EAAAA,EAAAA,CAAkBA,CACX,EADsB,iBACpBC,CAAAA,EAAAA,EAAAA,GAAAA,CAAIA,CAACH,EAAW,CAClCJ,IAAKA,EACLC,UAAWO,IAAWP,EAAWC,GACjC,GAAGG,CAAK,EAEZ,EACAqB,GAAahB,WAAW,CAAG,eCf3B,IAAMiB,EAAwB5B,EAAAA,OAAb,GAA6B,CAAC,GAK5CC,IALyB,IAAoB,CAC9CC,WAAS,UACTC,CAAQ,CACRC,GAAIC,EAAY,GAAG,CACnB,GAAGC,EACJ,GAEC,OADAH,EAAWI,CAAAA,EAAAA,EAAAA,EAAAA,CAAkBA,CACX,EADsB,aACpBC,CAAAA,EAAAA,EAAAA,GAAAA,CAAIA,CAACH,EAAW,CAClCJ,IAAKA,EACLC,UAAWO,IAAWP,EAAWC,GACjC,GAAGG,CAAK,EAEZ,GACAsB,EAASjB,WAAW,CAAG,WCZvB,IAAMkB,EAAgBH,CAAAA,EAAAA,EAAAA,CAAAA,CAAgBA,CAAC,MACjCI,EAAyB9B,EAAAA,QAAb,EAA6B,CAAC,GAK7CC,KAL0B,GAAoB,CAC/CC,WAAS,UACTC,CAAQ,CACRC,GAAIC,EAAYwB,CAAa,CAC7B,GAAGvB,EACJ,GAEC,OADAH,EAAWI,CAAAA,EAAAA,EAAAA,EAAAA,CAAkBA,CACX,EADsB,cACpBC,CAAAA,EAAAA,EAAAA,GAAAA,CAAIA,CAACH,EAAW,CAClCJ,IAAKA,EACLC,UAAWO,IAAWP,EAAWC,GACjC,GAAGG,CAAK,EAEZ,GACAwB,EAAUnB,WAAW,CAAG,YCNxB,IAAMoB,EAAoB/B,EAAAA,GAAb,OAA6B,CAAC,GAAnB,QAAoB,UAC1CG,CAAQ,WACRD,CAAS,IACT8B,CAAE,MACFC,CAAI,QACJC,CAAM,CACNC,QAAO,CAAK,UACZf,CAAQ,CAERhB,CADA,EACIC,EAAY,KAAK,CACrB,GAAGC,EACJ,GACOO,EAASN,CAAAA,EAAAA,EAAAA,EAAAA,CAAkBA,CAACJ,EAAU,QAC5C,MAAoBK,CAAb,EAAaA,EAAAA,GAAAA,CAAIA,CAACH,EAAP,CAChBJ,IAAKA,EACL,GAAGK,CAAK,CACRJ,UAAWO,IAAWP,EAAWW,EAAQmB,GAAM,MAAS,GAAnCvB,GAAmC,CAAHuB,GAAMC,GAAQ,QAAa,OAALA,GAAQC,GAAU,UAAiB,OAAPA,IACvGd,IATyJ,KAS/Ie,EAAoB3B,CAAAA,EAAAA,EAAb,GAAaA,CAAIA,CAACT,EAAU,CAC3CqB,GAD0B,MAChBA,CACZ,GAAKA,CACP,EACF,GACAW,EAAKpB,WAAW,CAAG,OACnB,MAAeyB,OAAOC,MAAM,CAACN,EAAM,CACjCO,INhBajB,CMgBRA,CACLkB,KNjBoBlB,CKDPS,CLCQ,CMkBrBU,EAFYnB,KDjBUS,EAAC,CCmBbH,CACVc,CAFgBX,ITpBH/B,CSsBPA,CACN2C,GHrByBf,EDFZH,CLAQzB,CSwBrB4C,CTxBsB,GSsBR5C,CFtBD6B,CFAQJ,CIyBrBoB,CJzBsB,GIuBRpB,EFvBOI,CLSRhB,CKTS,CE0BtBiC,EAFcjB,KRxBDlB,CCSUE,COkBvBkC,CPlBwB,GOgBNlC,IRzBKF,EAAC,CGAXa,CK2BDA,CADMb,CAElB,EAAC,SL5B0Ba,EAAC,GK2BFA,kJCC5B,MArCgC,QAuBtBjB,EAtBN,IAAMyC,EAAa,YAoCRC,UAlCL,GAAEC,CAAC,CAAE,CAAGC,CAAAA,EAAAA,EAAAA,EAAAA,CAAcA,CAAC,UACvB,CAACC,EAASC,EAAW,CAAGC,CAAAA,EAAAA,EAAAA,QAAAA,CAAQA,EAAC,GACvC,MACI,+BACQ,WAACC,EAAAA,CAASA,CAACC,IAAI,EAACC,SAAS,cACrB,WAACF,EAAAA,CAASA,CAACV,MAAM,EAACa,QAAS,IAAML,EAAW,CAACD,aACzC,UAACO,MAAAA,CAAIxD,UAAU,qBAAa+C,EAAE,oBAC9B,UAACS,MAAAA,CAAIxD,UAAU,qBACViD,EAAU,UAACQ,EAAAA,CAAeA,CAAAA,CAACC,KAAMC,EAAAA,GAAMA,CAAEC,MAAM,SAC5C,UAACH,EAAAA,CAAeA,CAAAA,CAACC,KAAMG,EAAAA,GAAOA,CAAED,MAAM,cAGlD,UAACR,EAAAA,CAASA,CAACb,IAAI,WACP,WAACV,EAAAA,CAAIA,CAACY,IAAI,EAACzC,UAAU,gCACjB,WAAC8D,IAAAA,WAAE,UAACC,IAAAA,UAAGhB,EAAE,YAAc,IAAC,WAACiB,OAAAA,WAAK,IAAE5D,EAAM6D,OAAO,CAACC,MAAM,CAAG9D,EAAM6D,OAAO,CAACC,MAAM,CAAC,KAAQ,CAAG,SACvF,WAACJ,IAAAA,WAAE,UAACC,IAAAA,UAAGhB,EAAE,aAAe,IAAC,WAACiB,OAAAA,WAAK,IAAE5D,EAAM6D,OAAO,CAACE,UAAU,CAAIC,IAAOhE,EAAM6D,OAAO,CAACE,UAAU,EAAEE,MAAM,CAACxB,GAAe,KAAK,IAAtDuB,MACnE,WAACN,IAAAA,WAAE,UAACC,IAAAA,UAAGhB,EAAE,aAAe,IAAC,WAACiB,OAAAA,WAAK,IAAE5D,EAAM6D,OAAO,CAACK,QAAQ,CAAIF,IAAOhE,EAAM6D,OAAO,CAACK,QAAQ,EAAED,MAAM,CAjBhG,cAiBiED,WACjE,WAACN,IAAAA,WAAE,UAACC,IAAAA,UAAGhB,EAAE,kBAAoB,IAAC,WAACiB,OAAAA,WAAK,IAAE5D,EAAM6D,OAAO,CAACM,UAAU,CAAIH,IAAOhE,EAAM6D,OAAO,CAACM,UAAU,EAAEF,MAAM,CAACxB,GAAe,KAAK,IAAtDuB,MACxE,WAACN,IAAAA,WAAE,UAACC,IAAAA,UAAGhB,EAAE,sBAAwB,IACzD,UAACiB,OAAAA,UACC5D,CAAAA,MAAAA,GAAAA,EAAM6D,OAAAA,EAAN7D,KAAAA,EAAAA,EAAeoE,GAAfpE,IAAeoE,GACf,UAACC,IAAAA,CAAEC,KAAMtE,EAAM6D,OAAO,CAACO,OAAO,CAAEG,OAAQ,kBACvCvE,EAAM6D,OAAO,CAACO,OAAO,iBAUhC,mBC7CA,4CACA,8CACA,WACA,OAAe,EAAQ,KAAmE,CAC1F,EACA,SAFsB,wCCDtB,IAAMI,EAAuB9E,QAAb,CAAaA,UAAF,GAAqB,CAAC,MACjD8E,EAAQnE,WAAW,CAAG,oBACtB,MAAemE,OAAOA,EAAC", "sources": ["webpack://_N_E/./node_modules/react-bootstrap/esm/CardBody.js", "webpack://_N_E/./node_modules/react-bootstrap/esm/CardFooter.js", "webpack://_N_E/./node_modules/react-bootstrap/esm/CardHeader.js", "webpack://_N_E/./node_modules/react-bootstrap/esm/CardImg.js", "webpack://_N_E/./node_modules/react-bootstrap/esm/CardImgOverlay.js", "webpack://_N_E/./node_modules/react-bootstrap/esm/CardLink.js", "webpack://_N_E/./node_modules/react-bootstrap/esm/CardSubtitle.js", "webpack://_N_E/./node_modules/react-bootstrap/esm/CardText.js", "webpack://_N_E/./node_modules/react-bootstrap/esm/CardTitle.js", "webpack://_N_E/./node_modules/react-bootstrap/esm/Card.js", "webpack://_N_E/./pages/project/components/ProjectDetailsAccordion.tsx", "webpack://_N_E/?12fb", "webpack://_N_E/./node_modules/react-bootstrap/esm/CardHeaderContext.js"], "sourcesContent": ["\"use client\";\n\nimport * as React from 'react';\nimport classNames from 'classnames';\nimport { useBootstrapPrefix } from './ThemeProvider';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst CardBody = /*#__PURE__*/React.forwardRef(({\n  className,\n  bsPrefix,\n  as: Component = 'div',\n  ...props\n}, ref) => {\n  bsPrefix = useBootstrapPrefix(bsPrefix, 'card-body');\n  return /*#__PURE__*/_jsx(Component, {\n    ref: ref,\n    className: classNames(className, bsPrefix),\n    ...props\n  });\n});\nCardBody.displayName = 'CardBody';\nexport default CardBody;", "\"use client\";\n\nimport * as React from 'react';\nimport classNames from 'classnames';\nimport { useBootstrapPrefix } from './ThemeProvider';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst CardFooter = /*#__PURE__*/React.forwardRef(({\n  className,\n  bsPrefix,\n  as: Component = 'div',\n  ...props\n}, ref) => {\n  bsPrefix = useBootstrapPrefix(bsPrefix, 'card-footer');\n  return /*#__PURE__*/_jsx(Component, {\n    ref: ref,\n    className: classNames(className, bsPrefix),\n    ...props\n  });\n});\nCardFooter.displayName = 'CardFooter';\nexport default CardFooter;", "\"use client\";\n\nimport classNames from 'classnames';\nimport * as React from 'react';\nimport { useMemo } from 'react';\nimport { useBootstrapPrefix } from './ThemeProvider';\nimport CardHeaderContext from './CardHeaderContext';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst CardHeader = /*#__PURE__*/React.forwardRef(({\n  bsPrefix,\n  className,\n  // Need to define the default \"as\" during prop destructuring to be compatible with styled-components github.com/react-bootstrap/react-bootstrap/issues/3595\n  as: Component = 'div',\n  ...props\n}, ref) => {\n  const prefix = useBootstrapPrefix(bsPrefix, 'card-header');\n  const contextValue = useMemo(() => ({\n    cardHeaderBsPrefix: prefix\n  }), [prefix]);\n  return /*#__PURE__*/_jsx(CardHeaderContext.Provider, {\n    value: contextValue,\n    children: /*#__PURE__*/_jsx(Component, {\n      ref: ref,\n      ...props,\n      className: classNames(className, prefix)\n    })\n  });\n});\nCardHeader.displayName = 'CardHeader';\nexport default CardHeader;", "\"use client\";\n\nimport classNames from 'classnames';\nimport * as React from 'react';\nimport { useBootstrapPrefix } from './ThemeProvider';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst CardImg = /*#__PURE__*/React.forwardRef(\n// Need to define the default \"as\" during prop destructuring to be compatible with styled-components github.com/react-bootstrap/react-bootstrap/issues/3595\n({\n  bsPrefix,\n  className,\n  variant,\n  as: Component = 'img',\n  ...props\n}, ref) => {\n  const prefix = useBootstrapPrefix(bsPrefix, 'card-img');\n  return /*#__PURE__*/_jsx(Component, {\n    ref: ref,\n    className: classNames(variant ? `${prefix}-${variant}` : prefix, className),\n    ...props\n  });\n});\nCardImg.displayName = 'CardImg';\nexport default CardImg;", "\"use client\";\n\nimport * as React from 'react';\nimport classNames from 'classnames';\nimport { useBootstrapPrefix } from './ThemeProvider';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst CardImgOverlay = /*#__PURE__*/React.forwardRef(({\n  className,\n  bsPrefix,\n  as: Component = 'div',\n  ...props\n}, ref) => {\n  bsPrefix = useBootstrapPrefix(bsPrefix, 'card-img-overlay');\n  return /*#__PURE__*/_jsx(Component, {\n    ref: ref,\n    className: classNames(className, bsPrefix),\n    ...props\n  });\n});\nCardImgOverlay.displayName = 'CardImgOverlay';\nexport default CardImgOverlay;", "\"use client\";\n\nimport * as React from 'react';\nimport classNames from 'classnames';\nimport { useBootstrapPrefix } from './ThemeProvider';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst CardLink = /*#__PURE__*/React.forwardRef(({\n  className,\n  bsPrefix,\n  as: Component = 'a',\n  ...props\n}, ref) => {\n  bsPrefix = useBootstrapPrefix(bsPrefix, 'card-link');\n  return /*#__PURE__*/_jsx(Component, {\n    ref: ref,\n    className: classNames(className, bsPrefix),\n    ...props\n  });\n});\nCardLink.displayName = 'CardLink';\nexport default CardLink;", "\"use client\";\n\nimport * as React from 'react';\nimport classNames from 'classnames';\nimport { useBootstrapPrefix } from './ThemeProvider';\nimport divWithClassName from './divWithClassName';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst DivStyledAsH6 = divWithClassName('h6');\nconst CardSubtitle = /*#__PURE__*/React.forwardRef(({\n  className,\n  bsPrefix,\n  as: Component = DivStyledAsH6,\n  ...props\n}, ref) => {\n  bsPrefix = useBootstrapPrefix(bsPrefix, 'card-subtitle');\n  return /*#__PURE__*/_jsx(Component, {\n    ref: ref,\n    className: classNames(className, bsPrefix),\n    ...props\n  });\n});\nCardSubtitle.displayName = 'CardSubtitle';\nexport default CardSubtitle;", "\"use client\";\n\nimport * as React from 'react';\nimport classNames from 'classnames';\nimport { useBootstrapPrefix } from './ThemeProvider';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst CardText = /*#__PURE__*/React.forwardRef(({\n  className,\n  bsPrefix,\n  as: Component = 'p',\n  ...props\n}, ref) => {\n  bsPrefix = useBootstrapPrefix(bsPrefix, 'card-text');\n  return /*#__PURE__*/_jsx(Component, {\n    ref: ref,\n    className: classNames(className, bsPrefix),\n    ...props\n  });\n});\nCardText.displayName = 'CardText';\nexport default CardText;", "\"use client\";\n\nimport * as React from 'react';\nimport classNames from 'classnames';\nimport { useBootstrapPrefix } from './ThemeProvider';\nimport divWithClassName from './divWithClassName';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst DivStyledAsH5 = divWithClassName('h5');\nconst CardTitle = /*#__PURE__*/React.forwardRef(({\n  className,\n  bsPrefix,\n  as: Component = DivStyledAsH5,\n  ...props\n}, ref) => {\n  bsPrefix = useBootstrapPrefix(bsPrefix, 'card-title');\n  return /*#__PURE__*/_jsx(Component, {\n    ref: ref,\n    className: classNames(className, bsPrefix),\n    ...props\n  });\n});\nCardTitle.displayName = 'CardTitle';\nexport default CardTitle;", "\"use client\";\n\nimport classNames from 'classnames';\nimport * as React from 'react';\nimport { useBootstrapPrefix } from './ThemeProvider';\nimport CardBody from './CardBody';\nimport CardFooter from './CardFooter';\nimport CardHeader from './CardHeader';\nimport CardImg from './CardImg';\nimport CardImgOverlay from './CardImgOverlay';\nimport CardLink from './CardLink';\nimport CardSubtitle from './CardSubtitle';\nimport CardText from './CardText';\nimport CardTitle from './CardTitle';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst Card = /*#__PURE__*/React.forwardRef(({\n  bsPrefix,\n  className,\n  bg,\n  text,\n  border,\n  body = false,\n  children,\n  // Need to define the default \"as\" during prop destructuring to be compatible with styled-components github.com/react-bootstrap/react-bootstrap/issues/3595\n  as: Component = 'div',\n  ...props\n}, ref) => {\n  const prefix = useBootstrapPrefix(bsPrefix, 'card');\n  return /*#__PURE__*/_jsx(Component, {\n    ref: ref,\n    ...props,\n    className: classNames(className, prefix, bg && `bg-${bg}`, text && `text-${text}`, border && `border-${border}`),\n    children: body ? /*#__PURE__*/_jsx(CardBody, {\n      children: children\n    }) : children\n  });\n});\nCard.displayName = 'Card';\nexport default Object.assign(Card, {\n  Img: CardImg,\n  Title: CardTitle,\n  Subtitle: CardSubtitle,\n  Body: CardBody,\n  Link: CardLink,\n  Text: CardText,\n  Header: CardHeader,\n  Footer: CardFooter,\n  ImgOverlay: CardImgOverlay\n});", "//Import Library\r\nimport React, { useState } from \"react\";\r\nimport { faMinus, faPlus } from \"@fortawesome/free-solid-svg-icons\";\r\nimport { FontAwesomeIcon } from \"@fortawesome/react-fontawesome\";\r\nimport moment from \"moment\";\r\nimport { Accordion, Card } from \"react-bootstrap\";\r\n\r\n//Import services/components\r\nimport { useTranslation } from 'next-i18next';\r\n\r\n\r\nconst ProjectDetailsAccordion = (props: any) => {\r\n    const formatDate = \"DD-MM-YYYY HH:mm:ss\";\r\n    const formatDateStartDate = \"DD-MM-YYYY\";\r\n    const { t } = useTranslation('common');\r\n    const [section, setSection] = useState(true);\r\n    return (\r\n        <>\r\n                <Accordion.Item eventKey=\"0\">\r\n                    <Accordion.Header onClick={() => setSection(!section)}>\r\n                        <div className=\"cardTitle\">{t(\"ProjectDetails\")}</div>\r\n                        <div className=\"cardArrow\">\r\n                            {section ? <FontAwesomeIcon icon={faPlus} color=\"#fff\" /> :\r\n                                <FontAwesomeIcon icon={faMinus} color=\"#fff\" />}\r\n                        </div>\r\n                    </Accordion.Header>\r\n                    <Accordion.Body>\r\n                            <Card.Text className=\"projectDetails ps-0\">\r\n                                <p><b>{t(\"Status\")}</b>:<span> {props.project.status ? props.project.status[\"title\"] : \"\"}</span></p>\r\n                                <p><b>{t(\"Created\")}</b>:<span> {props.project.created_at ? (moment(props.project.created_at).format(formatDate)) : null} </span></p>\r\n                                <p><b>{t(\"EndDate\")}</b>:<span> {props.project.end_date ? (moment(props.project.end_date).format(formatDateStartDate)) : null}</span></p>\r\n                                <p><b>{t(\"LastModified\")}</b>:<span> {props.project.updated_at ? (moment(props.project.updated_at).format(formatDate)) : null} </span></p>\r\n                                <p><b>{t(\"WeblinktoProject\")}</b>:\r\n\t\t\t\t\t\t\t\t<span>\r\n\t\t\t\t\t\t\t\t\t{props.project?.website && (\r\n\t\t\t\t\t\t\t\t\t\t<a href={props.project.website} target={\"_blank\"}>\r\n\t\t\t\t\t\t\t\t\t\t{props.project.website}\r\n\t\t\t\t\t\t\t\t\t\t</a>\r\n\t\t\t\t\t\t\t\t\t)}\r\n\t\t\t\t\t\t\t\t</span>\r\n                                </p>\r\n                            </Card.Text>\r\n                    </Accordion.Body>\r\n                </Accordion.Item>\r\n        </>\r\n    )\r\n}\r\n\r\nexport default ProjectDetailsAccordion;", "\n    (window.__NEXT_P = window.__NEXT_P || []).push([\n      \"/project/components/ProjectDetailsAccordion\",\n      function () {\n        return require(\"private-next-pages/project/components/ProjectDetailsAccordion.tsx\");\n      }\n    ]);\n    if(module.hot) {\n      module.hot.dispose(function () {\n        window.__NEXT_P.push([\"/project/components/ProjectDetailsAccordion\"])\n      });\n    }\n  ", "\"use client\";\n\nimport * as React from 'react';\nconst context = /*#__PURE__*/React.createContext(null);\ncontext.displayName = 'CardHeaderContext';\nexport default context;"], "names": ["CardBody", "React", "ref", "className", "bsPrefix", "as", "Component", "props", "useBootstrapPrefix", "_jsx", "classNames", "<PERSON><PERSON><PERSON>er", "displayName", "<PERSON><PERSON><PERSON><PERSON>", "prefix", "contextValue", "useMemo", "cardHeaderBsPrefix", "CardHeaderContext", "Provider", "value", "children", "CardImg", "variant", "CardImgOverlay", "CardLink", "DivStyledAsH6", "divWithClassName", "CardSubtitle", "CardText", "DivStyledAsH5", "CardTitle", "Card", "bg", "text", "border", "body", "Object", "assign", "Img", "Title", "Subtitle", "Body", "Link", "Text", "Header", "Footer", "ImgOverlay", "formatDate", "ProjectDetailsAccordion", "t", "useTranslation", "section", "setSection", "useState", "Accordion", "<PERSON><PERSON>", "eventKey", "onClick", "div", "FontAwesomeIcon", "icon", "faPlus", "color", "faMinus", "p", "b", "span", "project", "status", "created_at", "moment", "format", "end_date", "updated_at", "website", "a", "href", "target", "context"], "sourceRoot": "", "ignoreList": [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 12]}